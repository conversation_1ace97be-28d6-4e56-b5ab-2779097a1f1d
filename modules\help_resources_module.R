# --- 1. 加载必要的库 ---
# 假设主要库已在app.R中加载
# library(shiny)

# --- 2. 定义模块UI ---
help_resources_module_ui <- function(id) {
  ns <- NS(id)
  
  sidebarLayout(
    position = 'right',
    sidebarPanel(
      h5(tags$u('Resources')),
      div(title = "Wiki page for the calculator",
          a("About WPRO all-cause-of-mortality and excess death calculator",
            href = "https://github.com/WorldHealthOrganization/ACMCalculator/wiki",
            target = "_blank")),
      div(title="WPRO all-cause mortality dashboard",
          a("WPRO all-cause mortality dashboard",
            href="https://lynx.wpro.who.int/viz/allcausedeath.asp",
            target="_blank")
      ),
      div(title=paste("Information on the methodology used",
                      "in the calculator"),
          a("About the methodology used in the tool.",
            href = "https://github.com/WorldHealthOrganization/ACMCalculator/wiki/Methodology-used-in-ACMCalculator/", target = "_blank")
      ),
      br(),
      div(a("WPRO all-cause-of-mortality and excess death calculator on GitHub", href="https://github.com/WorldHealthOrganization/ACMCalculator",
            target="_blank")),
      div(a("Shiny: a web application framework for R", href="http://shiny.rstudio.com/",
            target="_blank"))
    ),
    mainPanel(
      h5(tags$u('Help with the WPRO all-cause-of-mortality and excess death calculator')),
      p("This app is maintained on GitHub. To request new features or report a bug,",
        "please interact with the",
        a("repository", href='https://github.com/WorldHealthOrganization/ACMCalculator',
          target="_blank"),
        "or email us at", 
        a(actionButton(inputId = ns("email1"), label = "<EMAIL>",
                       icon = icon("envelope", lib = "font-awesome")),
          href="mailto:<EMAIL>"),
        a(actionButton(inputId = ns("email2"), label = "<EMAIL>", 
                       icon = icon("envelope", lib = "font-awesome")),
          href="mailto:<EMAIL>")),
      
      h5(tags$u('How to Install the app off-line')),
      p("This app can be installed to run entirely on your local machine. Running",
        "the app locally is the same as using this online version, except",
        "that no internet connection is necessary and your data does not have to",
        "be uploaded to an external location.",
        "To install this app locally for off-line use, follow these simple steps."),
      
      p("1. Open the",strong("RGui")," app (You can search for it by name)."), 
      p("Note: This is not the Rstudio app, which should not be used."),
      p("2. In the",strong("R Console"), "window, cut-and-paste the following command:"),
      p('source("https://faculty.stat.ucla.edu/handcock/ACMCalculator.R")'),
      p("and hit Enter."),
      p("3. The app will install itself and close RGui. This may take a few minutes. Once R is closed",
        "go to your Documents directory and find the file named",
        strong("ACMCalculator"),"."),
      p("4. Double-click on the file, and the app will open within your default browser.",
        "Note that although a web browser is being used to display the app,",
        "no information is being sent over the internet and indeed",
        "the app is operating offline. There may be a delay of a few seconds before the app appears.",
        "If the app fails to open the",
        "first time, simply close the browser and double-click the",
        strong("ACMCalculator"), " file again."),
      p("4. In the future, when you want to run the app, just double-click on the ",strong("ACMCalculator")," file and the app will open within your default browser."),

      p(),
      h5(tags$u('ACM Excess Mortality Model User Guide')),
      h6(tags$u('1. Introduction')),
      p("This guide is intended to help users correctly use the 'Historical Average', 'Negative Binomial Regression' and other models within the ACM calculator to accurately analyze and estimate excess mortality. Correct data input and parameter settings are crucial for obtaining reliable results."),
      p("All the models (Historical Average, Negative Binomial Regression, etc.) rely on historical data to predict 'expected' deaths, which are then compared to 'actual' reported deaths during a specific event period to calculate excess mortality."),

      h6(tags$u('2. Basic Model Assumptions')),
      p("The design of the models is based on the following core assumptions:"),
      tags$ul(
        tags$li(strong("Event Impact"), ": Specific public health events (such as COVID-19, H1N1 influenza pandemic, etc.) may lead to a significant increase in the number of deaths during a certain period."),
        tags$li(strong("Intervention Impact"), ": Public health interventions (such as restrictions on population movement, mandatory mask-wearing, mass vaccinations, etc.) may lead to a decrease in population mortality."),
        tags$li(strong("Baseline Prediction"), ": The models are trained using 'normal' mortality data from periods without special epidemic events or large-scale interventions (i.e., historical baseline data) to obtain model parameters. Based on these parameters, the models can estimate the 'expected' number of deaths during a specific event period (e.g., during an epidemic or when public health measures are implemented). Excess mortality is calculated by comparing the 'expected' number of deaths with the 'actually reported' deaths for that period.")
      ),

      h6(tags$u('3. Key Considerations and User Guidelines')),
      p("To ensure the accuracy of the model analysis and the reliability of the results, users must pay close attention to the following points:"),

      h6(tags$u('3.1. Data Input and Event Period Consistency (Important)')),
      p(strong("Core Issue"), ": Correctly defining and inputting the historical data range for model training and defining the event period are crucial."),
      p(strong("User Feedback Highlight"), ":"),
      p("A user reported that when calculating excess mortality up to August 2023, including data up to December 2023 (i.e., beyond the intended end point of the event period being analyzed) led to an underestimation of excess mortality."),
      p("This is likely because the model might misinterpret data points occurring after the defined event period, and not explicitly marked as an 'event,' as 'historical normal data.' This, in turn, affects the training of the baseline model and its parameter estimation, thereby impacting the prediction of expected deaths for the specified event period."),
      p(strong("Operational Guidance and Recommendations"), ":"),
      tags$ul(
        tags$li(
          strong("Clearly Define the Event Period"), ":",
          tags$ul(
            tags$li("Before starting the analysis, the user must ", strong("precisely define"), " the start and end times of the 'event period' you wish to analyze (e.g., Jan 1, 2020, to August 31, 2023).")
          )
        ),
        tags$li(
          strong("Data Preparation and Alignment"), ":",
          tags$ul(
            tags$li(strong("Training Data (Historical Data for Training)"), ": The dataset used for model training (typically corresponding to ", code("event_index == '0'"), " in the intermediate data frame used by the R model scripts) ", strong("should not include"), " data from the 'event period' you are currently analyzing. It also ", strong("should not include"), " data from periods immediately following the event period where mortality rates may not have yet returned to 'normal' levels, unless you have a clear justification that this data represents a new baseline. The user could exclude the abnormal data from the training set by defining an event with start and end date."),
          )
        ),
        tags$li(
          strong("Understanding the ", code("event_index"), " (Automatically Set)"), ":",
          tags$ul(
            tags$li("The model implementation (as seen in the R scripts) relies on a column named ", code("event_index"), " (or similar) to distinguish between historical data and event period data."),
            tags$li(
              code("event_index == '0'"), " typically denotes historical 'normal' data used for training the model."
            ),
            tags$li(
              code("event_index != '0'"), " (e.g., '1') denotes the event period for which the model needs to predict expected deaths."
            ),
            tags$li(
              strong("Important Clarification"), ": Users do not directly define or modify the ", code("event_index"), " column themselves. The ACM calculator ", strong("automatically sets the ", code("event_index")), " based on the event start and end dates that the user defines in the calculator's interface. Therefore, the accuracy of the ", code("event_index"), " is contingent upon the user correctly specifying the event period dates. An incorrect event period definition by the user will lead to an incorrect automatic assignment of ", code("event_index"), " values, which will subsequently result in flawed analysis.")
            )
          )
        ),

      h6(tags$u('3.2. Impact of Event Period Definition on Results')),
      tags$ul(
        tags$li(
          strong("Different Definitions, Different Results"), ": If the user defines the start and end times for the same event differently, it means the model will use different datasets for training, and the prediction period will also differ. This will directly lead to variations in the calculated excess mortality statistics."
        ),
        tags$li(
          strong("Recommendation"), ": Clearly and consistently define the event period at the beginning of the project or analysis, and maintain this definition throughout all related analyses unless there is a compelling reason for adjustment."
        )
      ),

      h6(tags$u('3.3. Impact of Including Post-Event Data in the Training Set')),
      tags$ul(
        tags$li(
          strong("Affects Model Parameters"), ": Whether to include data from ", strong("after"), " an event period (i.e., post-epidemic period data) in the training dataset significantly impacts the estimation of model parameters."
        ),
        tags$li(
          strong("Consideration of Trend Changes"), ": This is particularly important when there are inconsistencies in mortality trends before and after an epidemic. For example, post-epidemic mortality rates might:",
          tags$ul(
            tags$li("Return to pre-epidemic levels."),
            tags$li("Remain persistently higher than pre-epidemic levels."),
            tags$li("Fall below pre-epidemic levels (possibly due to a 'harvesting effect' or behavioral changes)."),
            tags$li("Experience accelerated or decelerated trend changes.")
          )
        ),
        tags$li(
          strong("Impact on Event Period Estimation"), ": Including post-event data with significantly altered trends in the training set to estimate excess mortality for a ", em("past"), " event period can distort the judgment of the true baseline for that event period, thereby affecting the accuracy of the excess mortality estimation."
        ),
        tags$li(
          strong("Recommendations"), ":",
          tags$ul(
            tags$li("When analyzing excess mortality for a specific historical event (e.g., the early stages of COVID-19 in 2020), it is generally ", strong("not recommended"), " to include data from after that event (e.g., 2022, 2023) in the training baseline used to estimate mortality for that earlier event, unless there is very clear evidence that this later data has fully returned to normal and is consistent with the earlier baseline trend."),
            tags$li("If there is a need to analyze overall trends that include post-event periods or to establish a new baseline, the nature of the post-event data should be carefully evaluated, and it may be necessary to treat the post-event period as a new, independent phase of analysis.")
          )
        )
      ),

      h6(tags$u('4. Conclusion')),
      p("Accurately estimating excess mortality requires careful data preparation and an understanding of the model's assumptions. By following the guidelines above, particularly the recommendations regarding data input and event period consistency, users can improve the accuracy and reliability of the ACM calculator's analysis results."),
      p("If you have further questions, please refer to the model documentation or contact technical support.")
    )
  )
}

# --- 3. 定义模块服务器 ---
help_resources_module_server <- function(id, rv) {
  moduleServer(id, function(input, output, session) {
    # 邮件按钮的观察器
    observeEvent(input$email1, {
      # 这里不需要做任何事情，因为邮件链接是通过href属性处理的
    })
    
    observeEvent(input$email2, {
      # 这里不需要做任何事情，因为邮件链接是通过href属性处理的
    })
  })
}