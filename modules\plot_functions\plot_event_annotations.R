# 事件标注函数
# 负责绘制事件标注

# 加载必要的库
library(ggplot2)
library(dplyr)

# 添加事件标注
add_event_annotations <- function(p, event_data, y_min, y_max) {
  if (is.null(event_data) || nrow(event_data) == 0) {
    return(p)
  }
  
  # 获取事件颜色
  event_colors <- attr(event_data, "event_colors")
  if (is.null(event_colors)) {
    event_colors <- RColorBrewer::brewer.pal(min(nrow(event_data), 9), "Set3")
    names(event_colors) <- unique(event_data$event_name)
  }
  
  # 为每个事件添加标注
  for (i in 1:nrow(event_data)) {
    event <- event_data[i, ]
    color <- event_colors[event$event_name]
    
    # 添加开始日期垂直线
    p <- p + geom_vline(
      xintercept = event$start_time,
      color = color,
      alpha = 0.5,
      linetype = "solid",
      linewidth = 1
    )
    
    # 添加结束日期垂直线
    p <- p + geom_vline(
      xintercept = event$end_time,
      color = color,
      alpha = 0.5,
      linetype = "solid",
      linewidth = 1
    )
    
    # 添加水平线和箭头
    p <- p + annotate(
      "segment",
      x = event$start_time,
      xend = event$end_time,
      y = y_min + (y_max - y_min) * 0.1, # 在y轴的10%位置
      yend = y_min + (y_max - y_min) * 0.1,
      color = color,
      alpha = 0.7,
      linewidth = 0.8,
      arrow = arrow(ends = "both", type = "open", length = unit(0.1, "inches"))
    )
    
    # 添加事件标签
    p <- p + geom_text(
      data = data.frame(
        x = event$start_time + (event$end_time - event$start_time) / 2,
        y = y_min + (y_max - y_min) * 0.15,
        label = event$event_name
      ),
      aes(x = x, y = y, label = label),
      hjust = 0.5,
      vjust = -0.5,
      size = 3,
      color = "darkred",
      inherit.aes = FALSE
    )
  }
  
  return(p)
}
