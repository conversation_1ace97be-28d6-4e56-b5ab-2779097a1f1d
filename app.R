# app.R
# library(shiny)
library(DT)
library(openxlsx) # For reading Excel files
library(lubridate) # For date processing
library(dplyr) # For data operations (like distinct and left_join)
library(ggplot2) # For plotting
library(scales) # For formatting axis labels
library(plotly) # For interactive plots
library(ggrepel) # For labeling points in plots
library(readxl) # For reading Excel files
library(shinyjs) # 添加shinyjs库，用于about模块
library(RColorBrewer) # 添加RColorBrewer库，用于事件块的颜色

# 设置工作目录和数据目录
app_dir <- getwd()
data_dir <- file.path(app_dir, "data")

# 检查数据目录是否存在，如果不存在则创建
if (!dir.exists(data_dir)) {
  dir.create(data_dir, recursive = TRUE)
  message("创建数据目录: ", data_dir)
}

# 加载全局设置
# 清除环境中的所有函数缓存
rm(list = lsf.str())

# 注意：已将 date_midpoint 函数移动到 plot_prediction_module.R 文件中

source("modules/utils.R", encoding = "UTF-8")
source("modules/ACM_hist_new.R", encoding = "UTF-8")
source("modules/ACM_his_tr.R", encoding = "UTF-8")
source("modules/ACM_nb.R", encoding = "UTF-8")
source("modules/ACM_quasipoisson.R", encoding = "UTF-8")
source("modules/ACM_zip.R", encoding = "UTF-8")
source("modules/data_process_module.R", encoding = "UTF-8")
source("modules/model_run_module.R", encoding = "UTF-8")
source("modules/model_summary_module.R", encoding = "UTF-8")
source("modules/report_module.R", encoding = "UTF-8")

# 创建一个动态加载模块的函数
load_module <- function(module_name) {
  # 检查模块是否已经加载
  if (module_name == "plot_prediction" && !exists("plot_prediction_module_ui", mode = "function")) {
    # 先删除相关函数以避免冲突
    rm(list = ls(pattern = "create_.*_plot"), envir = .GlobalEnv)
    rm(list = ls(pattern = "plot_prediction_module"), envir = .GlobalEnv)
    rm(list = ls(pattern = "date_midpoint"), envir = .GlobalEnv)
    # 加载预测结果绘图模块
    source("modules/plot_prediction_module_new.R", encoding = "UTF-8")
    message("Loaded plot_prediction_module_new.R")
    return(TRUE)
  } else if (module_name == "plot_event" && !exists("plot_event_module_ui", mode = "function")) {
    # 加载事件绘图模块
    source("modules/plot_event_module_new.R", encoding = "UTF-8")
    message("Loaded plot_event_module_new.R")
    return(TRUE)
  } else if (module_name == "plot_total" && !exists("plot_total_module_ui", mode = "function")) {
    # 加载总体绘图模块
    source("modules/plot_total_module.R", encoding = "UTF-8")
    message("Loaded plot_total_module.R")
    return(TRUE)
  }
  return(FALSE)
}

# 加载必要的非绘图模块
source("modules/about_module.R", encoding = "UTF-8") # 添加about模块
source("modules/methods_module.R", encoding = "UTF-8") # 添加methods模块
source("modules/help_resources_module.R", encoding = "UTF-8") # 添加help_resources模块

# 修改主UI以匹配图片中的导航栏样式，并将About设为首页
ui <- navbarPage(
  # title = "WPRO all-cause-of-mortality and excess death calculator",
  title = "",
  id = "navbar",
  windowTitle = "WPRO all-cause-of-mortality and excess death calculator",
  collapsible = TRUE,

  # 添加JavaScript处理导航请求
  header = tags$head(
    tags$script(HTML("
      Shiny.addCustomMessageHandler('navigateTab', function(tabName) {
        $('a[data-value=\"' + tabName + '\"]').click();
      });

      // 添加事件监听器来处理模块内部的标签页切换
      $(document).on('change', '.shiny-bound-input', function() {
        // 如果这是一个标签页选择器
        if (this.id.indexOf('tabsetpanel') !== -1) {
          // 触发一个事件来通知Shiny标签页已经改变
          Shiny.setInputValue(this.id + '_changed', new Date().getTime());
        }
      });
    "))
  ),

  # 添加About模块作为首页
  tabPanel("WPRO all-cause-of-mortality and excess death calculator", about_module_ui("about_mod"), value = "tab1"),
  tabPanel(
    "Data",
    useShinyjs(),
    data_process_module_ui("data_process_mod")
  ),
  tabPanel("Model", model_run_module_ui("model_run_mod"), value = "tab3"),
  tabPanel("Tables", model_summary_module_ui("model_summary_mod"), value = "tab4"),
  tabPanel("Plots",
    value = "tab6",
    fluidRow(
      column(
        12,
        # 添加条件面板进行数据检查
        conditionalPanel(
          condition = "!output.has_merged_data",
          div(
            style = "text-align: center; margin-top: 50px;",
            h3("Data is not ready. Please process data first...", style = "color: #666;"),
            actionButton("goto_data_tab", "Go to Data tab",
              style = "margin-top: 20px; background-color: #337ab7; color: white;")
          )
        ),
        conditionalPanel(
          condition = "output.has_merged_data && !output.has_prediction_data",
          div(
            style = "text-align: center; margin-top: 50px;",
            h3("Expected Death Data is not ready. Please run model first...", style = "color: #666;"),
            actionButton("goto_model_tab", "Go to Model tab",
              style = "margin-top: 20px; background-color: #337ab7; color: white;")
          )
        ),
        conditionalPanel(
          condition = "output.has_merged_data && output.has_prediction_data",
          tabsetPanel(
            id = "plottabs",
            tabPanel(
              "Model prediction results", br(),
              uiOutput("plot_prediction_ui")
            ),
            tabPanel(
              "Event-specific excess mortality", br(),
              uiOutput("plot_event_ui"),
            ),
            tabPanel(
              "Total excess mortality", br(),
              uiOutput("plot_total_ui")
            )
          )
        )
      )
    )
  ),
  tabPanel("Report", report_module_ui("report_mod"), value = "tab5"),
  tabPanel(
    "Methods",
    methods_module_ui("methods_mod") # 添加Methods模块
  ),
  tabPanel(
    "Help and Resources",
    help_resources_module_ui("help_resources_mod") # 添加Help and Resources模块
  ),
)

# 加载 Server 定义
server <- function(input, output, session) {
  options(digits = 3)

  # Initialize reactive values shared across modules
  rv <- reactiveValues(
    acm_data = NULL,
    acm_rawdata = NULL,
    acm_temp = NULL,
    data_description = NULL,
    merged_data = NULL,
    event_data = NULL,
    sheet_name = NULL,
    merged_data = NULL,
    merged_data_ext = NULL,
    total_prediction = NULL,
    plot_time_data = NULL,
    summary_by_event = NULL,
    overall_summary = NULL
  )

  # 调用About模块服务器
  about_module_server("about_mod", rv)

  # 调用数据处理模块服务器
  data_process_module_server("data_process_mod", rv)

  # 使用 rv$merged_data
  observe({
    if (!is.null(rv$merged_data)) {
      message("Merged data is ready.")
      # openxlsx::write.xlsx(rv$merged_data, file = "merged_data.xlsx", rowNames = FALSE)
      # 直接使用 rv$merged_data，不要加括号
      rv_data <- rv$merged_data
      rv_data$CAUSE <- "Total"
      rv_data$AREA <- "Total"
      rv_data$COUNTRY <- "AAA"
      rv_data$ISO3 <- "ISO"
      rv_data$SE_IDENTIFIER <- "END"

      # 1. Extract unique combinations of YEAR and PERIOD
      unique_combinations <- rv_data %>%
        filter(PERIOD <= 52) %>%
        select(YEAR, PERIOD) %>%
        distinct()

      # Save current locale setting
      original_locale <- Sys.getlocale("LC_TIME")
      # Change locale setting to English
      Sys.setlocale("LC_TIME", "en_US.UTF-8")

      # 2. Batch calculate DATE_TO_SPECIFY_WEEK
      unique_combinations$DATE_TO_SPECIFY_WEEK <- apply(unique_combinations, 1, function(row) {
        target_date <- get_week_start_date(as.numeric(row["YEAR"]), as.numeric(row["PERIOD"]))
        format(target_date, "%b-%d")
      })

      # Restore original locale setting
      Sys.setlocale("LC_TIME", original_locale)

      # 3. Merge results back to original dataframe
      rv_data <- rv_data %>%
        filter(PERIOD <= 52) %>%
        left_join(unique_combinations, by = c("YEAR", "PERIOD"))

      rv$merged_data_ext <- rv_data
      # saveRDS(rv_data, "merged_data_ext.rds")
      # openxlsx::write.xlsx(rv_data, file = "merged_data_ext.xlsx", rowNames = FALSE)
    }
  })

  # Call the model run module server
  # 修改 model_run_module_server 的调用方式
  model_results <- model_run_module_server("model_run_mod", rv)

  # 添加观察器来处理模型运行结果
  observe({
    req(model_results$model_results())
    results <- model_results$model_results()

    # 检查模型运行结果并更新UI
    if (!is.null(results)) {
      message("Model results updated successfully")
    }
  })

  # Call the model summary module server
  model_summary_module_server("model_summary_mod", rv)

  # Call the report module server
  report_module_server("report_mod", rv)

  # 动态加载绘图模块
  # 创建模块加载状态跟踪
  module_loaded <- reactiveValues(
    plot_prediction = FALSE,
    plot_event = FALSE,
    plot_total = FALSE
  )

  # 预测结果模块
  output$plot_prediction_ui <- renderUI({
    # 加载模块
    load_module("plot_prediction")
    # 标记模块已加载
    module_loaded$plot_prediction <- TRUE
    # 返回UI
    plot_prediction_module_ui("plot_prediction_mod")
  })

  # 初始化预测结果模块服务器
  observe({
    req(module_loaded$plot_prediction)
    # 确保模块已加载
    load_module("plot_prediction")
    # 初始化服务器
    plot_prediction_module_server("plot_prediction_mod", rv)
  })

  # 事件特定模块
  output$plot_event_ui <- renderUI({
    # 加载模块
    load_module("plot_event")
    # 标记模块已加载
    module_loaded$plot_event <- TRUE
    # 返回UI
    plot_event_module_ui("plot_event_mod")
  })

  # 初始化事件特定模块服务器
  observe({
    req(module_loaded$plot_event)
    # 确保模块已加载
    load_module("plot_event")
    # 初始化服务器
    plot_event_module_server("plot_event_mod", rv)
  })

  # 总体模块
  output$plot_total_ui <- renderUI({
    # 加载模块
    load_module("plot_total")
    # 标记模块已加载
    module_loaded$plot_total <- TRUE
    # 返回UI
    plot_total_module_ui("plot_total_mod")
  })

  # 初始化总体模块服务器
  observe({
    req(module_loaded$plot_total)
    # 确保模块已加载
    load_module("plot_total")
    # 初始化服务器
    plot_total_module_server("plot_total_mod", rv)
  })

  # 监听标签页切换，预加载模块
  observeEvent(input$plottabs, {
    if (input$plottabs == "Model prediction results") {
      load_module("plot_prediction")
    } else if (input$plottabs == "Event-specific excess mortality") {
      load_module("plot_event")
    } else if (input$plottabs == "Total excess mortality") {
      load_module("plot_total")
    }
  }, ignoreInit = FALSE)

  # 调用Methods模块服务器
  methods_module_server("methods_mod", rv)

  # 调用Help and Resources模块服务器
  help_resources_module_server("help_resources_mod", rv)

  # 添加数据状态检查
  output$has_merged_data <- reactive({
    !is.null(rv$merged_data)
  })
  outputOptions(output, "has_merged_data", suspendWhenHidden = FALSE)

  output$has_prediction_data <- reactive({
    !is.null(rv$total_prediction)
  })
  outputOptions(output, "has_prediction_data", suspendWhenHidden = FALSE)

  # 添加导航按钮处理器
  observeEvent(input$goto_data_tab, {
    updateNavbarPage(session, "navbar", selected = "Data")
  })

  observeEvent(input$goto_model_tab, {
    # 确保与tabPanel中的标签名称完全匹配
    updateNavbarPage(session, "navbar", selected = "Model")
    session$sendCustomMessage("navigateTab", "tab3")
  })
}

# 运行 Shiny 应用
shinyApp(ui = ui, server = server)
