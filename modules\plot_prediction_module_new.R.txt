# 预测结果绘图模块
# 负责绘制预测结果图表

# 加载必要的库
library(shiny)
library(ggplot2)
library(dplyr)
library(lubridate)
library(plotly)
library(RColorBrewer)

# 加载绘图函数
source("modules/plot_functions/plot_excess_deaths.R")
source("modules/plot_functions/plot_p_score.R")

# 确保加载必要的库
library(plotly)
library(RColorBrewer)
library(patchwork) # 用于图层叠加

# 安全计算两个日期的中点
date_midpoint <- function(date1, date2) {
  if (inherits(date1, "Date") || inherits(date1, "POSIXt")) {
    date1_num <- as.numeric(date1)
  } else {
    date1_num <- date1
  }

  if (inherits(date2, "Date") || inherits(date2, "POSIXt")) {
    date2_num <- as.numeric(date2)
  } else {
    date2_num <- date2
  }

  mid_num <- (date1_num + date2_num) / 2

  if (inherits(date1, "Date")) {
    return(as.Date(mid_num, origin = "1970-01-01"))
  } else if (inherits(date1, "POSIXt")) {
    return(as.POSIXct(mid_num, origin = "1970-01-01"))
  } else {
    return(mid_num)
  }
}

# 添加区域设置为英文，确保月份标签为英文
# 保存当前区域设置
original_locale <- Sys.getlocale("LC_TIME")
# 设置为英文区域
# Sys.setlocale("LC_TIME", "English")

# --- 2. 定义模块UI ---
plot_prediction_module_ui <- function(id) {
  ns <- NS(id)
  fluidPage(
    # titlePanel("Mortality Data Visualization"),
    sidebarLayout(
      sidebarPanel(
        width = 3,
        h4("Data Filtering"),
        uiOutput(ns("sex_selector")),
        uiOutput(ns("age_group_selector")),
        h4("Model Selection"),
        uiOutput(ns("model_selector")),
        hr(),
        # 时间范围选择
        h4("Time Range Selection"),
        uiOutput(ns("time_slider")),
        hr(),
        h4("Download Plots (PDF)"),
        downloadButton(ns("downloadACMPlot"), "Download ACM Plot"),
        br(), br(),
        downloadButton(ns("downloadEDPlot"), "Download Excess Deaths Plot"),
        br(), br(),
        downloadButton(ns("downloadEPPlot"), "Download P-Score Plot")
      ),
      mainPanel(
        width = 9,
        tabsetPanel(
          id = ns("plotTabs"),
          tabPanel(
            "All Cause Mortality",
            br(),
            h4("All Cause Mortality (Recorded vs. Expected)"),
            # 将plotOutput替换为plotlyOutput
            plotlyOutput(ns("ACMplot"), height = "600px")
          ),
          tabPanel(
            "Excess Deaths",
            br(),
            tabsetPanel(
              id = ns("edTabs"),
              tabPanel(
                "Bar Plot",
                br(),
                h4("Excess Deaths"),
                # 将plotOutput替换为plotlyOutput
                plotlyOutput(ns("EDplot"), height = "600px")
              ),
              tabPanel(
                "Line Plot",
                br(),
                h4("Excess Deaths (Line Plot)"),
                # 将plotOutput替换为plotlyOutput
                plotlyOutput(ns("EDLinePlot"), height = "600px")
              )
            )
          ),
          tabPanel(
            "P-Score",
            br(),
            tabsetPanel(
              id = ns("psTabs"),
              tabPanel(
                "Bar Plot",
                br(),
                h4("P-Score of Excess Deaths (%)"),
                # 将plotOutput替换为plotlyOutput
                plotlyOutput(ns("EPplot"), height = "600px")
              ),
              tabPanel(
                "Line Plot",
                br(),
                h4("P-Score (Line Plot)"),
                # 将plotOutput替换为plotlyOutput
                plotlyOutput(ns("EPLinePlot"), height = "600px")
              )
            )
          )
        )
      )
    )
  )
}

# --- 3. 定义模块服务器 ---
plot_prediction_module_server <- function(id, rv) {
  moduleServer(id, function(input, output, session) {
    # 使用命名空间
    ns <- session$ns

    # 动态生成UI选择器
    output$sex_selector <- renderUI({
      req(rv$total_prediction)
      unique_sex <- sort(unique(rv$total_prediction$SEX), na.last = TRUE, decreasing = TRUE)
      selectInput(ns("selected_sex"), "Select Sex:",
        choices = unique_sex,
        selected = if (length(unique_sex) > 0) unique_sex[1] else NULL,
        multiple = FALSE
      )
    })

    output$age_group_selector <- renderUI({
      req(rv$total_prediction)
      unique_age_group <- sort(unique(rv$total_prediction$AGE_GROUP), na.last = TRUE, decreasing = TRUE)
      selectInput(ns("selected_age_group"), "Select Age Group:",
        choices = unique_age_group,
        selected = if (length(unique_age_group) > 0) unique_age_group[1] else NULL,
        multiple = FALSE
      )
    })

    output$model_selector <- renderUI({
      req(rv$total_prediction)
      unique_models <- sort(unique(rv$total_prediction$Model))
      # 默认选择逻辑：如果包含Negative Binomial Regression，则选择它，否则选择第一个模型
      default_selected <- if ("Negative Binomial Regression" %in% unique_models) {
        "Negative Binomial Regression"
      } else if (length(unique_models) > 0) {
        unique_models[1]
      } else {
        NULL
      }

      # 打印调试信息
      message("[DEBUG] Available models in prediction module: ", paste(unique_models, collapse = ", "))
      message("[DEBUG] Default selected model in prediction module: ", default_selected)

      checkboxGroupInput(ns("selected_models"), "Select Models to Display:",
        choices = unique_models,
        selected = default_selected
      )
      # unique_models <- sort(unique(rv$total_prediction$Model))
      # checkboxGroupInput(ns("selected_models"), "Select Models to Display:",
      #   choices = unique_models,
      #   selected = unique_models
      # )
    })

    # 创建动态时间滑动条
    output$time_slider <- renderUI({
      req(rv$total_prediction, input$selected_sex, input$selected_age_group, input$selected_models)

      data <- rv$total_prediction %>%
        filter(
          SEX == input$selected_sex,
          AGE_GROUP == input$selected_age_group,
          Model %in% input$selected_models
        )

      data_dates <- data %>%
        mutate(
          TimePoint = case_when(
            max(PERIOD, na.rm = TRUE) > 12 ~
              as.Date(paste(YEAR, 1, 1, sep = "-")) + weeks(PERIOD - 1) + days(3),
            TRUE ~ as.Date(paste(YEAR, PERIOD, 15, sep = "-"))
          )
        ) %>%
        pull(TimePoint)

      if (length(data_dates) == 0) {
        return(NULL)
      }

      date_range <- range(data_dates, na.rm = TRUE)

      sliderInput(ns("time_range"),
        "Select Time Period:",
        min = date_range[1],
        max = date_range[2],
        value = date_range,
        timeFormat = "%Y-%m",
        step = 7,
        width = "100%"
      )
    })

    # 过滤数据
    filtered_total_data <- reactive({
      req(
        rv$total_prediction, input$selected_sex,
        input$selected_age_group, input$selected_models,
        input$time_range
      )

      data <- rv$total_prediction %>%
        filter(
          SEX == input$selected_sex,
          AGE_GROUP == input$selected_age_group,
          Model %in% input$selected_models
        )

      max_period <- max(data$PERIOD, na.rm = TRUE)

      data %>%
        mutate(
          TimePoint = case_when(
            max_period > 12 ~
              as.Date(paste(YEAR, 1, 1, sep = "-")) + weeks(PERIOD - 1) + days(3),
            TRUE ~ as.Date(paste(YEAR, PERIOD, 15, sep = "-"))
          )
        ) %>%
        filter(
          TimePoint >= input$time_range[1],
          TimePoint <= input$time_range[2]
        ) %>%
        arrange(TimePoint)
    })

    # 添加时间格式化函数
    format_time_labels <- function(dates, data) {
      # 判断是否为周数据
      max_period <- max(data$PERIOD, na.rm = TRUE)
      if (max_period > 12) {
        # 周数据格式化
        sapply(dates, function(d) {
          year <- format(d, "%Y")
          week <- isoweek(d)
          sprintf("%s-%02d", year, week)
        })
      } else {
        # 月数据格式化
        format(dates, "%Y-%m")
      }
    }



    ################################################################
    # --- Render Plot Outputs ---
    # 修改渲染方法，避免ggplotly转换错误
    output$ACMplot <- renderPlotly({
      message("Rendering ACM plot...")

      tryCatch(
        {
          data <- filtered_total_data()
          if (is.null(data) || nrow(data) == 0) {
            message("No data available for ACM plot")
            return(plot_ly() %>%
              add_annotations(
                text = "No data available for selected filters.",
                x = 0.5, y = 0.5, showarrow = FALSE
              ))
          }

          # Create the base Plotly object
          p <- plot_ly(data, x = ~TimePoint) %>%
            add_trace(
              y = ~NO_DEATHS, name = "Recorded", type = "scatter", mode = "lines",
              line = list(color = "black", width = 2)
            )

          # Add expected deaths lines for each model
          # 定义一个更丰富的颜色调色板，确保每个模型有不同的颜色
          model_colors <- c(
            "Historical Average" = "#00CED1",                # 亮青色
            "Negative Binomial Regression" = "#FF0000",      # 鲜红色
            "Quasi-Poisson Model" = "#32CD32",            # 亮绿色
            "Zero Inflated Poisson Model" = "#9932CC",      # 深紫色
            "BSTS Model" = "#FFA500",                      # 亮橙色
            "State Space Model" = "#1E90FF",               # 亮蓝色
            "ARIMA/SARIMA Model" = "#8B4513"               # 深棕色
          )

          for (model_name in unique(data$Model)) {
            model_data <- data[data$Model == model_name, ]
            # 如果模型名称在预定义颜色中，使用它；否则使用默认颜色
            line_color <- if (model_name %in% names(model_colors)) {
              model_colors[model_name]
            } else {
              "#999999" # 默认灰色
            }
            line_dash <- if (model_name == "Historical Average") "dash" else "dot"

            p <- p %>% add_trace(
              data = model_data,
              y = ~EXP_DEATHS,
              name = paste(model_name, "Expected"),
              type = "scatter",
              mode = "lines",
              line = list(color = line_color, width = 1.5, dash = line_dash)
            )

            if ("LOWER_LIMIT" %in% names(model_data) && "UPPER_LIMIT" %in% names(model_data)) {
              p <- p %>% add_ribbons(
                data = model_data,
                y = ~EXP_DEATHS,
                ymin = ~LOWER_LIMIT,
                ymax = ~UPPER_LIMIT,
                name = paste(model_name, "95% CI"),
                line = list(color = "transparent"),
                fillcolor = adjustcolor(line_color, alpha.f = 0.2),
                showlegend = FALSE
              )
            }
          }

          # Calculate the maximum and minimum y-values for positioning and y-axis limits
          max_y <- max(data$NO_DEATHS, na.rm = TRUE)
          min_y <- min(data$NO_DEATHS, na.rm = TRUE)
          if ("UPPER_LIMIT" %in% names(data)) {
            max_y <- max(max_y, max(data$UPPER_LIMIT, na.rm = TRUE))
          }
          if ("LOWER_LIMIT" %in% names(data)) {
            min_y <- min(min_y, min(data$LOWER_LIMIT, na.rm = TRUE))
          }

          # Adjust y-axis limits to reduce whitespace
          y_min <- max(0, min_y * 0.95) # Start slightly below the minimum, but not below 0
          y_max <- max_y * 1.1 # Extend slightly above the maximum to accommodate labels

          # Add event markers as colored rectangles instead of vertical lines
          event_data <- data %>%
            filter(event_index != "0" & !is.na(event_index)) %>%
            filter(!is.na(event_name)) %>%
            group_by(event_index, event_name) %>%
            summarise(
              start_time = min(TimePoint),
              end_time = max(TimePoint),
              max_deaths = max(NO_DEATHS),
              .groups = "drop"
            )

          # Define a color palette for events - using colors different from the main plot
          # Using Set3 palette which provides more contrasting and lighter colors
          event_colors <- RColorBrewer::brewer.pal(min(12, max(3, nrow(event_data))), "Set3")

          if (nrow(event_data) > 0) {
            # First add colored rectangles for each event period
            for (i in 1:nrow(event_data)) {
              # Use modulo to cycle through colors if more events than colors
              color_index <- ((i - 1) %% length(event_colors)) + 1

              # 确保日期类型正确
              start_time <- event_data$start_time[i]
              end_time <- event_data$end_time[i]

              p <- p %>%
                add_trace(
                  type = "scatter",
                  mode = "none",
                  x = c(start_time, end_time, end_time, start_time),
                  y = c(y_min, y_min, y_max, y_max),
                  fill = "toself",
                  fillcolor = event_colors[color_index],
                  line = list(color = "transparent"),
                  opacity = 0.08, # Increase transparency for lighter background
                  name = event_data$event_name[i],
                  showlegend = TRUE,
                  hoverinfo = "name"
                ) %>%
                add_annotations(
                  x = date_midpoint(event_data$start_time[i], event_data$end_time[i]), # Center the label
                  y = y_max * 0.95, # Position consistently at 95% of the max y value
                  text = event_data$event_name[i],
                  showarrow = FALSE,
                  font = list(color = "darkred", size = 10)
                )
            }
          }

          # Set the layout with dynamic y-axis range
          p <- p %>% layout(
            title = list(
              text = "All Cause Mortality: Recorded vs. Expected",
              font = list(size = 16)
            ),
            xaxis = list(
              title = "Time",
              tickangle = 45
            ),
            yaxis = list(
              title = "Number of Deaths",
              range = c(y_min, y_max) # Set dynamic y-axis range
            ),
            legend = list(orientation = "h", y = -0.2),
            hovermode = "closest"
          )

          return(p)
        },
        error = function(e) {
          message("Error in ACM plot: ", e$message)
          return(plot_ly() %>%
            add_annotations(
              text = paste("Error:", e$message),
              x = 0.5, y = 0.5, showarrow = FALSE
            ))
        }
      )
    })


    # 修改 ED 图表渲染函数
    output$EDplot <- renderPlotly({
      # 添加调试信息
      message("Rendering ED plot...")

      # 使用tryCatch捕获错误
      tryCatch(
        {
          data <- filtered_total_data()
          if (is.null(data) || nrow(data) == 0) {
            message("No data available for ED plot")
            return(plot_ly() %>%
              add_annotations(
                text = "No data available for selected filters.",
                x = 0.5, y = 0.5, showarrow = FALSE
              ))
          }

          # 过滤无死亡记录的数据并计算超额死亡
          data <- data %>%
            filter(NO_DEATHS > 0) %>%
            mutate(EXCESS_DEATHS = NO_DEATHS - EXP_DEATHS)

          if (nrow(data) == 0) {
            return(plot_ly() %>%
              add_annotations(
                text = "No data with reported deaths available.",
                x = 0.5, y = 0.5, showarrow = FALSE
              ))
          }

          # 计算置信区间
          has_ci <- FALSE
          if ("LOWER_LIMIT" %in% names(data) && "UPPER_LIMIT" %in% names(data)) {
            data <- data %>% mutate(
              EXCESS_LOWER = NO_DEATHS - UPPER_LIMIT,
              EXCESS_UPPER = NO_DEATHS - LOWER_LIMIT
            )
            has_ci <- TRUE
          }

          message("ED plot created successfully")

          # 创建基础 plotly 对象
          p <- plot_ly()

          # 为每个模型添加柱状图
          # 定义一个更丰富的颜色调色板，确保每个模型有不同的颜色
          model_colors <- c(
            "Historical Average" = "rgba(0, 206, 209, 0.7)",      # 亮青色
            "Negative Binomial Regression" = "rgba(255, 0, 0, 0.7)", # 鲜红色
            "Quasi-Poisson Model" = "rgba(50, 205, 50, 0.7)",     # 亮绿色
            "Zero Inflated Poisson Model" = "rgba(153, 50, 204, 0.7)", # 深紫色
            "BSTS Model" = "rgba(255, 165, 0, 0.7)",               # 亮橙色
            "State Space Model" = "rgba(30, 144, 255, 0.7)",        # 亮蓝色
            "ARIMA/SARIMA Model" = "rgba(139, 69, 19, 0.7)"         # 深棕色
          )

          for (model_name in unique(data$Model)) {
            model_data <- data[data$Model == model_name, ]
            # 如果模型名称在预定义颜色中，使用它；否则使用默认颜色
            bar_color <- if (model_name %in% names(model_colors)) {
              model_colors[model_name]
            } else {
              "rgba(150, 150, 150, 0.7)" # 默认灰色
            }

            p <- p %>% add_bars(
              data = model_data,
              x = ~TimePoint,
              y = ~EXCESS_DEATHS,
              name = model_name,
              marker = list(color = bar_color),
              error_y = if (has_ci) {
                list(
                  type = "data",
                  array = model_data$EXCESS_UPPER - model_data$EXCESS_DEATHS,
                  arrayminus = model_data$EXCESS_DEATHS - model_data$EXCESS_LOWER,
                  color = "gray40",
                  thickness = 0.5,
                  width = 3
                )
              } else {
                NULL
              }
            )
          }

          # 添加事件标记为彩色矩形而不是垂直线
          event_data <- data %>%
            filter(!is.na(event_index), event_index != "0", !is.na(event_name)) %>%
            group_by(event_index, event_name) %>%
            summarise(
              start_time = min(TimePoint, na.rm = TRUE),
              end_time = max(TimePoint, na.rm = TRUE),
              max_excess = max(EXCESS_DEATHS, na.rm = TRUE),
              .groups = "drop"
            )

          # 定义事件的色彩谱 - 使用与主图不同的颜色方案
          # 使用Set3调色板，它提供更多对比色且较浅的颜色
          event_colors <- RColorBrewer::brewer.pal(min(12, max(3, nrow(event_data))), "Set3")

          if (nrow(event_data) > 0) {
            # 为每个事件期间添加彩色矩形
            for (i in 1:nrow(event_data)) {
              # 如果事件数量超过颜色数量，使用模运算循环使用颜色
              color_index <- ((i - 1) %% length(event_colors)) + 1

              y_min_val <- min(0, min(data$EXCESS_DEATHS, na.rm = TRUE))
              y_max_val <- max(data$EXCESS_DEATHS, na.rm = TRUE) * 1.1

              # 确保日期类型正确
              start_time <- event_data$start_time[i]
              end_time <- event_data$end_time[i]

              p <- p %>%
                add_trace(
                  type = "scatter",
                  mode = "none",
                  x = c(start_time, end_time, end_time, start_time),
                  y = c(y_min_val, y_min_val, y_max_val, y_max_val),
                  fill = "toself",
                  fillcolor = event_colors[color_index],
                  line = list(color = "transparent"),
                  opacity = 0.08, # 增加透明度，使背景更淡
                  name = event_data$event_name[i],
                  showlegend = TRUE,
                  hoverinfo = "name"
                ) %>%
                add_annotations(
                  x = date_midpoint(event_data$start_time[i], event_data$end_time[i]), # 将标签居中
                  y = y_max_val * 0.95, # Position consistently at 95% of the max y value
                  text = event_data$event_name[i],
                  showarrow = FALSE,
                  font = list(color = "darkred", size = 10)
                )
            }
          }

          # 设置布局
          plot_subtitle <- paste("Sex:", input$selected_sex, "| Age Group:", input$selected_age_group)

          p <- p %>% layout(
            title = list(
              text = paste("Excess Deaths<br><sup>", plot_subtitle, "</sup>"),
              font = list(size = 16)
            ),
            xaxis = list(
              title = "Time",
              tickangle = 45
            ),
            yaxis = list(title = "Excess Deaths"),
            barmode = "group",
            legend = list(orientation = "h", y = -0.2),
            hovermode = "closest"
          )

          return(p)
        },
        error = function(e) {
          message("Error in ED plot: ", e$message)
          return(plot_ly() %>%
            add_annotations(
              text = paste("Error:", e$message),
              x = 0.5, y = 0.5, showarrow = FALSE
            ))
        }
      )
    })

    # 修改 ED Line 图表渲染函数
    output$EDLinePlot <- renderPlotly({
      # 添加调试信息
      message("Rendering ED Line plot...")

      # 使用tryCatch捕获错误
      tryCatch(
        {
          data <- filtered_total_data()
          if (is.null(data) || nrow(data) == 0) {
            message("No data available for ED Line plot")
            return(plot_ly() %>%
              add_annotations(
                text = "No data available for selected filters.",
                x = 0.5, y = 0.5, showarrow = FALSE
              ))
          }

          # 过滤无死亡记录的数据并计算超额死亡
          data <- data %>%
            filter(NO_DEATHS > 0) %>%
            mutate(EXCESS_DEATHS = NO_DEATHS - EXP_DEATHS)

          if (nrow(data) == 0) {
            return(plot_ly() %>%
              add_annotations(
                text = "No data with reported deaths available.",
                x = 0.5, y = 0.5, showarrow = FALSE
              ))
          }

          # 计算置信区间
          has_ci <- FALSE
          if ("LOWER_LIMIT" %in% names(data) && "UPPER_LIMIT" %in% names(data)) {
            data <- data %>% mutate(
              EXCESS_LOWER = NO_DEATHS - UPPER_LIMIT,
              EXCESS_UPPER = NO_DEATHS - LOWER_LIMIT
            )
            has_ci <- TRUE
          }

          message("ED Line plot created successfully")

          # 创建基础 plotly 对象
          p <- plot_ly()

          # 为每个模型添加线图
          # 定义一个更丰富的颜色调色板，确保每个模型有不同的颜色
          model_colors <- c(
            "Historical Average" = "#00CED1",                # 亮青色
            "Negative Binomial Regression" = "#FF0000",      # 鲜红色
            "Quasi-Poisson Model" = "#32CD32",            # 亮绿色
            "Zero Inflated Poisson Model" = "#9932CC",      # 深紫色
            "BSTS Model" = "#FFA500",                      # 亮橙色
            "State Space Model" = "#1E90FF",               # 亮蓝色
            "ARIMA/SARIMA Model" = "#8B4513"               # 深棕色
          )

          for (model_name in unique(data$Model)) {
            model_data <- data[data$Model == model_name, ]
            # 如果模型名称在预定义颜色中，使用它；否则使用默认颜色
            line_color <- if (model_name %in% names(model_colors)) {
              model_colors[model_name]
            } else {
              "#999999" # 默认灰色
            }

            p <- p %>% add_trace(
              data = model_data,
              x = ~TimePoint,
              y = ~EXCESS_DEATHS,
              name = model_name,
              type = "scatter",
              mode = "lines",
              line = list(color = line_color, width = 2)
            )

            # 添加置信区间
            if (has_ci) {
              p <- p %>% add_ribbons(
                data = model_data,
                x = ~TimePoint,
                ymin = ~EXCESS_LOWER,
                ymax = ~EXCESS_UPPER,
                name = paste(model_name, "95% CI"),
                line = list(color = "transparent"),
                fillcolor = adjustcolor(line_color, alpha.f = 0.2),
                showlegend = FALSE
              )
            }
          }

          # 添加事件标记为彩色矩形而不是垂直线
          event_data <- data %>%
            filter(!is.na(event_index), event_index != "0", !is.na(event_name)) %>%
            group_by(event_index, event_name) %>%
            summarise(
              start_time = min(TimePoint, na.rm = TRUE),
              end_time = max(TimePoint, na.rm = TRUE),
              max_excess = max(EXCESS_DEATHS, na.rm = TRUE),
              .groups = "drop"
            )

          # 定义事件的色彩谱 - 使用与主图不同的颜色方案
          # 使用Set3调色板，它提供更多对比色且较浅的颜色
          event_colors <- RColorBrewer::brewer.pal(min(12, max(3, nrow(event_data))), "Set3")

          if (nrow(event_data) > 0) {
            # 为每个事件期间添加彩色矩形
            for (i in 1:nrow(event_data)) {
              # 如果事件数量超过颜色数量，使用模运算循环使用颜色
              color_index <- ((i - 1) %% length(event_colors)) + 1

              y_min_val <- min(0, min(data$EXCESS_DEATHS, na.rm = TRUE))
              y_max_val <- max(data$EXCESS_DEATHS, na.rm = TRUE) * 1.1

              # 确保日期类型正确
              start_time <- event_data$start_time[i]
              end_time <- event_data$end_time[i]

              p <- p %>%
                add_trace(
                  type = "scatter",
                  mode = "none",
                  x = c(start_time, end_time, end_time, start_time),
                  y = c(y_min_val, y_min_val, y_max_val, y_max_val),
                  fill = "toself",
                  fillcolor = event_colors[color_index],
                  line = list(color = "transparent"),
                  opacity = 0.08, # 增加透明度，使背景更淡
                  name = event_data$event_name[i],
                  showlegend = TRUE,
                  hoverinfo = "name"
                ) %>%
                add_annotations(
                  x = date_midpoint(event_data$start_time[i], event_data$end_time[i]), # 将标签居中
                  y = y_max_val * 0.95, # Position consistently at 95% of the max y value
                  text = event_data$event_name[i],
                  showarrow = FALSE,
                  font = list(color = "darkred", size = 10)
                )
            }
          }

          # 设置布局
          plot_subtitle <- paste("Sex:", input$selected_sex, "| Age Group:", input$selected_age_group)

          p <- p %>% layout(
            title = list(
              text = paste("Excess Deaths (Line Plot)<br><sup>", plot_subtitle, "</sup>"),
              font = list(size = 16)
            ),
            xaxis = list(
              title = "Time",
              tickangle = 45
            ),
            yaxis = list(title = "Excess Deaths"),
            legend = list(orientation = "h", y = -0.2),
            hovermode = "closest"
          )

          return(p)
        },
        error = function(e) {
          message("Error in ED Line plot: ", e$message)
          return(plot_ly() %>%
            add_annotations(
              text = paste("Error:", e$message),
              x = 0.5, y = 0.5, showarrow = FALSE
            ))
        }
      )
    })

    # 修改 P-Score 图表渲染函数
    output$EPplot <- renderPlotly({
      # 添加调试信息
      message("Rendering EP plot...")

      # 使用tryCatch捕获错误
      tryCatch(
        {
          data <- filtered_total_data()
          if (is.null(data) || nrow(data) == 0) {
            message("No data available for EP plot")
            return(plot_ly() %>%
              add_annotations(
                text = "No data available for selected filters.",
                x = 0.5, y = 0.5, showarrow = FALSE
              ))
          }

          # 过滤无死亡记录的数据并计算P-Score
          data <- data %>%
            filter(NO_DEATHS > 0, EXP_DEATHS > 0) %>%
            mutate(P_SCORE = (NO_DEATHS - EXP_DEATHS) / EXP_DEATHS * 100)

          if (nrow(data) == 0) {
            return(plot_ly() %>%
              add_annotations(
                text = "No data with reported deaths available.",
                x = 0.5, y = 0.5, showarrow = FALSE
              ))
          }

          # 计算置信区间
          has_ci <- FALSE
          if ("LOWER_LIMIT" %in% names(data) && "UPPER_LIMIT" %in% names(data)) {
            data <- data %>% mutate(
              P_SCORE_LOWER = (NO_DEATHS - UPPER_LIMIT) / EXP_DEATHS * 100,
              P_SCORE_UPPER = (NO_DEATHS - LOWER_LIMIT) / EXP_DEATHS * 100
            )
            has_ci <- TRUE
          }

          message("EP plot created successfully")

          # 创建基础 plotly 对象
          p <- plot_ly()

          # 为每个模型添加柱状图
          # 使用与ED图表相同的颜色调色板
          model_colors <- c(
            "Historical Average" = "rgba(0, 206, 209, 0.7)",      # 亮青色
            "Negative Binomial Regression" = "rgba(255, 0, 0, 0.7)", # 鲜红色
            "Quasi-Poisson Model" = "rgba(50, 205, 50, 0.7)",     # 亮绿色
            "Zero Inflated Poisson Model" = "rgba(153, 50, 204, 0.7)", # 深紫色
            "BSTS Model" = "rgba(255, 165, 0, 0.7)",               # 亮橙色
            "State Space Model" = "rgba(30, 144, 255, 0.7)",        # 亮蓝色
            "ARIMA/SARIMA Model" = "rgba(139, 69, 19, 0.7)"         # 深棕色
          )

          for (model_name in unique(data$Model)) {
            model_data <- data[data$Model == model_name, ]
            # 如果模型名称在预定义颜色中，使用它；否则使用默认颜色
            bar_color <- if (model_name %in% names(model_colors)) {
              model_colors[model_name]
            } else {
              "rgba(150, 150, 150, 0.7)" # 默认灰色
            }

            p <- p %>% add_bars(
              data = model_data,
              x = ~TimePoint,
              y = ~P_SCORE,
              name = model_name,
              marker = list(color = bar_color),
              error_y = if (has_ci) {
                list(
                  type = "data",
                  array = model_data$P_SCORE_UPPER - model_data$P_SCORE,
                  arrayminus = model_data$P_SCORE - model_data$P_SCORE_LOWER,
                  color = "gray40",
                  thickness = 0.5,
                  width = 3
                )
              } else {
                NULL
              }
            )
          }

          # 添加事件标记为彩色矩形而不是垂直线
          event_data <- data %>%
            filter(!is.na(event_index), event_index != "0", !is.na(event_name)) %>%
            group_by(event_index, event_name) %>%
            summarise(
              start_time = min(TimePoint, na.rm = TRUE),
              end_time = max(TimePoint, na.rm = TRUE),
              max_pscore = max(P_SCORE, na.rm = TRUE),
              .groups = "drop"
            )

          # 定义事件的色彩谱 - 使用与主图不同的颜色方案
          # 使用Set3调色板，它提供更多对比色且较浅的颜色
          event_colors <- RColorBrewer::brewer.pal(min(12, max(3, nrow(event_data))), "Set3")

          if (nrow(event_data) > 0) {
            # 为每个事件期间添加彩色矩形
            for (i in 1:nrow(event_data)) {
              # 如果事件数量超过颜色数量，使用模运算循环使用颜色
              color_index <- ((i - 1) %% length(event_colors)) + 1

              y_min_val <- min(0, min(data$P_SCORE, na.rm = TRUE))
              y_max_val <- max(data$P_SCORE, na.rm = TRUE) * 1.1

              # 确保日期类型正确
              start_time <- event_data$start_time[i]
              end_time <- event_data$end_time[i]

              p <- p %>%
                add_trace(
                  type = "scatter",
                  mode = "none",
                  x = c(start_time, end_time, end_time, start_time),
                  y = c(y_min_val, y_min_val, y_max_val, y_max_val),
                  fill = "toself",
                  fillcolor = event_colors[color_index],
                  line = list(color = "transparent"),
                  opacity = 0.08, # 增加透明度，使背景更淡
                  name = event_data$event_name[i],
                  showlegend = TRUE,
                  hoverinfo = "name"
                ) %>%
                add_annotations(
                  x = date_midpoint(event_data$start_time[i], event_data$end_time[i]), # 将标签居中
                  y = y_max_val * 0.95, # Position consistently at 95% of the max y value
                  text = event_data$event_name[i],
                  showarrow = FALSE,
                  font = list(color = "darkred", size = 10)
                )
            }
          }

          # 添加零线
          p <- p %>% add_trace(
            x = c(min(data$TimePoint, na.rm = TRUE), max(data$TimePoint, na.rm = TRUE)),
            y = c(0, 0),
            type = "scatter",
            mode = "lines",
            line = list(color = "black", dash = "dot", width = 1),
            showlegend = FALSE,
            hoverinfo = "none"
          )

          # 设置布局
          plot_subtitle <- paste("Sex:", input$selected_sex, "| Age Group:", input$selected_age_group)

          p <- p %>% layout(
            title = list(
              text = paste("P-Score of Excess Deaths (%)<br><sup>", plot_subtitle, "</sup>"),
              font = list(size = 16)
            ),
            xaxis = list(
              title = "Time",
              tickangle = 45
            ),
            yaxis = list(title = "P-Score (%)"),
            barmode = "group",
            legend = list(orientation = "h", y = -0.2),
            hovermode = "closest"
          )

          return(p)
        },
        error = function(e) {
          message("Error in EP plot: ", e$message)
          return(plot_ly() %>%
            add_annotations(
              text = paste("Error:", e$message),
              x = 0.5, y = 0.5, showarrow = FALSE
            ))
        }
      )
    })

    # 修改 P-Score 线图渲染函数
    output$EPLinePlot <- renderPlotly({
      # 添加调试信息
      message("Rendering EP Line plot...")

      # 使用tryCatch捕获错误
      tryCatch(
        {
          data <- filtered_total_data()
          if (is.null(data) || nrow(data) == 0) {
            message("No data available for EP Line plot")
            return(plot_ly() %>%
              add_annotations(
                text = "No data available for selected filters.",
                x = 0.5, y = 0.5, showarrow = FALSE
              ))
          }

          # 过滤无死亡记录的数据并计算P-Score
          data <- data %>%
            filter(NO_DEATHS > 0, EXP_DEATHS > 0) %>%
            mutate(P_SCORE = (NO_DEATHS - EXP_DEATHS) / EXP_DEATHS * 100)

          if (nrow(data) == 0) {
            return(plot_ly() %>%
              add_annotations(
                text = "No data with reported deaths available.",
                x = 0.5, y = 0.5, showarrow = FALSE
              ))
          }

          # 计算置信区间
          has_ci <- FALSE
          if ("LOWER_LIMIT" %in% names(data) && "UPPER_LIMIT" %in% names(data)) {
            data <- data %>% mutate(
              P_SCORE_LOWER = (NO_DEATHS - UPPER_LIMIT) / EXP_DEATHS * 100,
              P_SCORE_UPPER = (NO_DEATHS - LOWER_LIMIT) / EXP_DEATHS * 100
            )
            has_ci <- TRUE
          }

          message("EP Line plot created successfully")

          # 创建基础 plotly 对象
          p <- plot_ly()

          # 为每个模型添加线图
          # 使用与ED线图相同的颜色调色板
          model_colors <- c(
            "Historical Average" = "#00CED1",                # 亮青色
            "Negative Binomial Regression" = "#FF0000",      # 鲜红色
            "Quasi-Poisson Model" = "#32CD32",            # 亮绿色
            "Zero Inflated Poisson Model" = "#9932CC",      # 深紫色
            "BSTS Model" = "#FFA500",                      # 亮橙色
            "State Space Model" = "#1E90FF",               # 亮蓝色
            "ARIMA/SARIMA Model" = "#8B4513"               # 深棕色
          )

          for (model_name in unique(data$Model)) {
            model_data <- data[data$Model == model_name, ]
            # 如果模型名称在预定义颜色中，使用它；否则使用默认颜色
            line_color <- if (model_name %in% names(model_colors)) {
              model_colors[model_name]
            } else {
              "#999999" # 默认灰色
            }

            p <- p %>% add_trace(
              data = model_data,
              x = ~TimePoint,
              y = ~P_SCORE,
              name = model_name,
              type = "scatter",
              mode = "lines",
              line = list(color = line_color, width = 2)
            )

            # 添加置信区间
            if (has_ci) {
              p <- p %>% add_ribbons(
                data = model_data,
                x = ~TimePoint,
                ymin = ~P_SCORE_LOWER,
                ymax = ~P_SCORE_UPPER,
                name = paste(model_name, "95% CI"),
                line = list(color = "transparent"),
                fillcolor = adjustcolor(line_color, alpha.f = 0.2),
                showlegend = FALSE
              )
            }
          }

          # 添加事件标记为彩色矩形而不是垂直线
          event_data <- data %>%
            filter(!is.na(event_index), event_index != "0", !is.na(event_name)) %>%
            group_by(event_index, event_name) %>%
            summarise(
              start_time = min(TimePoint, na.rm = TRUE),
              end_time = max(TimePoint, na.rm = TRUE),
              max_pscore = max(P_SCORE, na.rm = TRUE),
              .groups = "drop"
            )

          # 定义事件的色彩谱 - 使用与主图不同的颜色方案
          # 使用Set3调色板，它提供更多对比色且较浅的颜色
          event_colors <- RColorBrewer::brewer.pal(min(12, max(3, nrow(event_data))), "Set3")

          if (nrow(event_data) > 0) {
            # 为每个事件期间添加彩色矩形
            for (i in 1:nrow(event_data)) {
              # 如果事件数量超过颜色数量，使用模运算循环使用颜色
              color_index <- ((i - 1) %% length(event_colors)) + 1

              y_min_val <- min(0, min(data$P_SCORE, na.rm = TRUE))
              y_max_val <- max(data$P_SCORE, na.rm = TRUE) * 1.1

              # 确保日期类型正确
              start_time <- event_data$start_time[i]
              end_time <- event_data$end_time[i]

              p <- p %>%
                add_trace(
                  type = "scatter",
                  mode = "none",
                  x = c(start_time, end_time, end_time, start_time),
                  y = c(y_min_val, y_min_val, y_max_val, y_max_val),
                  fill = "toself",
                  fillcolor = event_colors[color_index],
                  line = list(color = "transparent"),
                  opacity = 0.08, # 增加透明度，使背景更淡
                  name = event_data$event_name[i],
                  showlegend = TRUE,
                  hoverinfo = "name"
                ) %>%
                add_annotations(
                  x = date_midpoint(event_data$start_time[i], event_data$end_time[i]), # 将标签居中
                  y = y_max_val * 0.95, # Position consistently at 95% of the max y value
                  text = event_data$event_name[i],
                  showarrow = FALSE,
                  font = list(color = "darkred", size = 10)
                )
            }
          }

          # 添加零线
          p <- p %>% add_trace(
            x = c(min(data$TimePoint, na.rm = TRUE), max(data$TimePoint, na.rm = TRUE)),
            y = c(0, 0),
            type = "scatter",
            mode = "lines",
            line = list(color = "black", dash = "dot", width = 1),
            showlegend = FALSE,
            hoverinfo = "none"
          )

          # 设置布局
          plot_subtitle <- paste("Sex:", input$selected_sex, "| Age Group:", input$selected_age_group)

          p <- p %>% layout(
            title = list(
              text = paste("P-Score (Line Plot)<br><sup>", plot_subtitle, "</sup>"),
              font = list(size = 16)
            ),
            xaxis = list(
              title = "Time",
              tickangle = 45
            ),
            yaxis = list(title = "P-Score (%)"),
            legend = list(orientation = "h", y = -0.2),
            hovermode = "closest"
          )

          return(p)
        },
        error = function(e) {
          message("Error in EP Line plot: ", e$message)
          return(plot_ly() %>%
            add_annotations(
              text = paste("Error:", e$message),
              x = 0.5, y = 0.5, showarrow = FALSE
            ))
        }
      )
    })



    # 绘制超额死亡率图表
    output$excess_deaths_plot <- renderPlotly({
      req(filtered_excess_deaths_data())

      # 使用封装的绘图函数
      p <- plot_excess_deaths(
        filtered_excess_deaths_data(),
        title = paste("Excess Deaths -", input$excess_deaths_area, input$excess_deaths_sex, input$excess_deaths_age, input$excess_deaths_cause)
      )

      # 转换为交互式图表
      ggplotly(p, tooltip = c("x", "y", "colour")) %>%
        layout(
          legend = list(orientation = "h", y = -0.2),
          margin = list(l = 50, r = 50, b = 100, t = 50),
          hoverlabel = list(bgcolor = "white", font = list(size = 10))
        )
    })

    # 绘制 P-Score 图表
    output$p_score_plot <- renderPlotly({
      req(filtered_p_score_data())

      # 使用封装的绘图函数
      p <- plot_p_score(
        filtered_p_score_data(),
        title = paste("P-Score (%) -", input$p_score_area, input$p_score_sex, input$p_score_age, input$p_score_cause)
      )

      # 转换为交互式图表
      ggplotly(p, tooltip = c("x", "y", "fill")) %>%
        layout(
          legend = list(orientation = "h", y = -0.2),
          margin = list(l = 50, r = 50, b = 100, t = 50),
          hoverlabel = list(bgcolor = "white", font = list(size = 10))
        )
    })

    # 下载超额死亡率图表
    output$download_excess_deaths <- downloadHandler(
      filename = function() {
        paste("excess_deaths_", input$excess_deaths_area, "_", input$excess_deaths_sex, "_",
          input$excess_deaths_age, "_", input$excess_deaths_cause, "_",
          format(Sys.time(), "%Y%m%d_%H%M%S"), ".png",
          sep = ""
        )
      },
      content = function(file) {
        # 使用封装的绘图函数
        p <- plot_excess_deaths(
          filtered_excess_deaths_data(),
          title = paste("Excess Deaths -", input$excess_deaths_area, input$excess_deaths_sex, input$excess_deaths_age, input$excess_deaths_cause)
        )

        # 保存图表
        ggsave(file, plot = p, width = 10, height = 6, dpi = 300)
      }
    )

    # 下载 P-Score 图表
    output$download_p_score <- downloadHandler(
      filename = function() {
        paste("p_score_", input$p_score_area, "_", input$p_score_sex, "_",
          input$p_score_age, "_", input$p_score_cause, "_",
          format(Sys.time(), "%Y%m%d_%H%M%S"), ".png",
          sep = ""
        )
      },
      content = function(file) {
        # 使用封装的绘图函数
        p <- plot_p_score(
          filtered_p_score_data(),
          title = paste("P-Score (%) -", input$p_score_area, input$p_score_sex, input$p_score_age, input$p_score_cause)
        )

        # 保存图表
        ggsave(file, plot = p, width = 10, height = 6, dpi = 300)
      }
    )

    ###############################
    # --- Download handlers ---
    output$downloadACMPlot <- downloadHandler(
      filename = function() {
        paste0("ACM_Plot_", input$selected_sex, "_", input$selected_age_group, "_", Sys.Date(), ".pdf")
      },
      content = function(file) {
        req(filtered_total_data())
        tryCatch(
          {
            plot_obj <- create_acm_plot(filtered_total_data())
            ggsave(file, plot = plot_obj, device = "pdf", width = 11, height = 7, units = "in")
          },
          error = function(e) {
            message("Error in downloading ACM plot: ", e$message)
          }
        )
      }
    )

    output$downloadEDPlot <- downloadHandler(
      filename = function() {
        paste0("ExcessDeaths_Plot_", input$selected_sex, "_", input$selected_age_group, "_", Sys.Date(), ".pdf")
      },
      content = function(file) {
        req(filtered_total_data())
        tryCatch(
          {
            plot_obj <- create_ed_plot(filtered_total_data())
            ggsave(file, plot = plot_obj, device = "pdf", width = 11, height = 7, units = "in")
          },
          error = function(e) {
            message("Error in downloading ED plot: ", e$message)
          }
        )
      }
    )

    output$downloadEPPlot <- downloadHandler(
      filename = function() {
        paste0("PScore_Plot_", input$selected_sex, "_", input$selected_age_group, "_", Sys.Date(), ".pdf")
      },
      content = function(file) {
        req(filtered_total_data())
        tryCatch(
          {
            plot_obj <- create_ep_plot(filtered_total_data())
            ggsave(file, plot = plot_obj, device = "pdf", width = 11, height = 7, units = "in")
          },
          error = function(e) {
            message("Error in downloading EP plot: ", e$message)
          }
        )
      }
    )

    # 保存数据到全局响应式值
    observe({
      rv$plot_time_data <- filtered_total_data()
    })

    ################################################################
  })
}

