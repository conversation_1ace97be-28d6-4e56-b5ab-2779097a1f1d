# --- 1. 加载必要的库 ---
# 假设主要库已在app.R中加载
# library(shiny)

# --- 2. 定义模块UI ---
methods_module_ui <- function(id) {
  ns <- NS(id)
  
  fluidRow(
    column(6, style="padding: 0 30px 0 0;",
           div(id=ns("methodsbox"),
               p("This page has a description of the statistical methods used in the calculator to compute the expected and excess deaths in countries."),
               
               HTML("<br/>"),
               
               p(strong("All-cause mortality"),
                 "is defined as the total number of recorded deaths across all causes."),
               
               p(strong("Excess death"),
                 "is defined as the difference between the number of all-cause deaths during 2020-21 and the expected number of deaths."),
               
               p(strong("Expected death"), "is defined as the expected number of deaths in 2020-21 if no pandemic had occurred.",
                 "The expected number of deaths is calculated in two different ways, using either a",
                 strong("negative binomial regression"), "or the", strong("historical five year average"),
                 ", both of which are based on the years 2015-2019."),
               
               HTML("<br/>"),
               
               p(strong(em("Negative-binomial regression"))),
               
               p("This particular negative-binomial regression model is a generalized additive model (GAM) in that it uses smoothing functions",
                 "for the predictor variables. Since the date and period are input as discrete values, they are smoothed using cubic splines,",
                 "a common smoothing technique."),
               
               p(strong(em("Historical 5-year average"))),
               
               p("The 5-years historical average is based on, and the 95% confidence interval (95% CI) are calculated",
                 "from, the deaths observed in 2015-2019."),
               
               HTML("<br/>"),
               
               p("Below is a detailed description of the methods in statistical language.",
                 "It is in PDF format and can be saved for separate study."),
           ),
           # 修改iframe标签，使用更完整的属性设置
           tags$iframe(
               src = "ACMCalculator_Methodology_210407.pdf",
               width = "100%",
               height = "600px",
               style = "border: none;"
           )
    )
  )
}

# --- 3. 定义模块服务器 ---
methods_module_server <- function(id, rv) {
  moduleServer(id, function(input, output, session) {
    # 添加下载处理函数
    output$downloadMethodsPDF <- downloadHandler(
      filename = function() {
        "ACMCalculator_Methodology_210407.pdf"
      },
      content = function(file) {
        file.copy(from = "www/ACMCalculator_Methodology_210407.pdf", to = file)
      }
    )
    
    # 动态生成PDF查看器
    output$pdfViewer <- renderUI({
      tags$iframe(
        style = "height:600px; width:100%; scrolling=yes",
        src = session$getResourcePath("ACMCalculator_Methodology_210407.pdf")
      )
    })
  })
}