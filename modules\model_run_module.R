# Load necessary R packages
library(shiny)
library(dplyr)
library(DT)
library(mgcv)
library(openxlsx)
library(reshape2)
library(readxl)
library(stringr)
library(shinyjs) # 添加shinyjs库用于进度提示功能
library(shinyalert) # 添加shinyalert库用于显示弹窗

# 加载公共函数和模型文件
source("modules/common_functions.R")
source("modules/ACM_hist_new.R")
source("modules/ACM_nb.R")
source("modules/ACM_quasipoisson.R")
source("modules/ACM_zip.R")
source("modules/ACM_kfas.R")
source("modules/ACM_arima.R")
source("modules/ACM_his_tr.R") # <-- 新增：加载历史平均及趋势模型

# Define UI interface
model_run_module_ui <- function(id) {
  ns <- NS(id)
  fluidPage(
    useShinyjs(),
    tags$head(
      tags$script(HTML(sprintf(
        "
        $(document).on('click', '#%s', function() {
          $('#%s').show();
        });
        ",
        ns("run"),
        ns("progress-area")
      )))
    ),
    tags$head(
      tags$style(HTML("
            .shiny-output-error { visibility: hidden; }
            .shiny-output-error:before { visibility: hidden; }

            /* 进度条样式 */
            .progress-container {
                margin-top: 10px;
                margin-bottom: 10px;
            }
            .progress {
                height: 20px;
            }
            .progress-bar {
                transition: width 0.3s ease;
            }
            #progress-message {
                margin-top: 5px;
                font-weight: bold;
            }
        "))
    ),
    conditionalPanel(
      condition = "!output.has_data",
      ns = ns,
      div(
        class = "data-not-ready",
        style = "text-align: center; margin-top: 50px;",
        h3("Data is not ready. Please process data first...", style = "color: #666;"),
        br(),
        actionButton(ns("goto_data_tab"), "Go to Data tab",
          style = "margin-top: 20px; background-color: #337ab7; color: white;"
        )
      )
    ),
    conditionalPanel(
      condition = "output.has_data",
      ns = ns,
      sidebarLayout(
        mainPanel(
          width = 8,
          wellPanel(
            h4("Data Filtering"),
            fluidRow(
              column(width = 6,
                h5("Sex:"),
                div(style = "margin-bottom: 10px;",
                  div(style = "display: flex; gap: 10px;",
                    actionButton(ns("select_all_sex"), "Select All", class = "btn-sm", style = "flex: 1;"),
                    actionButton(ns("clear_all_sex"), "Clear All", class = "btn-sm", style = "flex: 1;")
                  )
                ),
                uiOutput(ns("sex_selector"))
              ),
              column(width = 6,
                h5("Age Group:"),
                div(style = "margin-bottom: 10px;",
                  div(style = "display: flex; gap: 10px;",
                    actionButton(ns("select_all_age"), "Select All", class = "btn-sm", style = "flex: 1;"),
                    actionButton(ns("clear_all_age"), "Clear All", class = "btn-sm", style = "flex: 1;")
                  )
                ),
                uiOutput(ns("age_group_selector"))
              )
            )
          )
        ),
        sidebarPanel(
          width = 4,
          wellPanel(
            h4("Model Selection"),
            tags$style(HTML("
              .model-option {
                margin-bottom: 12px;
              }
              .model-description {
                font-size: 12px;
                color: #666;
                margin-left: 20px;
                font-style: italic;
                display: block;
              }
            ")),
            div(style = "margin-bottom: 15px;",
              div(style = "display: flex; gap: 10px;",
                actionButton(ns("select_all"), "Select All", class = "btn-sm", style = "flex: 1;"),
                actionButton(ns("clear_all"), "Clear All", class = "btn-sm", style = "flex: 1;")
              )
            ),
            div(
              div(class = "model-option",
                checkboxInput(ns("model_arima"), "ARIMA/SARIMA Model"),
                tags$span(class = "model-description", "Time series model capturing temporal dependencies and seasonal patterns in mortality data.")
              ),
              div(class = "model-option",
                checkboxInput(ns("model_hist"), "Historical Average"),
                tags$span(class = "model-description", "Simple method using average deaths from baseline period to predict expected deaths.")
              ),
              div(class = "model-option", # <-- 新增：历史平均及趋势模型UI
                checkboxInput(ns("model_his_tr"), "Historical Average and Trend Model"),
                tags$span(class = "model-description", "Uses historical average and incorporates a time trend component.")
              ),
              div(class = "model-option",
                checkboxInput(ns("model_nb"), "Negative Binomial Regression"),
                tags$span(class = "model-description", "Regression model for count data with overdispersion, handles variance greater than mean.")
              ),
              div(class = "model-option",
                checkboxInput(ns("model_quasipoisson"), "Quasi-Poisson Model"),
                tags$span(class = "model-description", "Similar to Poisson regression but with an estimated dispersion parameter for overdispersed count data.")
              ),
              div(class = "model-option",
                checkboxInput(ns("model_zip"), "Zero Inflated Poisson Model"),
                tags$span(class = "model-description", "Two-component model for count data with excess zeros, combining logistic and Poisson regression.")
              )
            ),
            br(),
            actionButton(ns("run"), "Run Model", class = "btn-primary btn-block"),
            br(),
            hidden(
              div(
                id = ns("progress-area"), class = "progress-container",
                div(
                  class = "progress",
                  div(
                    id = ns("progress-bar"), class = "progress-bar progress-bar-striped active",
                    role = "progressbar", style = "width: 0%", `aria-valuenow` = "0",
                    `aria-valuemin` = "0", `aria-valuemax` = "100"
                  )
                ),
                div(
                  id = ns("progress-message"), class = "text-center",
                  textOutput(ns("progress_text"))
                )
              )
            )
          )
        )
      )
    )
  )
}

# Define server logic
model_run_module_server <- function(id, rv) {
  moduleServer(
    id,
    function(input, output, session) {
      output$has_data <- reactive({
        !is.null(rv$merged_data)
      })
      outputOptions(output, "has_data", suspendWhenHidden = FALSE)

      observe({
        updateCheckboxInput(session, "model_hist", value = TRUE)
        updateCheckboxInput(session, "model_his_tr", value = FALSE) # <-- 新增：历史平均及趋势模型默认不选中
        updateCheckboxInput(session, "model_nb", value = TRUE)
        updateCheckboxInput(session, "model_quasipoisson", value = FALSE)
        updateCheckboxInput(session, "model_zip", value = FALSE)
        updateCheckboxInput(session, "model_arima", value = FALSE)
      })

      observeEvent(input$select_all, {
        updateCheckboxInput(session, "model_hist", value = TRUE)
        updateCheckboxInput(session, "model_his_tr", value = TRUE) # <-- 新增：历史平均及趋势模型全选
        updateCheckboxInput(session, "model_nb", value = TRUE)
        updateCheckboxInput(session, "model_quasipoisson", value = TRUE)
        updateCheckboxInput(session, "model_zip", value = TRUE)
        updateCheckboxInput(session, "model_arima", value = TRUE)
      })

      observeEvent(input$clear_all, {
        updateCheckboxInput(session, "model_hist", value = FALSE)
        updateCheckboxInput(session, "model_his_tr", value = FALSE) # <-- 新增：历史平均及趋势模型全清
        updateCheckboxInput(session, "model_nb", value = FALSE)
        updateCheckboxInput(session, "model_quasipoisson", value = FALSE)
        updateCheckboxInput(session, "model_zip", value = FALSE)
        updateCheckboxInput(session, "model_arima", value = FALSE)
      })

      source("modules/utils.R")

      rv_data <- reactive({
        req(rv$merged_data_ext)
        return(rv$merged_data_ext)
      })

      unique_sex <- reactive({
        req(rv_data())
        unique(rv_data()$SEX)
      })

      unique_age_group <- reactive({
        req(rv_data())
        unique(rv_data()$AGE_GROUP)
      })

      output$sex_selector <- renderUI({
        req(unique_sex())
        choices <- unique_sex()
        selected <- if ("Total" %in% choices) "Total" else choices
        checkboxGroupInput(session$ns("sex"), NULL,
          choices = choices,
          selected = selected
        )
      })

      output$age_group_selector <- renderUI({
        req(unique_age_group())
        choices <- unique_age_group()
        selected <- if ("Total" %in% choices) "Total" else choices
        checkboxGroupInput(session$ns("age_group"), NULL,
          choices = choices,
          selected = selected
        )
      })

      observeEvent(input$select_all_sex, {
        req(unique_sex())
        updateCheckboxGroupInput(session, "sex", selected = unique_sex())
      })

      observeEvent(input$clear_all_sex, {
        updateCheckboxGroupInput(session, "sex", selected = character(0))
      })

      observeEvent(input$select_all_age, {
        req(unique_age_group())
        updateCheckboxGroupInput(session, "age_group", selected = unique_age_group())
      })

      observeEvent(input$clear_all_age, {
        updateCheckboxGroupInput(session, "age_group", selected = character(0))
      })

      filtered_data <- reactive({
        req(rv_data(), input$sex, input$age_group)
        rv_data() %>%
          filter(
            SEX %in% input$sex,
            AGE_GROUP %in% input$age_group
          )
      })

      progress_value <- reactiveVal(0)
      progress_message <- reactiveVal("")

      output$progress_text <- renderText({
        progress_message()
      })

      updateProgress <- function(value, message) {
        progress_value(value)
        progress_message(message)
        shinyjs::runjs(sprintf(
          "$('#%s').css('width', '%s%%').attr('aria-valuenow', %s);",
          session$ns("progress-bar"), value, value
        ))
      }

      model_result_keys <- list(
        "ARIMA/SARIMA Model" = "arima",
        "Historical Average" = "hist",
        "Historical Average and Trend Model" = "his_tr", # <-- 新增：历史平均及趋势模型键
        "Negative Binomial Regression" = "nb",
        "Quasi-Poisson Model" = "quasipoisson",
        "Zero Inflated Poisson Model" = "zip"
      )

      get_selected_models <- reactive({
        selected_models <- c()
        if(input$model_arima) selected_models <- c(selected_models, "ARIMA/SARIMA Model")
        if(input$model_hist) selected_models <- c(selected_models, "Historical Average")
        if(input$model_his_tr) selected_models <- c(selected_models, "Historical Average and Trend Model") # <-- 新增
        if(input$model_nb) selected_models <- c(selected_models, "Negative Binomial Regression")
        if(input$model_quasipoisson) selected_models <- c(selected_models, "Quasi-Poisson Model")
        if(input$model_zip) selected_models <- c(selected_models, "Zero Inflated Poisson Model")
        return(selected_models)
      })

      model_results <- eventReactive(input$run, {
        selected_models <- get_selected_models()
        message("Selected models: ", paste(selected_models, collapse = ", "))
        shinyjs::show(id = "progress-area")
        shinyjs::disable("run")
        updateProgress(10, "Preparing data...")
        req(filtered_data())
        data <- filtered_data()

        if (nrow(data) == 0) {
          shinyjs::html("progress-message", "Error: No data available after filtering")
          shinyjs::delay(1500, {
            shinyjs::hide("progress-area")
            shinyjs::enable("run")
          })
          return(NULL)
        }

        results <- list()
        num_selected_models <- length(selected_models)
        progress_increment <- if (num_selected_models > 0) 80 / num_selected_models else 0 # 10% for prep, 10% for final processing
        current_progress <- 10

        # 1. Historical Average
        if ("Historical Average" %in% selected_models) {
          current_progress <- current_progress + progress_increment / 2
          updateProgress(floor(current_progress), "Running Historical Average model...")
          message("\n[INFO] Running Historical Average model...")
          flush.console()
          results$hist <- tryCatch({
            source("modules/ACM_hist_new.R", local = TRUE) # Ensure it's sourced in the correct environment
            result <- fcn_hist(data)
            if (is.null(result) || !is.data.frame(result)) dplyr::tibble() else dplyr::as_tibble(result)
          }, error = function(e) {
            message("Error in Historical Average model: ", e$message)
            dplyr::tibble()
          })
          current_progress <- current_progress + progress_increment / 2
          updateProgress(floor(current_progress), "Historical Average model completed")
        }

        # 2. Historical Average and Trend Model
        if ("Historical Average and Trend Model" %in% selected_models) {
          current_progress <- current_progress + progress_increment / 2
          updateProgress(floor(current_progress), "Running Historical Average and Trend Model...")
          message("\n[INFO] Running Historical Average and Trend Model...")
          flush.console()
          results$his_tr <- tryCatch({
            source("modules/ACM_his_tr.R", local = TRUE) # Ensure it's sourced
            result <- fcn_his_tr(data)
            if (is.null(result) || !is.data.frame(result)) dplyr::tibble() else dplyr::as_tibble(result)
          }, error = function(e) {
            message("Error in Historical Average and Trend Model: ", e$message)
            dplyr::tibble()
          })
          current_progress <- current_progress + progress_increment / 2
          updateProgress(floor(current_progress), "Historical Average and Trend Model completed")
        }

        # 3. Negative Binomial Regression
        if ("Negative Binomial Regression" %in% selected_models) {
          current_progress <- current_progress + progress_increment / 2
          updateProgress(floor(current_progress), "Running Negative Binomial Regression model...")
          message("\n[INFO] Running Negative Binomial Regression model...")
          flush.console()
          results$nb <- tryCatch({
            source("modules/ACM_nb.R", local = TRUE)
            model_result <- fcn_nb(data)
            if (is.null(model_result) || !is.data.frame(model_result)) dplyr::tibble() else dplyr::as_tibble(model_result)
          }, error = function(e) {
            message("Error in Negative Binomial Regression model: ", e$message)
            dplyr::tibble()
          })
          current_progress <- current_progress + progress_increment / 2
          updateProgress(floor(current_progress), "Negative Binomial Regression model completed")
        }

        # 4. Quasi-Poisson Model
        if ("Quasi-Poisson Model" %in% selected_models) {
          current_progress <- current_progress + progress_increment / 2
          updateProgress(floor(current_progress), "Running Quasi-Poisson Model...")
          message("\n[INFO] Running Quasi-Poisson Model...")
          flush.console()
          results$quasipoisson <- tryCatch({
            source("modules/ACM_quasipoisson.R", local = TRUE)
            result <- fcn_quasipoisson(data)
            if (is.null(result) || !is.data.frame(result)) dplyr::tibble() else dplyr::as_tibble(result)
          }, error = function(e) {
            message("Error in Quasi-Poisson Model: ", e$message)
            dplyr::tibble()
          })
          current_progress <- current_progress + progress_increment / 2
          updateProgress(floor(current_progress), "Quasi-Poisson Model completed")
        }

        # 5. Zero Inflated Poisson Model
        if ("Zero Inflated Poisson Model" %in% selected_models) {
          current_progress <- current_progress + progress_increment / 2
          updateProgress(floor(current_progress), "Running Zero Inflated Poisson Model...")
          message("\n[INFO] Running Zero Inflated Poisson Model...")
          flush.console()
          results$zip <- tryCatch({
            source("modules/ACM_zip.R", local = TRUE)
            result <- fcn_zip(data)
            if (is.null(result) || !is.data.frame(result)) dplyr::tibble() else dplyr::as_tibble(result)
          }, error = function(e) {
            message("Error in Zero Inflated Poisson Model: ", e$message)
            dplyr::tibble()
          })
          current_progress <- current_progress + progress_increment / 2
          updateProgress(floor(current_progress), "Zero Inflated Poisson Model completed")
        }
        
        # 6. ARIMA/SARIMA Model
        if ("ARIMA/SARIMA Model" %in% selected_models) {
          current_progress <- current_progress + progress_increment / 2
          updateProgress(floor(current_progress), "Running ARIMA/SARIMA Model...")
          message("\n[INFO] Running ARIMA/SARIMA Model...")
          flush.console()
          results$arima <- tryCatch({
            source("modules/ACM_arima.R", local = TRUE)
            arima_result <- fcn_arima(data)
            if (is.null(arima_result) || !is.data.frame(arima_result)) dplyr::tibble() else dplyr::as_tibble(arima_result)
          }, error = function(e) {
            message("Error in ARIMA/SARIMA Model: ", e$message)
            dplyr::tibble()
          })
          current_progress <- current_progress + progress_increment / 2
          updateProgress(floor(current_progress), "ARIMA/SARIMA Model completed")
        }

        updateProgress(95, "Processing results...")

        # Check if any model produced results
        successful_models_exist <- any(sapply(results, function(res) !is.null(res) && (is.data.frame(res) && nrow(res) > 0 || is.list(res) && !is.null(res$predictions))))

        if (successful_models_exist) {
          message("\n[DEBUG] Results summary:")
          message("Historical Average: ", !is.null(results$hist) && nrow(results$hist) > 0)
          message("Historical Average and Trend: ", !is.null(results$his_tr) && nrow(results$his_tr) > 0) # <-- 新增
          message("Negative Binomial: ", !is.null(results$nb) && nrow(results$nb) > 0)
          message("Quasi-Poisson: ", !is.null(results$quasipoisson) && nrow(results$quasipoisson) > 0)
          message("Zero Inflated Poisson: ", !is.null(results$zip) && nrow(results$zip) > 0)
          message("ARIMA/SARIMA Model: ", !is.null(results$arima) && nrow(results$arima) > 0)

          success_count <- sum(c(
            !is.null(results$hist) && (is.data.frame(results$hist) && nrow(results$hist) > 0 || (is.list(results$hist) && !is.null(results$hist$predictions))),
            !is.null(results$his_tr) && (is.data.frame(results$his_tr) && nrow(results$his_tr) > 0 || (is.list(results$his_tr) && !is.null(results$his_tr$predictions))), # <-- 新增
            !is.null(results$nb) && (is.data.frame(results$nb) && nrow(results$nb) > 0 || (is.list(results$nb) && !is.null(results$nb$predictions))),
            !is.null(results$quasipoisson) && (is.data.frame(results$quasipoisson) && nrow(results$quasipoisson) > 0 || (is.list(results$quasipoisson) && !is.null(results$quasipoisson$predictions))),
            !is.null(results$zip) && (is.data.frame(results$zip) && nrow(results$zip) > 0 || (is.list(results$zip) && !is.null(results$zip$predictions))),
            !is.null(results$arima) && (is.data.frame(results$arima) && nrow(results$arima) > 0 || (is.list(results$arima) && !is.null(results$arima$predictions)))
          ))
          total_count <- length(selected_models)

          model_status_rows <- sapply(selected_models, function(model_name) {
            result_key_name <- model_result_keys[[model_name]]
            model_succeeded <- !is.null(results[[result_key_name]]) && 
                               ( (is.data.frame(results[[result_key_name]]) && nrow(results[[result_key_name]]) > 0) || 
                                 (is.list(results[[result_key_name]]) && !is.null(results[[result_key_name]]$predictions)) )
            paste(
              "<tr>",
              "<td style='padding:8px; text-align:left; border:1px solid #ddd;'>", model_name, "</td>",
              "<td style='padding:8px; text-align:center; border:1px solid #ddd;'>",
              ifelse(model_succeeded,
                "<span style='color:green; font-weight:bold;'>✓ Success</span>",
                "<span style='color:red; font-weight:bold;'>✗ Failed</span>"),
              "</td>",
              "</tr>"
            )
          })
          
          model_results_table <- paste(
            "<div style='text-align:center; margin-bottom:15px;'>",
            "<span style='font-size:18px; font-weight:bold;'>", success_count, " of ", total_count, " models completed successfully</span>",
            "</div>",
            "<table style='width:100%; border-collapse:collapse; margin-bottom:15px;'>",
            "<tr style='background-color:#f2f2f2;'>",
            "<th style='padding:8px; text-align:left; border:1px solid #ddd;'>Model</th>",
            "<th style='padding:8px; text-align:center; border:1px solid #ddd;'>Status</th>",
            "</tr>",
            paste(model_status_rows, collapse=""),
            "</table>",
            "<div style='text-align:center; font-style:italic; color:#666;'>",
            "This window will close automatically in 15 seconds.",
            "</div>"
          )

          shinyalert::shinyalert(
            title = "<span style='color:#2c3e50;'>Model Execution Results</span>",
            text = model_results_table,
            type = "success", # Or "info" if some failed
            html = TRUE,
            confirmButtonText = "OK",
            timer = 15000,
            animation = TRUE,
            size = "m",
            closeOnEsc = TRUE,
            closeOnClickOutside = TRUE
          )

          combined_results <- NULL
          for (method in selected_models) {
            result_key <- model_result_keys[[method]]
            if (!is.null(result_key) && !is.null(results[[result_key]])) {
              current_model_output <- results[[result_key]]
              if (is.data.frame(current_model_output) && nrow(current_model_output) > 0) {
                 message("[INFO] Processing results for: ", method)
                 current_results_df <- dplyr::mutate(current_model_output, Model = method)
                 combined_results <- dplyr::bind_rows(combined_results, current_results_df)
              } else if (is.list(current_model_output) && !is.null(current_model_output$predictions) && is.data.frame(current_model_output$predictions) && nrow(current_model_output$predictions) > 0) {
                 message("[INFO] Processing list-based predictions for: ", method)
                 current_results_df <- dplyr::mutate(current_model_output$predictions, Model = method)
                 combined_results <- dplyr::bind_rows(combined_results, current_results_df)
              } else {
                 message("[WARNING] No valid data frame or predictions found for model: ", method)
              }
            }
          }
          
          if (!is.null(combined_results) && nrow(combined_results) > 0) {
            possible_integer_cols <- c("NO_DEATHS", "EXP_DEATHS", "ESTIMATE", "LOWER_LIMIT", "UPPER_LIMIT", "EXCESS_DEATHS",
                                     "TOTAL_EXPECTED", "TOTAL_EXCESS", "TOTAL_SE", "EXCESS_LOWER", "EXCESS_UPPER")
            existing_integer_cols <- intersect(names(combined_results), possible_integer_cols)
            
            combined_results <- combined_results %>%
              mutate(across(all_of(existing_integer_cols), as.numeric))

            if ("P_SCORE" %in% names(combined_results)) {
              combined_results <- combined_results %>% mutate(P_SCORE = round(as.numeric(P_SCORE), 2))
              if ("P_LOWER" %in% names(combined_results)) combined_results <- combined_results %>% mutate(P_LOWER = round(as.numeric(P_LOWER), 2))
              if ("P_UPPER" %in% names(combined_results)) combined_results <- combined_results %>% mutate(P_UPPER = round(as.numeric(P_UPPER), 2))
            } else if (all(c("EXCESS_DEATHS", "EXP_DEATHS") %in% names(combined_results))) {
              combined_results <- combined_results %>%
                mutate(P_SCORE = round(100 * EXCESS_DEATHS / EXP_DEATHS, 2))
              if (all(c("LOWER_LIMIT", "UPPER_LIMIT", "NO_DEATHS") %in% names(combined_results))) { # Assuming NO_DEATHS is observed for P_LOWER/UPPER calculation from limits
                 combined_results <- combined_results %>%
                   mutate(
                     # This P_LOWER/P_UPPER calculation might need review based on exact definition
                     P_LOWER = round(100 * (LOWER_LIMIT - EXP_DEATHS) / EXP_DEATHS, 2), 
                     P_UPPER = round(100 * (UPPER_LIMIT - EXP_DEATHS) / EXP_DEATHS, 2)
                   )
              }
            }
            
            combined_results <- combined_results %>%
              mutate(across(all_of(existing_integer_cols), round))

            cols_to_remove <- intersect(
              names(combined_results),
              c("COUNTRY", "ISO3", "AREA", "CAUSE", "DATE_TO_SPECIFY_WEEK", "SE_IDENTIFIER")
            )
            if (length(cols_to_remove) > 0) {
              combined_results <- combined_results %>% select(-all_of(cols_to_remove))
            }
            
            # Define desired column order, ensuring all columns exist
            desired_cols <- c("WM_IDENTIFIER", "YEAR", "PERIOD", "SEX", "AGE_GROUP", 
                              "NO_DEATHS", "EXP_DEATHS", "LOWER_LIMIT", "UPPER_LIMIT", 
                              "EXCESS_DEATHS", "P_SCORE", "P_LOWER", "P_UPPER", # Added P_LOWER, P_UPPER
                              "event_index", "event_name", "SERIES", "Model")
            final_cols <- character(0)
            for(col_name in desired_cols){
                if(col_name %in% names(combined_results)){
                    final_cols <- c(final_cols, col_name)
                }
            }
            # Add any remaining columns not in desired_cols (e.g. if new ones are added by models)
            remaining_cols <- setdiff(names(combined_results), final_cols)
            final_cols <- c(final_cols, remaining_cols)

            combined_results <- combined_results %>% select(all_of(final_cols))
            
            rv$total_prediction <- combined_results
          } else {
             rv$total_prediction <- dplyr::tibble() # Ensure it's an empty tibble if no results
          }


          # Process summary_by_event and overall_summary similarly
          # For summary_by_event
          all_summaries_by_event <- list()
          for (method in selected_models) {
            result_key <- model_result_keys[[method]]
            if (!is.null(result_key) && !is.null(results[[result_key]])) {
                current_model_output <- results[[result_key]]
                # Check if 'summary_by_event' attribute exists and is a data frame
                if (!is.null(attr(current_model_output, "summary_by_event")) && is.data.frame(attr(current_model_output, "summary_by_event"))) {
                    summary_df <- attr(current_model_output, "summary_by_event")
                    if (nrow(summary_df) > 0) {
                        all_summaries_by_event[[method]] <- dplyr::mutate(summary_df, Model = method)
                    }
                } else if (is.list(current_model_output) && !is.null(current_model_output$summary_by_event) && is.data.frame(current_model_output$summary_by_event)) {
                    # Handle cases where summary_by_event is a direct list component (e.g. from ARIMA)
                    summary_df <- current_model_output$summary_by_event
                     if (nrow(summary_df) > 0) {
                        all_summaries_by_event[[method]] <- dplyr::mutate(summary_df, Model = method)
                    }
                }
            }
          }
          if (length(all_summaries_by_event) > 0) {
            combined_summary <- dplyr::bind_rows(all_summaries_by_event)
            # Apply formatting similar to combined_results
            possible_integer_cols_sum <- c("observed", "expected", "excess", "excess_lower", "excess_upper", "TOTAL_EXPECTED", "TOTAL_EXCESS", "TOTAL_SE", "EXCESS_LOWER", "EXCESS_UPPER")
            possible_decimal_cols_sum <- c("p_score", "p_score_lower", "p_score_upper", "P_SCORE")
            
            existing_integer_cols_sum <- intersect(names(combined_summary), possible_integer_cols_sum)
            existing_decimal_cols_sum <- intersect(names(combined_summary), possible_decimal_cols_sum)

            if (length(existing_integer_cols_sum) > 0) {
              combined_summary <- combined_summary %>%
                mutate(across(all_of(existing_integer_cols_sum), ~ round(as.numeric(.))))
            }
            if (length(existing_decimal_cols_sum) > 0) {
              combined_summary <- combined_summary %>%
                mutate(across(all_of(existing_decimal_cols_sum), ~ round(as.numeric(.), 2)))
            }
            rv$summary_by_event <- combined_summary
          } else {
            rv$summary_by_event <- dplyr::tibble()
          }

          # For overall_summary
          all_overall_summaries <- list()
            for (method in selected_models) {
                result_key <- model_result_keys[[method]]
                if (!is.null(result_key) && !is.null(results[[result_key]])) {
                    current_model_output <- results[[result_key]]
                    if (!is.null(attr(current_model_output, "overall_summary")) && is.data.frame(attr(current_model_output, "overall_summary"))) {
                        overall_df <- attr(current_model_output, "overall_summary")
                        if (nrow(overall_df) > 0) {
                           all_overall_summaries[[method]] <- dplyr::mutate(overall_df, Model = method)
                        }
                    } else if (is.list(current_model_output) && !is.null(current_model_output$overall_summary) && is.data.frame(current_model_output$overall_summary)) {
                        overall_df <- current_model_output$overall_summary
                        if (nrow(overall_df) > 0) {
                           all_overall_summaries[[method]] <- dplyr::mutate(overall_df, Model = method)
                        }
                    }
                }
            }
          if (length(all_overall_summaries) > 0) {
            combined_overall <- dplyr::bind_rows(all_overall_summaries)
            # Apply formatting
            if (length(existing_integer_cols_sum) > 0) { # Reusing col definitions from summary_by_event
              combined_overall <- combined_overall %>%
                mutate(across(all_of(intersect(names(combined_overall), existing_integer_cols_sum)), ~ round(as.numeric(.))))
            }
            if (length(existing_decimal_cols_sum) > 0) {
              combined_overall <- combined_overall %>%
                mutate(across(all_of(intersect(names(combined_overall), existing_decimal_cols_sum)), ~ round(as.numeric(.), 2)))
            }
            rv$overall_summary <- combined_overall
          } else {
            rv$overall_summary <- dplyr::tibble()
          }
          
          message("Model results updated successfully in rv")
        } else {
            message("No models produced valid results. rv will not be updated with predictions.")
            rv$total_prediction <- dplyr::tibble()
            rv$summary_by_event <- dplyr::tibble()
            rv$overall_summary <- dplyr::tibble()
             shinyalert::shinyalert(
                title = "No Model Results",
                text = "None of the selected models produced any output. Please check your data and model configurations.",
                type = "warning",
                confirmButtonText = "OK"
            )
        }
        
        updateProgress(100, "All calculations completed!")
        shinyjs::delay(1500, {
          shinyjs::hide("progress-area")
          shinyjs::enable("run")
        })
        return(results)
      })

      observeEvent(input$goto_data_tab, {
        session$sendCustomMessage("navigateTab", "Data")
      })

      return(list(
        model_results = model_results
      ))
    }
  )
}
