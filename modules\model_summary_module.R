# Load necessary R packages
library(shiny)
library(dplyr)
library(DT)
library(openxlsx)
library(shinyjs)

# Define UI interface for model summary
model_summary_module_ui <- function(id) {
  ns <- NS(id)
  fluidPage(
    useShinyjs(),
    # Add CSS using tags$style
    tags$head(
      tags$style(HTML("
        .shiny-output-error { visibility: hidden; }
        .shiny-output-error:before { visibility: hidden; }
        .dt-buttons { margin-bottom: 10px; }
        .data-not-ready { text-align: center; padding: 50px; color: #666; background: #f9f9f9; border-radius: 5px; margin: 20px; }
      "))
    ),

    # 添加数据检查条件面板
    conditionalPanel(
      condition = "!output.has_model_data",
      ns = ns,
      div(
        class = "data-not-ready",
        style = "text-align: center; margin-top: 50px;",
        h3("Model results are not available. Please run the model first...", style = "color: #666;"),
        br(),
        div(style = "margin-top: 20px;",
          actionButton(ns("goto_model_tab"), "Go to Model tab", class = "btn-primary")
        )
      )
    ),

    # 原有的UI内容放在条件面板中
    conditionalPanel(
      condition = "output.has_model_data",
      ns = ns,
      fluidRow(
        # Left side for table content
        column(
          9,
          tabsetPanel(
            id = ns("tabsetpanel"),
            tabPanel(
              "Total Prediction Results",
              value = "Total Prediction Results",
              br(),
              wellPanel(
                # 添加下载按钮
                DT::dataTableOutput(ns("total_predictions")),
                br(), br(),
                downloadButton(ns("downloadTotalPredictions"), "Download Full Data")
              )
            ),
            tabPanel(
              "Summary by Event",
              value = "Summary by Event",
              br(),
              wellPanel(
                # 添加下载按钮
                DT::dataTableOutput(ns("summary_by_event")),
                br(), br(),
                downloadButton(ns("downloadSummaryByEvent"), "Download Full Data")
              )
            ),
            tabPanel(
              "Overall Summary",
              value = "Overall Summary",
              br(),
              wellPanel(
                # 添加下载按钮
                DT::dataTableOutput(ns("overall_summary")),
                br(), br(),
                downloadButton(ns("downloadOverallSummary"), "Download Full Data")
              )
            )
          )
        ),
        # Right side for filter controls
        column(
          3,
          wellPanel(
            h4("Filter Results"),
            # Sex filter
            uiOutput(ns("sex_filter")),
            # Age group filter
            uiOutput(ns("age_group_filter")),
            # Model filter
            uiOutput(ns("model_filter")),
            # Event filter (for Summary by Event tab)
            conditionalPanel(
              condition = "input.tabsetpanel === 'Summary by Event'",
              ns = ns,
              uiOutput(ns("event_filter"))
            )
          )
        )
      )
    )
  )
}

# Define server logic for model summary
model_summary_module_server <- function(id, rv) {
  moduleServer(
    id,
    function(input, output, session) {
      # 添加数据状态检查
      output$has_model_data <- reactive({
        !is.null(rv$total_prediction) && !is.null(rv$summary_by_event) && !is.null(rv$overall_summary)
      })
      outputOptions(output, "has_model_data", suspendWhenHidden = FALSE)

      # 按钮样式已直接在UI中添加，不需要使用shinyjs

      # 获取当前选中的标签页
      current_tab <- reactive({
        input$tabsetpanel
      })

      # 动态生成过滤器
      output$sex_filter <- renderUI({
        req(rv$overall_summary)
        choices <- unique(rv$overall_summary$SEX)
        selectInput(session$ns("selected_sex"), "Select Sex:",
          choices = choices,
          selected = if ("Total" %in% choices) "Total" else choices[1]
        )
      })

      output$age_group_filter <- renderUI({
        req(rv$overall_summary)
        choices <- unique(rv$overall_summary$AGE_GROUP)
        selectInput(session$ns("selected_age_group"), "Select Age Group:",
          choices = choices,
          selected = if ("Total" %in% choices) "Total" else choices[1]
        )
      })

      output$model_filter <- renderUI({
        req(rv$overall_summary)
        choices <- unique(rv$overall_summary$Model)
        checkboxGroupInput(session$ns("selected_models"), "Select Models:",
          choices = choices,
          selected = choices
        )
      })

      output$event_filter <- renderUI({
        req(rv$summary_by_event)
        choices <- unique(rv$summary_by_event$event_name)
        selectInput(session$ns("selected_event"), "Select Event:",
          choices = c("All", choices),
          selected = "All"
        )
      })

      # 过滤数据
      filtered_total_predictions <- reactive({
        req(rv$total_prediction, input$selected_sex, input$selected_age_group, input$selected_models)

        data <- rv$total_prediction %>%
          filter(
            SEX == input$selected_sex,
            AGE_GROUP == input$selected_age_group,
            Model %in% input$selected_models
          )

        return(data)
      })

      filtered_summary_by_event <- reactive({
        req(rv$summary_by_event, input$selected_sex, input$selected_age_group, input$selected_models)

        data <- rv$summary_by_event %>%
          filter(
            SEX == input$selected_sex,
            AGE_GROUP == input$selected_age_group,
            Model %in% input$selected_models
          )

        # 如果选择了特定事件，则进一步过滤
        if (!is.null(input$selected_event) && input$selected_event != "All") {
          data <- data %>% filter(event_name == input$selected_event)
        }

        return(data)
      })

      filtered_overall_summary <- reactive({
        req(rv$overall_summary, input$selected_sex, input$selected_age_group, input$selected_models)

        data <- rv$overall_summary %>%
          filter(
            SEX == input$selected_sex,
            AGE_GROUP == input$selected_age_group,
            Model %in% input$selected_models
          )

        return(data)
      })

      # 表格输出
      output$total_predictions <- DT::renderDataTable({
        req(filtered_total_predictions())

        DT::datatable(filtered_total_predictions(),
          options = list(
            pageLength = 10,
            autoWidth = TRUE,
            width = "100%",
            scrollX = TRUE,
            scrollCollapse = TRUE,
            dom = "Blfrtip",
            buttons = list(
              list(extend = "copy", text = "Copy")
            ),
            columnDefs = list(
              list(
                targets = ncol(filtered_total_predictions()) - 1,
                width = "200px"
              ),
              list(
                targets = ncol(filtered_total_predictions()) - 0,
                width = "200px"
              )
            )
          ),
          extensions = c("Buttons")
        )
      })

      output$summary_by_event <- DT::renderDataTable({
        req(filtered_summary_by_event())

        DT::datatable(filtered_summary_by_event(),
          options = list(
            pageLength = 10,
            autoWidth = TRUE,
            width = "100%",
            scrollX = TRUE,
            scrollCollapse = TRUE,
            dom = "Blfrtip",
            buttons = list(
              list(extend = "copy", text = "Copy")
            ),
            columnDefs = list(
              list(
                targets = ncol(filtered_summary_by_event()) - 1,
                width = "150px"
              ),
              list(
                targets = ncol(filtered_summary_by_event()) - 0,
                width = "200px"
              )
            )
          ),
          extensions = c("Buttons")
        )
      })

      output$overall_summary <- DT::renderDataTable({
        req(filtered_overall_summary())

        DT::datatable(filtered_overall_summary(),
          options = list(
            pageLength = 10,
            autoWidth = TRUE,
            width = "100%",
            scrollX = TRUE,
            scrollCollapse = TRUE,
            dom = "Blfrtip",
            buttons = list(
              list(extend = "copy", text = "Copy")
            ),
            columnDefs = list(
              list(
                targets = ncol(filtered_overall_summary()) - 1,
                width = "150px"
              ),
              list(
                targets = ncol(filtered_overall_summary()) - 0,
                width = "200px"
              )
            )
          ),
          extensions = c("Buttons")
        )
      })

      # 添加下载处理器
      output$downloadTotalPredictions <- downloadHandler(
        filename = function() {
          paste("Total_Predictions_", format(Sys.Date(), "%Y-%m-%d"), ".xlsx", sep = "")
        },
        content = function(file) {
          req(filtered_total_predictions())
          openxlsx::write.xlsx(filtered_total_predictions(), file, rowNames = FALSE)
        }
      )

      output$downloadSummaryByEvent <- downloadHandler(
        filename = function() {
          paste("Summary_By_Event_", format(Sys.Date(), "%Y-%m-%d"), ".xlsx", sep = "")
        },
        content = function(file) {
          req(filtered_summary_by_event())
          openxlsx::write.xlsx(filtered_summary_by_event(), file, rowNames = FALSE)
        }
      )

      output$downloadOverallSummary <- downloadHandler(
        filename = function() {
          paste("Overall_Summary_", format(Sys.Date(), "%Y-%m-%d"), ".xlsx", sep = "")
        },
        content = function(file) {
          req(filtered_overall_summary())
          openxlsx::write.xlsx(filtered_overall_summary(), file, rowNames = FALSE)
        }
      )

      # 在 moduleServer 函数内部添加导航处理器
      observeEvent(input$goto_model_tab, {
        session$sendCustomMessage("navigateTab", "Model")
      })
    }
  )
}
