# --- 1. 加载必要的库 ---
# 假设主要库已在app.R中加载
# library(shiny)
# library(shinyjs)

# --- 2. 定义模块UI ---
about_module_ui <- function(id) {
  ns <- NS(id)
  
  tabPanel(
    title = span(title = "About", id = ns("sWtitle")),
    value = "tab1",
    fluidRow(
      column(
        2,
        actionButton(ns("aboutButton"), "About the calculator", class = "btn active", width = "80%"), br(),
        actionButton(ns("citeButton"), "Citing the calculator", class = "btn", width = "80%"), br(),
        actionButton(ns("startButton"), "Get Started", class = "btn btn-primary")
      ),
      column(
        6,
        style = "padding: 0 30px 0 0;",
        div(
          id = ns("aboutbox"),
          p("Welcome to the", strong("WPRO online-calculator for excess deaths in countries"), "!"),
          p("This calculator has been developed by the", a('World Health Organization, Western Pacific Region',
                                                         href = 'https://www.who.int/westernpacific/',
                                                         target = '_blank'), "in conjunction with the", a('Department of Statistics at UCLA',
                                                                                                        href = 'http://statistics.ucla.edu/', target = '_blank'), "."),
          p("This tool aims to estimate the", em("expected all-cause mortality"), "counts for each week or month starting at",
            "January 1, 2020 onward in the counter-factual situation where there had not been a pandemic.",
            "Monitoring the all-cause mortality trends is an important component of multisource surveillance for COVID-19.",
            "The", em("excess mortality"), "is defined to be the difference between the reported counts and expected counts for that week or month.",
            "in the Western Pacific region"),
          p("This interface is useful as part of the dialogue between the WHO WPRO and countries about the",
            "impact of the COVID-19 pandemic on all-cause mortality (ACM) in individual Member countries and territories",
            "in the Western Pacific region"),
          p("Tracking all-cause mortality trends is an important component of multisource surveillance for COVID-19.",
            "Excess deaths have been observed in several countries during the COVID19 pandemic.",
            "Evidence is needed to support timely and dynamic decision-making and policy development."),
          p("This tool will allow easy tracking and analysis of ACM and excess deaths.",
            "It is for use by member countries and does not require the data to be seen by the WHO WPRO.",
            "In fact, all analysis is done on the computer where it is run. An internet connection is only required",
            "to install the software (already done if you are reading this message!)."),
          p("A typical analysis will move sequentially through the tabs at the top of the page",
            "(starting with", actionLink(ns("startButtonLink"), "Get Started"), ".",
            "Click on the help icon at the top of any page for guidance."),
          p("Bug Reports/comments/suggestions/requests? Please share them with us.",
            "They are best submitted through our", a('GitHub site,',
                                                   href = 'https://github.com/WorldHealthOrganization/ACMCalculator',
                                                   target = '_blank'),
            "or by email to us (see", actionLink(ns("helpLink"), "Help"), "tab)."),
          p("This web application",
            "is written with the Shiny framework and development is via GitHub.  More information",
            "on Shiny and our GitHub repository can be found in the",
            "resource links on the right.")
        ),
        div(
          id = ns("citebox"),
          tabsetPanel(
            tabPanel(
              "BibTeX",
              p(strong("ACMCalculator")),
              tags$pre(id = ns('scitation'), '@Manual{handcock:ACMCalculator,
                  title = {ACMCalculator: Software tools for the Statistical Analysis of Excess Mortality from All Cause Mortality Data
                  author = {Mark S. Handcock},
                  year = {2021},
                  address = {Los Angeles, CA},
                  url = {http://hpmrg.org/}
                }'),
              p(strong("ACMCalculator")),
              tags$pre(id = ns('swcitation'), "@Manual{beylerian:ACMCalculator,
                  title = {\\pkg{ACMCalculator}: A Graphical User Interface for Analyzing Excess Mortality from All Cause Mortality Data},
                  author = {Mark S. Handcock},
                  year = {2021},
                  note = {\\proglang{R}~package version~0.1},
                  address = {Los Angeles, CA},
                  url = {https://cran.r-project.org/web/packages/WPROACM/}
                }")
            ),
            tabPanel(
              "Other",
              p(strong("ACMCalculator")),
              tags$pre("Mark S. Handcock (2021). ACMCalculator: A Graphical User Interface for Analyzing Excess Mortality from All Cause Mortality Data. URL http://hpmrg.org"),
              p(strong("ACMCalculator")),
              tags$pre("Mark S. Handcock (2021).
                ACMCalculator: A Graphical User Interface for Analyzing Excess Mortality from All Cause Mortality Data.")
            )
          ),
          p('If you use ACMCalculator, please cite it')
        )
      ),
      column(
        4,
        wellPanel(
          h5(tags$u('Resources')),
          div(a("The calculator on GitHub", href = "https://github.com/WorldHealthOrganization/ACMCalculator",
                target = "_blank")),
          div(a("WPRO all-cause mortality dashboard", href = "https://lynx.wpro.who.int/viz/allcausedeath.asp",
                target = "_blank")),
          div(a("Shiny: a web application framework for R", href = "http://shiny.rstudio.com/",
                target = "_blank"))
        )
      )
    )
  )
}

# --- 3. 定义模块服务器 ---
about_module_server <- function(id, rv) {
  moduleServer(id, function(input, output, session) {
    # 使用命名空间
    ns <- session$ns
    
    # 初始化时隐藏引用信息
    observe({
      shinyjs::hide(id = "citebox")
    })
    
    # 创建一个反应值来存储导航请求
    nav_request <- reactiveVal(NULL)
    
    # 移动到Data面板当用户点击Get Started按钮
    observeEvent(input$startButton, {
      # 使用 JavaScript 直接切换标签页
      session$sendCustomMessage(type = "navigateTab", message = "Data")
    })
    
    # 移动到Data面板当用户点击Get Started链接
    observeEvent(input$startButtonLink, {
      # 使用 JavaScript 直接切换标签页
      session$sendCustomMessage(type = "navigateTab", message = "Data")
    })
    
    # 移动到Help页面当用户点击Help链接按钮
    observeEvent(input$helpLink, {
      # 使用 JavaScript 直接切换标签页
      session$sendCustomMessage(type = "navigateTab", message = "Help and Resources")
    })
    
    # 切换显示About和Cite信息的逻辑
    observeEvent(input$aboutButton, {
      shinyjs::addClass(id = "aboutButton", class = "active")
      shinyjs::removeClass(id = "citeButton", class = "active")
      shinyjs::show(id = "aboutbox")
      shinyjs::hide(id = "citebox")
    })
    
    observeEvent(input$citeButton, {
      shinyjs::removeClass(id = "aboutButton", class = "active")
      shinyjs::addClass(id = "citeButton", class = "active")
      shinyjs::hide(id = "aboutbox")
      shinyjs::show(id = "citebox")
    })
  })
}