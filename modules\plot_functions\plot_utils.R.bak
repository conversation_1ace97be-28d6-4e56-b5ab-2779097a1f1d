# 绘图工具函数
# 包含一些通用的绘图工具函数

# 加载必要的库
library(ggplot2)
library(dplyr)
library(RColorBrewer)

# 创建模型颜色映射
get_model_colors <- function() {
  return(c(
    "Historical Average" = "cyan3",
    "Negative Binomial Regression" = "#E41A1C",
    "General Poisson Model" = "#4DAF4A",
    "Zero Inflated Poisson Model" = "#984EA3",
    "State Space Model" = "#FF7F00",
    "ARIMA/SARIMA Model" = "#A65628"
  ))
}

# 提取事件数据
extract_event_data <- function(data) {
  if ("event_index" %in% names(data) && "event_name" %in% names(data)) {
    event_data <- data %>%
      filter(!is.na(event_index), event_index != "0", !is.na(event_name)) %>%
      group_by(event_index, event_name) %>%
      summarise(
        start_time = min(TimePoint, na.rm = TRUE),
        end_time = max(TimePoint, na.rm = TRUE),
        .groups = 'drop'
      )

    # 创建事件背景的颜色映射
    if (nrow(event_data) > 0) {
      event_colors <- RColorBrewer::brewer.pal(min(nrow(event_data), 9), "Set3")
      names(event_colors) <- unique(event_data$event_name)
      attr(event_data, "event_colors") <- event_colors
    }

    return(event_data)
  } else {
    return(NULL)
  }
}

# 添加通用的图表元素
add_common_plot_elements <- function(p, x_lab = "Date", y_lab = "Value", title = NULL, subtitle = NULL) {
  p <- p +
    labs(
      x = x_lab,
      y = y_lab,
      title = title,
      subtitle = subtitle
    ) +
    theme_minimal() +
    theme(
      legend.position = "right",
      legend.title = element_text(size = 10),
      legend.text = element_text(size = 8),
      axis.title = element_text(size = 10),
      axis.text = element_text(size = 8),
      plot.title = element_text(size = 12, face = "bold"),
      plot.subtitle = element_text(size = 10),
      panel.grid.minor = element_blank(),
      panel.grid.major.x = element_blank(),
      panel.border = element_rect(fill = NA, color = "gray80"),
      plot.margin = margin(10, 10, 10, 10)
    )

  return(p)
}

# 格式化日期轴
format_date_axis <- function(p, date_breaks = "1 month", date_labels = "%b %Y") {
  p <- p +
    scale_x_date(
      breaks = scales::breaks_pretty(n = 10),
      labels = scales::label_date(format = date_labels),
      expand = c(0.02, 0.02)
    )

  return(p)
}

# 格式化数值轴
format_numeric_axis <- function(p, y_min = NULL, y_max = NULL, breaks = scales::breaks_pretty(n = 8)) {
  if (is.null(y_min) || is.null(y_max)) {
    p <- p +
      scale_y_continuous(
        breaks = breaks,
        labels = scales::label_number(big.mark = ","),
        expand = c(0.02, 0.02)
      )
  } else {
    p <- p +
      scale_y_continuous(
        limits = c(y_min, y_max),
        breaks = breaks,
        labels = scales::label_number(big.mark = ","),
        expand = c(0.02, 0.02)
      )
  }

  return(p)
}



    # 绘图函数
    create_acm_plot <- function(data) {
      if (is.null(data) || nrow(data) == 0) {
        return(ggplot() +
          annotate("text", x = 0.5, y = 0.5, label = "No data available for selected filters.") +
          theme_void())
      }

      # Calculate x-axis breaks
      date_range <- range(data$TimePoint, na.rm = TRUE)
      time_breaks <- seq(date_range[1], date_range[2], by = "3 months")

      plot_subtitle <- paste("Sex:", input$selected_sex, "| Age Group:", input$selected_age_group)

      # Calculate the maximum and minimum values for positioning and y-axis limits
      maxpos <- max(data$NO_DEATHS, na.rm = TRUE)
      minpos <- min(data$NO_DEATHS, na.rm = TRUE)
      if ("UPPER_LIMIT" %in% names(data)) {
        maxpos <- max(maxpos, max(data$UPPER_LIMIT, na.rm = TRUE))
      }
      if ("LOWER_LIMIT" %in% names(data)) {
        minpos <- min(minpos, min(data$LOWER_LIMIT, na.rm = TRUE))
      }

      # Adjust y-axis limits to reduce whitespace
      y_min <- max(0, minpos * 0.95) # Start slightly below the minimum, but not below 0
      y_max <- maxpos * 1.1 # Extend slightly above the maximum to accommodate labels

      p <- ggplot(data, aes(x = TimePoint)) +
        geom_line(aes(y = NO_DEATHS, color = "Recorded"), linewidth = 1) +
        geom_line(aes(y = EXP_DEATHS, color = Model, linetype = Model), linewidth = 0.8) +
        {
          if ("LOWER_LIMIT" %in% names(data) && "UPPER_LIMIT" %in% names(data)) {
            geom_ribbon(aes(ymin = LOWER_LIMIT, ymax = UPPER_LIMIT, fill = Model), alpha = 0.2)
          }
        } +
        # Add event annotations with colored blocks instead of vertical lines
        {
          event_data <- data %>%
            filter(event_index != "0" & !is.na(event_index)) %>%
            filter(!is.na(event_name)) %>%
            group_by(event_index, event_name) %>%
            summarise(
              start_time = min(TimePoint),
              end_time = max(TimePoint),
              max_deaths = max(NO_DEATHS),
              .groups = "drop"
            ) %>%
            mutate(
              # Adjust label position to be closer to the data
              label_y = maxpos * 0.98, # Place labels just below the max value
              # Generate different colors for different events
              event_color = factor(event_index)
            )

          if (nrow(event_data) > 0) {
            list(
              # Add colored rectangles for event periods
              geom_rect(
                data = event_data,
                aes(
                  xmin = start_time,
                  xmax = end_time,
                  ymin = y_min,
                  ymax = y_max,
                  fill = event_name
                ),
                alpha = 0.15,
                inherit.aes = FALSE
              ),
              # Add event labels
              geom_text(
                data = event_data,
                aes(
                  x = start_time + (end_time - start_time)/2, # Center the label in the event period
                  y = label_y,
                  label = event_name
                ),
                hjust = 0.5, # Center text horizontally
                vjust = -0.5, # Position above the rectangle
                size = 3,
                color = "darkred"
              ),
              # Add a scale for the event fills
              scale_fill_brewer(
                name = "Events",
                palette = "Pastel1"
              )
            )
          }
        } +
        scale_x_date(
          breaks = time_breaks,
          labels = function(x) format_time_labels(x, data),
          expand = expansion(mult = c(0.02, 0.02))
        ) +
        scale_y_continuous(limits = c(y_min, y_max), expand = expansion(mult = c(0, 0.05))) + # Set dynamic y-axis limits
        scale_color_manual(
          name = "Series",
          values = c("Recorded" = "black",
                    "Historical Average" = "cyan3",
                    "Negative Binomial Regression" = "#E41A1C", # 鲜红色
                    "General Poisson Model" = "#4DAF4A", # 鲜绿色
                    "Zero Inflated Poisson Model" = "#984EA3"), # 深紫色
          breaks = c("Recorded", "Historical Average", "Negative Binomial Regression",
                    "General Poisson Model", "Zero Inflated Poisson Model")
        ) +
        scale_linetype_manual(
          name = "Series",
          values = c("Historical Average" = "dashed",
                    "Negative Binomial Regression" = "dotted",
                    "General Poisson Model" = "longdash",
                    "Zero Inflated Poisson Model" = "twodash"),
          guide = "none"
        ) +
        scale_fill_manual(
          name = "95% CI",
          values = c("Historical Average" = "cyan3",
                    "Negative Binomial Regression" = "#E41A1C", # 鲜红色
                    "General Poisson Model" = "#4DAF4A", # 鲜绿色
                    "Zero Inflated Poisson Model" = "#984EA3"), # 深紫色
          breaks = c("Historical Average", "Negative Binomial Regression",
                    "General Poisson Model", "Zero Inflated Poisson Model")
        ) +
        labs(
          title = "All Cause Mortality: Recorded vs. Expected",
          subtitle = plot_subtitle,
          x = "Time",
          y = "Number of Deaths"
        ) +
        theme_bw() +
        theme(axis.text.x = element_text(angle = 45, hjust = 1), legend.position = "bottom")

      return(p)
    }

    create_ed_plot <- function(data) {
      if (is.null(data) || nrow(data) == 0) {
        return(ggplot() +
          annotate("text", x = 0.5, y = 0.5, label = "No data available for selected filters.") +
          theme_void())
      }

      # 过滤无死亡记录的数据并计算超额死亡
      data <- data %>%
        filter(NO_DEATHS > 0) %>%
        mutate(EXCESS_DEATHS = NO_DEATHS - EXP_DEATHS)

      if (nrow(data) == 0) {
        return(ggplot() +
          annotate("text", x = 0.5, y = 0.5, label = "No data with reported deaths available.") +
          theme_void())
      }

      # 计算置信区间
      if ("LOWER_LIMIT" %in% names(data) && "UPPER_LIMIT" %in% names(data)) {
        data <- data %>% mutate(
          EXCESS_LOWER = NO_DEATHS - UPPER_LIMIT,
          EXCESS_UPPER = NO_DEATHS - LOWER_LIMIT
        )
        ci_caption <- "Note: Error bars represent 95% CI for excess deaths."
      } else {
        ci_caption <- NULL
      }

      # 计算x轴刻度
      date_range <- range(data$TimePoint, na.rm = TRUE)
      n_breaks <- min(20, length(unique(data$TimePoint)))
      time_breaks <- seq(date_range[1], date_range[2], length.out = n_breaks)

      plot_subtitle <- paste("Sex:", input$selected_sex, "| Age Group:", input$selected_age_group)

      p <- ggplot(data, aes(x = TimePoint, y = EXCESS_DEATHS, fill = Model)) +
        geom_col(position = "dodge", width = 30, alpha = 0.7) +
        {
          if ("EXCESS_LOWER" %in% names(data) && "EXCESS_UPPER" %in% names(data)) {
            geom_errorbar(aes(ymin = EXCESS_LOWER, ymax = EXCESS_UPPER),
              position = position_dodge(30),
              width = 5, color = "gray40"
            )
          }
        } +
        # # 修复事件标注部分
        # {
        #   # 检查是否存在事件数据
        #   if ("event_index" %in% names(data) && "event_name" %in% names(data)) {
        #     event_data <- data %>%
        #       filter(!is.na(event_index), event_index != "0", !is.na(event_name)) %>%
        #       group_by(event_index, event_name) %>%
        #       summarise(
        #         start_time = min(TimePoint, na.rm = TRUE),
        #         end_time = max(TimePoint, na.rm = TRUE),
        #         max_excess = max(EXCESS_DEATHS, na.rm = TRUE),
        #         .groups = 'drop'
        #       )

        #     if (nrow(event_data) > 0) {
        #       list(
        #         geom_vline(data = event_data,
        #                   aes(xintercept = start_time),
        #                   linetype = "dashed",
        #                   color = "darkred",
        #                   alpha = 0.5),
        #         geom_vline(data = event_data,
        #                   aes(xintercept = end_time),
        #                   linetype = "dashed",
        #                   color = "darkred",
        #                   alpha = 0.5),
        #         geom_text(data = event_data,
        #                 aes(x = start_time,
        #                     y = max_excess * 0.9,
        #                     label = event_name),
        #                 angle = 90,
        #                 hjust = -0.1,
        #                 size = 3,
        #                 color = "darkred")
        #       )
        #     }
        #   }
        # } +
        geom_hline(yintercept = 0, linetype = "dashed", color = "black") +
        scale_x_date(
          breaks = time_breaks,
          labels = function(x) format_time_labels(x, data),
          expand = expansion(mult = c(0.02, 0.02))
        ) +
        scale_fill_manual(
          name = "Model",
          values = c("Historical Average" = "cyan3", "Negative Binomial Regression" = "indianred")
        ) +
        labs(
          title = "Excess Deaths (Recorded - Expected)",
          subtitle = plot_subtitle,
          caption = ci_caption,
          x = "Time",
          y = "Excess Deaths"
        ) +
        theme_bw() +
        theme(axis.text.x = element_text(angle = 45, hjust = 1), legend.position = "bottom")

      return(p)
    }

    create_ed_line_plot <- function(data) {
      if (is.null(data) || nrow(data) == 0) {
        return(ggplot() +
          annotate("text", x = 0.5, y = 0.5, label = "No data available for selected filters.") +
          theme_void())
      }

      # 过滤无死亡记录的数据并计算超额死亡
      data <- data %>%
        filter(NO_DEATHS > 0) %>%
        mutate(EXCESS_DEATHS = NO_DEATHS - EXP_DEATHS)

      if (nrow(data) == 0) {
        return(ggplot() +
          annotate("text", x = 0.5, y = 0.5, label = "No data with reported deaths available.") +
          theme_void())
      }

      # 计算置信区间
      if ("LOWER_LIMIT" %in% names(data) && "UPPER_LIMIT" %in% names(data)) {
        data <- data %>% mutate(
          EXCESS_LOWER = NO_DEATHS - UPPER_LIMIT,
          EXCESS_UPPER = NO_DEATHS - LOWER_LIMIT
        )
        ci_caption <- "Note: Shaded areas represent 95% CI for excess deaths."
      } else {
        ci_caption <- NULL
      }

      # 计算x轴刻度
      date_range <- range(data$TimePoint, na.rm = TRUE)
      time_breaks <- seq(date_range[1], date_range[2], by = "3 months")

      plot_subtitle <- paste("Sex:", input$selected_sex, "| Age Group:", input$selected_age_group)

      # 首先计算事件数据
      event_data <- data %>%
        filter(event_index != "0" & !is.na(event_index)) %>%
        filter(!is.na(event_name)) %>%
        group_by(event_index, event_name) %>%
        summarise(
          start_time = min(TimePoint),
          end_time = max(TimePoint),
          .groups = "drop"
        )

      # 计算y轴范围
      y_min <- min(0, min(data$EXCESS_DEATHS, na.rm = TRUE))
      y_max <- max(data$EXCESS_DEATHS, na.rm = TRUE) * 1.1

      # 创建事件背景的颜色映射
      if (nrow(event_data) > 0) {
        event_colors <- RColorBrewer::brewer.pal(min(nrow(event_data), 9), "Set3")
        names(event_colors) <- unique(event_data$event_name)
      }

      # 第一步：创建事件背景图层
      event_plot <- ggplot() +
        theme_void() +  # 使用空白主题，只显示事件矩形
        theme(
          plot.margin = margin(0, 0, 0, 0),
          legend.position = "none"
        )

      # 添加事件矩形
      if (nrow(event_data) > 0) {
        for (i in 1:nrow(event_data)) {
          event <- event_data[i, ]
          color <- event_colors[event$event_name]
          event_plot <- event_plot +
            annotate(
              "rect",
              xmin = event$start_time,
              xmax = event$end_time,
              ymin = y_min,
              ymax = y_max,
              fill = color,
              alpha = 0.15  # 增加一点透明度，使背景更清晰
            )
        }
      }

      # 第二步：创建主图层
      main_plot <- ggplot(data, aes(x = TimePoint, y = EXCESS_DEATHS)) +
        geom_line(aes(color = Model), linewidth = 1) +
        {
          if ("EXCESS_LOWER" %in% names(data) && "EXCESS_UPPER" %in% names(data)) {
            geom_ribbon(aes(ymin = EXCESS_LOWER, ymax = EXCESS_UPPER, fill = Model), alpha = 0.2, color = NA, show.legend = FALSE)
          }
        }

      # 第三步：创建事件标签图层
      label_plot <- ggplot() +
        theme_void() +  # 使用空白主题，只显示标签
        theme(
          plot.margin = margin(0, 0, 0, 0),
          legend.position = "none"
        )

      # 添加事件标签
      if (nrow(event_data) > 0) {
        label_plot <- label_plot +
          geom_text(
            data = event_data,
            aes(
              x = start_time + (end_time - start_time)/2,
              y = y_max * 0.95,
              label = event_name
            ),
            hjust = 0.5,
            vjust = -0.5,
            size = 3,
            color = "darkred"
          )
      }

      # 第四步：将三个图层叠加在一起
      # 使用patchwork包的叠加功能
      # 使用patchwork包的叠加功能
      # 注意：这里我们使用特殊的语法来叠加图层
      # 事件背景图层和主图层使用相同的坐标系统，但事件背景图层在最底层
      p <- ggplot() +
        # 首先添加事件开始和结束日期的垂直线
        {
          if (nrow(event_data) > 0) {
            lapply(1:nrow(event_data), function(i) {
              event <- event_data[i, ]
              color <- event_colors[event$event_name]
              c(
                # 开始日期垂直线
                geom_vline(
                  xintercept = event$start_time,
                  color = color,
                  alpha = 0.5,
                  linetype = "solid",
                  linewidth = 1
                ),
                # 结束日期垂直线
                geom_vline(
                  xintercept = event$end_time,
                  color = color,
                  alpha = 0.5,
                  linetype = "solid",
                  linewidth = 1
                ),
                # 水平线和箭头
                annotate(
                  "segment",
                  x = event$start_time,
                  xend = event$end_time,
                  y = y_min + (y_max - y_min) * 0.1, # 在y轴的10%位置
                  yend = y_min + (y_max - y_min) * 0.1,
                  color = color,
                  alpha = 0.7,
                  linewidth = 0.8,
                  arrow = arrow(ends = "both", type = "open", length = unit(0.1, "inches"))
                )
              )
            })
          }
        } +
        # 然后添加主图层
        geom_line(data = data, aes(x = TimePoint, y = EXCESS_DEATHS, color = Model), linewidth = 1) +
        {
          if ("EXCESS_LOWER" %in% names(data) && "EXCESS_UPPER" %in% names(data)) {
            geom_ribbon(data = data, aes(x = TimePoint, y = EXCESS_DEATHS, ymin = EXCESS_LOWER, ymax = EXCESS_UPPER, fill = Model), alpha = 0.2, color = NA)
          }
        } +
        # 最后添加事件标签
        {
          if (nrow(event_data) > 0) {
            geom_text(
              data = event_data,
              aes(
                x = start_time + (end_time - start_time)/2,
                y = y_min + (y_max - y_min) * 0.15, # 在水平箭头线上方一点
                label = event_name
              ),
              hjust = 0.5,
              vjust = -0.5,
              size = 3,
              color = "darkred",
              inherit.aes = FALSE
            )
          }
        }

      # 添加共同的图层元素
      p <- p +
        geom_hline(yintercept = 0, linetype = "dashed", color = "black") +
        scale_x_date(
          breaks = time_breaks,
          labels = function(x) format_time_labels(x, data),
          expand = expansion(mult = c(0.02, 0.02))
        ) +
        scale_color_manual(
          name = "Model",
          values = c("Historical Average" = "cyan3",
                    "Negative Binomial Regression" = "#E41A1C",
                    "General Poisson Model" = "#4DAF4A",
                    "Zero Inflated Poisson Model" = "#984EA3",
                    "BSTS model" = "#FF7F00")
        ) +

        labs(
          title = "Excess Deaths (Line Plot)",
          subtitle = plot_subtitle,
          caption = ci_caption,
          x = "Time",
          y = "Excess Deaths"
        ) +
        theme_bw() +
        theme(axis.text.x = element_text(angle = 45, hjust = 1), legend.position = "bottom")

      return(p)
    }

    create_ep_plot <- function(data) {
      if (is.null(data) || nrow(data) == 0) {
        return(ggplot() +
          annotate("text", x = 0.5, y = 0.5, label = "No data available for selected filters.") +
          theme_void())
      }

      # 过滤无死亡记录或预期死亡为零的数据
      data <- data %>%
        filter(NO_DEATHS > 0, abs(EXP_DEATHS) > 1e-6) %>%
        mutate(
          EXCESS_DEATHS = NO_DEATHS - EXP_DEATHS,
          P_SCORE = (EXCESS_DEATHS / EXP_DEATHS) * 100
        )

      if (nrow(data) == 0) {
        return(ggplot() +
          annotate("text", x = 0.5, y = 0.5, label = "No data with reported deaths and valid expected deaths available.") +
          theme_void())
      }

      # 计算P-Score置信区间
      if ("LOWER_LIMIT" %in% names(data) && "UPPER_LIMIT" %in% names(data)) {
        data <- data %>% mutate(
          P_LOWER = ((NO_DEATHS - UPPER_LIMIT) / EXP_DEATHS) * 100,
          P_UPPER = ((NO_DEATHS - LOWER_LIMIT) / EXP_DEATHS) * 100
        )
        ci_caption <- "Note: Error bars represent 95% CI for P-Score."
      } else {
        ci_caption <- NULL
      }

      # 计算x轴刻度
      date_range <- range(data$TimePoint, na.rm = TRUE)
      n_breaks <- min(20, length(unique(data$TimePoint)))
      time_breaks <- seq(date_range[1], date_range[2], length.out = n_breaks)

      plot_subtitle <- paste("Sex:", input$selected_sex, "| Age Group:", input$selected_age_group)

      # 首先计算事件数据
      event_data <- NULL
      if ("event_index" %in% names(data) && "event_name" %in% names(data)) {
        event_data <- data %>%
          filter(!is.na(event_index), event_index != "0", !is.na(event_name)) %>%
          group_by(event_index, event_name) %>%
          summarise(
            start_time = min(TimePoint, na.rm = TRUE),
            end_time = max(TimePoint, na.rm = TRUE),
            max_pscore = max(P_SCORE, na.rm = TRUE),
            .groups = 'drop'
          )
      }

      # 计算y轴范围
      y_min <- min(0, min(data$P_SCORE, na.rm = TRUE))
      y_max <- max(data$P_SCORE, na.rm = TRUE) * 1.1

      # 创建事件背景的颜色映射
      if (!is.null(event_data) && nrow(event_data) > 0) {
        event_colors <- RColorBrewer::brewer.pal(min(nrow(event_data), 9), "Set3")
        names(event_colors) <- unique(event_data$event_name)
      }

      # 第一步：创建事件背景图层
      event_plot <- ggplot() +
        theme_void() +  # 使用空白主题，只显示事件矩形
        theme(
          plot.margin = margin(0, 0, 0, 0),
          legend.position = "none"
        )

      # 添加事件矩形
      if (!is.null(event_data) && nrow(event_data) > 0) {
        for (i in 1:nrow(event_data)) {
          event <- event_data[i, ]
          color <- event_colors[event$event_name]
          event_plot <- event_plot +
            annotate(
              "rect",
              xmin = event$start_time,
              xmax = event$end_time,
              ymin = y_min,
              ymax = y_max,
              fill = color,
              alpha = 0.15  # 增加一点透明度，使背景更清晰
            )
        }
      }

      # 第二步：创建主图层
      main_plot <- ggplot(data, aes(x = TimePoint, y = P_SCORE)) +
        geom_col(aes(fill = Model), position = "dodge", width = 30, alpha = 0.7) +
        {
          if ("P_LOWER" %in% names(data) && "P_UPPER" %in% names(data)) {
            geom_errorbar(aes(ymin = P_LOWER, ymax = P_UPPER),
              position = position_dodge(30),
              width = 5, color = "gray40"
            )
          }
        }

      # 第三步：创建事件标签图层
      label_plot <- ggplot() +
        theme_void() +  # 使用空白主题，只显示标签
        theme(
          plot.margin = margin(0, 0, 0, 0),
          legend.position = "none"
        )

      # 添加事件标签
      if (!is.null(event_data) && nrow(event_data) > 0) {
        label_plot <- label_plot +
          geom_text(
            data = event_data,
            aes(
              x = start_time + (end_time - start_time)/2,
              y = y_min + (y_max - y_min) * 0.15, # 在水平箭头线上方一点
              label = event_name
            ),
            hjust = 0.5,
            vjust = -0.5,
            size = 3,
            color = "darkred"
          )
      }

      # 使用patchwork包的叠加功能
      # 注意：这里我们使用特殊的语法来叠加图层
      # 事件背景图层和主图层使用相同的坐标系统，但事件背景图层在最底层
      p <- ggplot() +
        # 首先添加事件开始和结束日期的垂直线
        {
          if (!is.null(event_data) && nrow(event_data) > 0) {
            lapply(1:nrow(event_data), function(i) {
              event <- event_data[i, ]
              color <- event_colors[event$event_name]
              c(
                # 开始日期垂直线
                geom_vline(
                  xintercept = event$start_time,
                  color = color,
                  alpha = 0.5,
                  linetype = "solid",
                  linewidth = 1
                ),
                # 结束日期垂直线
                geom_vline(
                  xintercept = event$end_time,
                  color = color,
                  alpha = 0.5,
                  linetype = "solid",
                  linewidth = 1
                ),
                # 水平线和箭头
                annotate(
                  "segment",
                  x = event$start_time,
                  xend = event$end_time,
                  y = y_min + (y_max - y_min) * 0.1, # 在y轴的10%位置
                  yend = y_min + (y_max - y_min) * 0.1,
                  color = color,
                  alpha = 0.7,
                  linewidth = 0.8,
                  arrow = arrow(ends = "both", type = "open", length = unit(0.1, "inches"))
                )
              )
            })
          }
        } +
        # 然后添加主图层
        geom_col(data = data, aes(x = TimePoint, y = P_SCORE, fill = Model), position = "dodge", width = 30, alpha = 0.7) +
        {
          if ("P_LOWER" %in% names(data) && "P_UPPER" %in% names(data)) {
            geom_errorbar(data = data, aes(x = TimePoint, y = P_SCORE, ymin = P_LOWER, ymax = P_UPPER),
              position = position_dodge(30),
              width = 5, color = "gray40"
            )
          }
        } +
        # 最后添加事件标签
        {
          if (!is.null(event_data) && nrow(event_data) > 0) {
            geom_text(
              data = event_data,
              aes(
                x = start_time + (end_time - start_time)/2,
                y = y_max * 0.95,
                label = event_name
              ),
              hjust = 0.5,
              vjust = -0.5,
              size = 3,
              color = "darkred",
              inherit.aes = FALSE
            )
          }
        }

      # 添加共同的图层元素
      p <- p +
        geom_hline(yintercept = 0, linetype = "dashed", color = "black") +
        scale_x_date(
          breaks = time_breaks,
          labels = function(x) format_time_labels(x, data),
          expand = expansion(mult = c(0.02, 0.02))
        ) +
        scale_fill_manual(
          name = "Model",
          values = c("Historical Average" = "cyan3",
                    "Negative Binomial Regression" = "#E41A1C",
                    "General Poisson Model" = "#4DAF4A",
                    "Zero Inflated Poisson Model" = "#984EA3",
                    "BSTS model" = "#FF7F00")
        ) +
        scale_y_continuous(labels = scales::percent_format(scale = 1)) +
        labs(
          title = "P-Score of Excess Deaths (%)",
          subtitle = plot_subtitle,
          caption = ci_caption,
          x = "Time",
          y = "P-Score"
        ) +
        theme_bw() +
        theme(axis.text.x = element_text(angle = 45, hjust = 1), legend.position = "bottom")

      return(p)
    }

    create_ep_line_plot <- function(data) {
      if (is.null(data) || nrow(data) == 0) {
        return(ggplot() +
          annotate("text", x = 0.5, y = 0.5, label = "No data available for selected filters.") +
          theme_void())
      }

      # 过滤无死亡记录或预期死亡为零的数据
      data <- data %>%
        filter(NO_DEATHS > 0, abs(EXP_DEATHS) > 1e-6) %>%
        mutate(
          EXCESS_DEATHS = NO_DEATHS - EXP_DEATHS,
          P_SCORE = (EXCESS_DEATHS / EXP_DEATHS) * 100
        )

      if (nrow(data) == 0) {
        return(ggplot() +
          annotate("text", x = 0.5, y = 0.5, label = "No data with reported deaths and valid expected deaths available.") +
          theme_void())
      }

      # 计算P-Score置信区间
      if ("LOWER_LIMIT" %in% names(data) && "UPPER_LIMIT" %in% names(data)) {
        data <- data %>% mutate(
          P_LOWER = ((NO_DEATHS - UPPER_LIMIT) / EXP_DEATHS) * 100,
          P_UPPER = ((NO_DEATHS - LOWER_LIMIT) / EXP_DEATHS) * 100
        )
        ci_caption <- "Note: Shaded areas represent 95% CI for P-Score."
      } else {
        ci_caption <- NULL
      }

      # 计算x轴刻度
      date_range <- range(data$TimePoint, na.rm = TRUE)
      time_breaks <- seq(date_range[1], date_range[2], by = "3 months")

      plot_subtitle <- paste("Sex:", input$selected_sex, "| Age Group:", input$selected_age_group)

      p <- ggplot(data, aes(x = TimePoint, y = P_SCORE, color = Model)) +
        geom_line(linewidth = 1) +
        {
          if ("P_LOWER" %in% names(data) && "P_UPPER" %in% names(data)) {
            geom_ribbon(aes(ymin = P_LOWER, ymax = P_UPPER, fill = Model), alpha = 0.2, color = NA)
          }
        } +
        # 添加事件标注 - 使用彩色块代替垂直线
        {
          event_data <- data %>%
            filter(event_index != "0" & !is.na(event_index)) %>%
            filter(!is.na(event_name)) %>%
            group_by(event_index, event_name) %>%
            summarise(
              start_time = min(TimePoint),
              end_time = max(TimePoint),
              max_pscore = max(P_SCORE),
              .groups = "drop"
            )

          # 计算y轴范围
          y_min <- min(0, min(data$P_SCORE, na.rm = TRUE))
          y_max <- max(data$P_SCORE, na.rm = TRUE) * 1.1

          if (nrow(event_data) > 0) {
            list(
              # 添加彩色矩形表示事件期间
              geom_rect(
                data = event_data,
                aes(
                  xmin = start_time,
                  xmax = end_time,
                  ymin = y_min,
                  ymax = y_max,
                  fill = event_name
                ),
                alpha = 0.08, # 增加透明度，使背景更淡
                inherit.aes = FALSE
              ),
              # 添加事件标签
              geom_text(
                data = event_data,
                aes(
                  x = start_time + (end_time - start_time)/2, # 将标签居中放置在事件期间
                  y = y_max * 0.95, # Position consistently at 95% of the max y value
                  label = event_name
                ),
                hjust = 0.5, # 水平居中文本
                vjust = -0.5, # 将文本放在矩形上方
                size = 3,
                color = "darkred"
              ),
              # 添加事件填充的比例尺
              scale_fill_brewer(
                name = "Events",
                palette = "Set3" # 使用Set3调色板，提供更多对比色且较浅的颜色
              )
            )
          }
        } +
        geom_hline(yintercept = 0, linetype = "dashed", color = "black") +
        scale_x_date(
          breaks = time_breaks,
          labels = function(x) format_time_labels(x, data),
          expand = expansion(mult = c(0.02, 0.02))
        ) +
        scale_color_manual(
          name = "Model",
          values = c("Historical Average" = "cyan3",
                    "Negative Binomial Regression" = "#E41A1C",
                    "General Poisson Model" = "#4DAF4A",
                    "Zero Inflated Poisson Model" = "#984EA3")
        ) +
        scale_fill_manual(
          name = "95% CI",
          values = c("Historical Average" = "cyan3",
                    "Negative Binomial Regression" = "#E41A1C",
                    "General Poisson Model" = "#4DAF4A",
                    "Zero Inflated Poisson Model" = "#984EA3")
        ) +
        scale_y_continuous(labels = scales::percent_format(scale = 1)) +
        labs(
          title = "P-Score (Line Plot)",
          subtitle = plot_subtitle,
          caption = ci_caption,
          x = "Time",
          y = "P-Score"
        ) +
        theme_bw() +
        theme(axis.text.x = element_text(angle = 45, hjust = 1), legend.position = "bottom")

      return(p)
    }

