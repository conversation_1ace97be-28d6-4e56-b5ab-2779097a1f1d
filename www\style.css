
.navbar .navbar-brand{ padding: 0 12px;font-size: 16px;line-height: 40px; }
.navbar-nav > li > a {padding-top: 0; padding-bottom: 0; line-height: 40px; height: 40px;}
.navbar {min-height:40px; height:40px;}


.codetxt {
  color: #333333;
  background-color: inherit;
}

#sWtitle {
  font-family:Courier;
  font-size:13pt;
}
pre {
  font-size:12px;
}
.form-group {
  margin-bottom: 5px;
}
.shiny-input-container:not {
  width:150px;
  margin:0;
}
div.tool {
  display:inline;
}

div.tool .tip {
    z-index:10;
    display:none;
    padding:2px 3px;
    margin-top:8px;
    margin-left:5px;
    font-size:12px;
    max-width:300px;
}
#statstip {
  margin-top:60px;
}
div.tool:hover .tip{
  display:inline;
  position:absolute;
  color:#333333;
  border:1px solid #CCCCCC;
  border-radius:3px;
  background:#FFFFFF;
  box-shadow: 3px 2px 5px 0 #CCCCCC;
  }
/*div.tool span.tip::before{
  z-index:20;
  content: "";
  width: 0px;
	height: 0px;
	border: 0.8em solid transparent;
	position: absolute;
  left: 5%;
	top: -19px;
	border-bottom: 10px solid #CCCCCC;
} */
div.tool .callout {
  display:none;
}
div.tool:hover .callout {
  display:inline;
  z-index: 20;
  position: absolute;
  left: 5%;
  top: -13px;
}

div.busy {
  position:fixed;
  top: 40%;
  left: 50%;
  margin-top: -100px;
  margin-left: -50px;
  display:none;
  background: rgba(230, 230, 230, .8);
  text-align: center;
  padding-top: 20px;
  padding-left: 30px;
  padding-bottom: 40px;
  padding-right: 30px;
  border-radius: 5px;
  z-index: 50;
}

#aboutButton,
#citeButton,
#startButton {
  margin-bottom: 5px;
  width: 137px;
}

#citebox {
  display: none;
}

#dataleft, #fitleft,
#plotleft, #mcmcleft,
#gofleft, #simleft {
  position: absolute;
  top: 50px;
  right: 65px;
  color: #B4B4B4;
  cursor: pointer;
}
#dataright, #fitright,
#plotright, #mcmcright,
#gofright, #simright {
  position: absolute;
  top: 50px;
  right: 38px;
  color: #B4B4B4;
  cursor: pointer;
}

#dataleft:hover, #fitleft:hover,
#plotleft:hover, #mcmcleft:hover,
#gofleft:hover, #simleft:hover,
#dataright:hover, #fitright:hover,
#plotright:hover, #mcmcright:hover,
#gofright:hover, #simright:hover,
.helper-btn:hover {
  color: #3A3A3A;
}

.helper-btn {
    position:fixed;
    top: 50px;
    right: 10px;
    color: #B4B4B4;
    cursor: pointer;
}

div.helper-box {
  position:fixed;
  top: 90px;
  right: 20px;
  max-width: 150px;
  max-height: 300px;
  display:none;
  background: #FFFFFF;
  text-align: left;
  font-size: 12px;
  padding: 5px;
  border:1px solid #CECED8;
  border-radius: 5px;
  z-index: 10;
}

.helper {
  cursor:pointer;
}

div.smallhelperbox {
  position:absolute;
  z-index: 10;
  display: none;
  width: 200px;
  height: 100px;
  background: #FFFFFF;
  text-align: left;
  font-size: 12px;
  padding: 5px;
  border: 1px solid #CECED8;
  border-radius: 5px;
  z-index: 10;
}

div.mischelperbox {
  position: absolute;
  z-index: 10;
  display: none;
  max-width: 400px;
  max-height: 300px;
  background: #FFFFFF;
  text-align: left;
  font-size: 12px;
  padding: 5px;
  border: 1px solid #CECED8;
  border-radius: 5px;
  z-index: 10;
}


div.warning {
  position: absolute;
  display: inline;
  padding: 5px 10px 5px 5px;
  border: 1px solid;
  font-size: 0.82em;
  z-index: 10;
  background-color: #fff8c4;
  border-color: #f2c779;
}
p.warning {
  position: absolute;
  right: 3px;
  z-index: 12;
  color: #A5A5AD;
}
p.warning:hover {
  color: #737379;
  cursor: pointer;
}
div.error {
  position: fixed;
  bottom: 10px;
  right: 10px;
  display: block;
  padding: 15px 15px 5px 15px;
  max-width: 350px;
  border: 1px solid;
  border-radius: 3px;
  font-size: 0.82em;
  z-index: 10;
  border-color: #D8000C;
  background-color: #FFFFFF;
}

.irs-bar {
  height: 8px;
  top: 25px;
  border-top: 1px solid #076EC3;
  border-bottom: 1px solid #076EC3;
  background: #076EC3;
}
.irs-from, .irs-to, .irs-single {
  color: #fff;
  font-size: 11px;
  line-height: 1.333;
  text-shadow: none;
  padding: 1px 3px;
  background: #076EC3;
  border-radius: 3px;
}
.irs-bar-edge {
  height: 8px;
  top: 25px;
  width: 14px;
  border: 1px solid #076EC3;
  border-right: 0;
  background: #076EC3;
  border-radius: 16px 0 0 16px;
}

#linkbox1,
#linkbox2,
#linkbox3,
#linkbox4,
#mixmxbox,
#cugbox,
#graphlevelbox,
#nodelevelbox {
  display: none;
}

#linktitle1,
#linktitle2,
#linktitle3,
#linktitle4,
#mixmxtitle,
#cugtitle,
#graphleveltitle,
#nodeleveltitle {
  cursor: pointer;
}

.stitle {
  font-size:12px;
  line-height:26px;
  margin-top:10px;
}
.snum {
  font-family:Courier;
  font-size:12px;
  line-height:27px;
  margin-top:10px;
}
.smallselect > .shiny-input-control > .selectize-input {
  padding: 5px;
  font-size: 12pt;
  width: 100px;
  height: 27px;
  line-height: 27px;
}

#savemodel {
  display:inline;
}

#matchingButton,
#allButton,
#nsims {
  margin-bottom:10px;
}

#cugtest {
  margin-bottom:50px;
}

div .mcmcopt {
  display:inline;
  padding: 2px;
  margin-bottom: 5px;
}

.inlineselect {
  width:100px;
  line-height:20px;
  font-size:12px;
}

#MCMCburnin, #MCMCinterval,
#MCMCsamplesize, #simMCMCburnin,
#simMCMCinterval{
  width: 100px;
}
/*.mcmcopt >
  div.form-group.shiny-input-container {
  width: 150px;
}*/
#chooseterm {
  display:inline;
}
#listofterms{
  display:inline;
  width:150px;
}
#matchingorall {
  margin-bottom:2px;
  margin-left:2px;
}
div.placeholder {
  height:150px;
  position:relative;
}
#termdoc {
  background:#FFFFFF;
  border:none;
  padding:10px;
}
#termdocbox {
  position:absolute;
  right:0;
  left:15px;
  margin-right:10px;
  max-height:55px;
  border: 1px solid #CECED8;
  border-radius: 5px;
  overflow:scroll;
  z-index:9;
}
#termexpand {
  position:absolute;
  right:20px;
  width:30px;
  height:20px;
  margin-top:10px;
  color: #A5A5AD;
  z-index:10;
}
#termexpand:hover {
  color: #737379;
}

#uichoosemodel_mcmc,
#uichoosemodel_gof,
#uichoosemodel_sim {
  display:inline;
  padding:4px 6px;
  margin-bottom:5px;
}

.mcmcwarning {
  font-size: 13px;
  padding: 8px 8px 8px 8px;
}
.mcmcwarning > p {
  margin: 0 0 0 0;
}
.topright {
  position: absolute;
  top: 3px;
  right: 3px;
  z-index: 12;
}
.shiftright {
  margin-left:15px;
}
.shiftdown {
  margin-top:30px;
}
.shiftdown25 {
  margin-top:25px;
}
.nwlabel {
  margin:0px 0px 10px;
}

#simsummary,
#simstatslabel,
#simsummary2 {
  border-style: none;
  padding: 0;
  margin: 0;
}
#infsummary {
  border-style: none;
  padding: 0 0 8 0;
  margin: 0;
}
#simcoef,
#simstats,
#simstats2 {
  border-style: none;
  padding: 5px;
  margin: 0 0 0 15px;
}

.round {
  border-radius: 4px;
  border: 1px solid #cccccc;
  padding: 5px 10px;
}
.btn-primary {
  color: #ffffff;
  background-color: #076EC3;
  border-color: #06589C;
}
.btn-primary:hover,
.btn-primary:focus,
.btn-primary.focus,
.btn-primary:active,
.btn-primary.active,
.open > .dropdown-toggle.btn-primary {
  color: #ffffff;
  background-color: #054D88;
  border-color: #02213A;
}
.gray {
  opacity: .5;
}
.form-control[disabled],
.form-control[readonly],
fieldset[disabled] .form-control,
input[disabled] {
  cursor: not-allowed;
  background-color: #e6e6e6;
  opacity: .5;
}
.navbar-default {
  background-color: #3A3A3A;
  border-color: #121212;
}
.navbar-default .navbar-brand {
  color: #ffffff;
}
.navbar-default .navbar-brand:hover,
.navbar-default .navbar-brand:focus {
  color: #ffffff;
  background-color: none;
}
.navbar-default .navbar-text {
  color: #ffffff;
}
.navbar-default .navbar-nav > li > a {
  color: #ffffff;
}
.navbar-default .navbar-nav > li > a:hover,
.navbar-default .navbar-nav > li > a:focus {
  color: #ffffff;
  background-color: #4E4E4E;
  text-shadow: 1px 1px #222222;
}
.navbar-default .navbar-nav > .active > a,
.navbar-default .navbar-nav > .active > a:hover,
.navbar-default .navbar-nav > .active > a:focus {
  color: #ffffff;
  background-color: #4E4E4E;
  text-shadow: 1px 1px #222222;
  box-shadow: inset 0 3px 5px rgba(0,0,0,.125);
}
.navbar-default .navbar-nav > .disabled > a,
.navbar-default .navbar-nav > .disabled > a:hover,
.navbar-default .navbar-nav > .disabled > a:focus {
  color: #cccccc;
  background-color: transparent;
}
.navbar-default .navbar-toggle {
  border-color: transparent;
}
.navbar-default .navbar-toggle:hover,
.navbar-default .navbar-toggle:focus {
  background-color: #4E4E4E;
  text-shadow: 1px 1px #222222;
}
.navbar-default .navbar-toggle .icon-bar {
  background-color: #ffffff;
}
.navbar-default .navbar-collapse,
.navbar-default .navbar-form {
  border-color: #121212;
}
.navbar-default .navbar-nav > .open > a,
.navbar-default .navbar-nav > .open > a:hover,
.navbar-default .navbar-nav > .open > a:focus {
  background-color: #4E4E4E;
  color: #ffffff;
}
@media (max-width: 767px) {
  .navbar-default .navbar-nav .open .dropdown-menu > li > a {
    color: #ffffff;
  }
  .navbar-default .navbar-nav .open .dropdown-menu > li > a:hover,
  .navbar-default .navbar-nav .open .dropdown-menu > li > a:focus {
    color: #ffffff;
    background-color: #222222;
  }
  .navbar-default .navbar-nav .open .dropdown-menu > .active > a,
  .navbar-default .navbar-nav .open .dropdown-menu > .active > a:hover,
  .navbar-default .navbar-nav .open .dropdown-menu > .active > a:focus {
    color: #ffffff;
    background-color: #222222;
  }
  .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a,
  .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:hover,
  .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:focus {
    color: #cccccc;
    background-color: transparent;
  }
}
.navbar-default .navbar-link {
  color: #ffffff;
}
.navbar-default .navbar-link:hover {
  color: #ffffff;
}
.navbar-default .btn-link {
  color: #ffffff;
}
.navbar-default .btn-link:hover,
.navbar-default .btn-link:focus {
  color: #ffffff;
}
.navbar-default .btn-link[disabled]:hover,
fieldset[disabled] .navbar-default .btn-link:hover,
.navbar-default .btn-link[disabled]:focus,
fieldset[disabled] .navbar-default .btn-link:focus {
  color: #cccccc;
}