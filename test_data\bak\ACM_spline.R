

# Helper function: initialize output
initialize_output <- function(src, YRS2020, wm_ident, l_period) {
    out_spline <- src %>%
        filter(YEAR >= YRS2020) %>%
        mutate(
            ESTIMATE = NO_DEATHS,
            LOWER_LIMIT = NO_DEATHS,
            UPPER_LIMIT = NO_DEATHS,
            EXCESS_DEATHS = NO_DEATHS,
            WM_IDENTIFIER = wm_ident
        )
    return(out_spline)
}

# Helper function: final processing
finalize_output <- function(out_spline, wm_ident) {
    # write.csv(out_spline, "calculate_final_output.csv")
    out_spline <- out_spline %>%
        mutate(NO_DEATHS = as.numeric(NO_DEATHS)) %>%
        mutate(ESTIMATE = as.numeric(ESTIMATE)) %>%
        mutate(EXCESS_DEATHS = NO_DEATHS - ESTIMATE) %>%
        melt(id.vars = c("COUNTRY", "ISO3", "WM_IDENTIFIER", "YEAR", "PERIOD",
                        "SEX", "AGE_GROUP", "AREA", "CAUSE", "DATE_TO_SPECIFY_WEEK",
                        "SE_IDENTIFIER", "LOWER_LIMIT", "UPPER_LIMIT", "EXCESS_DEATHS",
                        "event_name", "event_index")) %>%
        rename(SERIES = variable, NO_DEATHS = value) %>%
        mutate(SERIES = case_when(
            SERIES == "NO_DEATHS" ~ "Current deaths",
            SERIES == "ESTIMATE" ~ "Cyclical spline"
        )) %>%
        filter(!is.na(SERIES), YEAR != "") %>%
        group_by(COUNTRY, ISO3, WM_IDENTIFIER, YEAR, PERIOD,
                 SEX, AGE_GROUP, AREA, CAUSE, DATE_TO_SPECIFY_WEEK,
                 SE_IDENTIFIER, LOWER_LIMIT, UPPER_LIMIT, EXCESS_DEATHS,
                 event_name, event_index) %>%
        reframe(
            EXP_DEATHS = NO_DEATHS[SERIES == "Cyclical spline"],
            NO_DEATHS = NO_DEATHS[SERIES == "Current deaths"],
            SERIES = "Cyclical spline"
        ) %>%
        arrange(AREA, SEX, AGE_GROUP, YEAR, PERIOD)

    return(out_spline)
}

# Helper function: model fitting and prediction
fit_and_predict <- function(patt_src, hist_src, l_period, YRS2020, nys, DOM) {
    t.start <- Sys.time()

    write.xlsx(patt_src, "patt_src.xlsx")
    write.xlsx(hist_src, "hist_src.xlsx")

    # Set parameters based on period type (month/week)
    if (l_period > 51) {
        # Weekly data
        day <- cumsum(c(0, rep(7, 52)))[patt_src$PERIOD] + 3.5
        aDATE <- cumsum(c(0, rep(365, nys)))[patt_src$YEAR - 2014] + day
        num.cycle <- 52
        len.cycle <- 7
        hist_src$logdays <- log(7)
        src_pandemic <- patt_src %>%
            mutate(loc_DATE = aDATE, logdays = log(7))

        # Fit model
        fit <- tryCatch({
            mgcv::gam(NO_DEATHS ~ offset(logdays) + YEAR +
                     s(PERIOD, bs = "cc", fx = TRUE, k = 9),
                     knots = list(PERIOD = c(0, num.cycle)),
                     method = "REML", family = nb(), data = hist_src)
        }, error = function(e) {
            warning("Model fitting failed for pattern: ",
                   paste(unique(patt_src$SEX, patt_src$AGE_GROUP,
                                patt_src$AREA, patt_src$CAUSE), collapse = " "))
            return(NULL)
        })
        if (is.null(fit)) {
            return(data.frame())
        }
    } else {
        # Monthly data
        day <- cumsum(c(0, DOM))[patt_src$PERIOD] + 15
        aDATE <- cumsum(c(0, rep(365, nys)))[patt_src$YEAR - 2014] + day
        num.cycle <- 12
        len.cycle <- 30
        days <- DOM[hist_src$PERIOD]
        days[14] <- 29  # Adjust for leap year
        hist_src$logdays <- log(days)

        days <- DOM[patt_src$PERIOD]
        days[14] <- 29  # Feb 2016
        if (length(days) > 61) days[62] <- 29  # Feb 2020
        src_pandemic <- patt_src %>%
            mutate(loc_DATE = aDATE, logdays = log(days))

        # Fit model
        fit <- tryCatch({
            mgcv::gam(NO_DEATHS ~ offset(logdays) + YEAR +
                     s(PERIOD, bs = "cc", fx = TRUE, k = 5),
                     knots = list(PERIOD = c(0, num.cycle)),
                     method = "REML", family = nb(), data = hist_src)
        }, error = function(e) {
            warning("Model fitting failed for pattern: ",
                   paste(unique(patt_src$SEX, patt_src$AGE_GROUP,
                                patt_src$AREA, patt_src$CAUSE), collapse = " "))
            return(NULL)
        })
        if (is.null(fit)) return(data.frame())
    }

    # Model prediction
    estim <- mgcv::predict.gam(fit, newdata = src_pandemic, se.fit = TRUE)
    theta <- fit$family$getTheta(TRUE)

    # Optimize predictions using matrix operations
    set.seed(1)
    a <- matrix(rnorm(n = 1000 * length(estim$fit),
                     mean = estim$fit, sd = estim$se.fit),
               ncol = 1000)
    estim.median <- apply(a, 1, function(x) {
        mean(qnbinom(mu = exp(x), size = theta, p = 0.5))
    })
    estim.lower <- apply(a, 1, function(x) {
        mean(qnbinom(mu = exp(x), size = theta, p = 0.025))
    })
    estim.upper <- apply(a, 1, function(x) {
        mean(qnbinom(mu = exp(x), size = theta, p = 0.975))
    })

    # Ensure non-negative values
    estim.median[estim.median < 0] <- 0
    estim.lower[estim.lower < 0] <- 0
    estim.upper[estim.upper < 0] <- 0

    # Standardization
    estim.median.std <- len.cycle * estim.median / exp(src_pandemic$logdays)
    estim.lower.std <- len.cycle * estim.lower / exp(src_pandemic$logdays)
    estim.upper.std <- len.cycle * estim.upper / exp(src_pandemic$logdays)

    message("Pattern processing time: ", round(difftime(Sys.time(), t.start, units = "secs"), 1), " sec")

    return(list(
        src_pandemic = src_pandemic,
        estim.median = estim.median,
        estim.lower = estim.lower,
        estim.upper = estim.upper
    ))
}

# Helper function: update output
update_output <- function(out_spline, model_results, j, pattern, l_period,
                         patt_src, hist_src, year_predict) {
    # Debug output
    message("[DEBUG] Updating output for pattern: ", pattern)
    message("[DEBUG] Model results class: ", class(model_results))
    message("[DEBUG] Model results contains: ", paste(names(model_results), collapse = ", "))
    flush.console()

    # Extract model results
    src_pandemic <- model_results$src_pandemic
    estim.median <- model_results$estim.median
    estim.lower <- model_results$estim.lower
    estim.upper <- model_results$estim.upper

    # Check if model results are valid
    if (is.null(src_pandemic) || is.null(estim.median) || is.null(estim.lower) || is.null(estim.upper)) {
        message("[DEBUG] Warning: Invalid model results for pattern: ", pattern)
        return(out_spline)
    }

    nyear_predict <- length(year_predict)
    n_pat <- length(unique(paste(out_spline$SEX, out_spline$AGE_GROUP,
                                out_spline$AREA, out_spline$CAUSE)))

    # Create result data frame
    result_df <- out_spline[0, ]

    for (iyear_predict in seq_along(year_predict)) {
        y <- year_predict[iyear_predict]
        for (k in 0:(l_period - 1)) {
            a <- src_pandemic$YEAR == y & src_pandemic$PERIOD == (k + 1)
            y_temp <- y
            while (!any(a) && y_temp >= 2017) {
                y_temp <- y_temp - 1
                a <- src_pandemic$YEAR == y_temp & src_pandemic$PERIOD == (k + 1)
            }
            if (!any(a)) {
                a <- src_pandemic$YEAR == y & src_pandemic$PERIOD == k
            }

            # Get current pattern records
            current_records <- out_spline[
                out_spline$YEAR == y &
                out_spline$PERIOD == (k + 1) &
                paste(out_spline$SEX, out_spline$AGE_GROUP,
                      out_spline$AREA, out_spline$CAUSE) == pattern,
            ]

            if (nrow(current_records) > 0) {
                current_records$ESTIMATE <- estim.median[a]
                current_records$LOWER_LIMIT <- estim.lower[a]
                current_records$UPPER_LIMIT <- estim.upper[a]
                result_df <- rbind(result_df, current_records)
            }
        }
    }

    return(result_df)
}

# New function: calculate summary statistics by event and pattern
calculate_summary_by_event <- function(out_spline) {
    # write.csv(out_spline, "calculate_summary_by_event.csv")
    # Calculate summary statistics for each event and pattern
    summary_by_event <- out_spline %>%
        mutate(NO_DEATHS = as.numeric(NO_DEATHS)) %>%
        mutate(EXCESS_DEATHS = as.numeric(EXCESS_DEATHS)) %>%
        mutate(EXP_DEATHS = as.numeric(EXP_DEATHS)) %>%
        mutate(UPPER_LIMIT = as.numeric(UPPER_LIMIT)) %>%
        mutate(LOWER_LIMIT = as.numeric(LOWER_LIMIT)) %>%
        filter(event_index != "0" & !is.na(NO_DEATHS) & !is.na(event_index)) %>%
        group_by(COUNTRY, ISO3, WM_IDENTIFIER, SEX, AGE_GROUP, AREA, CAUSE, event_index, event_name) %>%
        reframe(
            TOTAL_DEATHS = sum(NO_DEATHS, na.rm = TRUE),
            TOTAL_EXPECTED = sum(EXP_DEATHS, na.rm = TRUE),
            TOTAL_EXCESS = sum(NO_DEATHS - EXP_DEATHS, na.rm = TRUE),
            # Reverse engineer standard error from confidence intervals and calculate total variance
            TOTAL_SE = sqrt(sum(((UPPER_LIMIT - LOWER_LIMIT) / (1.96*2))^2, na.rm = TRUE))
        ) %>%
        mutate(
            EXCESS_LOWER = TOTAL_EXCESS - 1.96 * TOTAL_SE,
            EXCESS_UPPER = TOTAL_EXCESS + 1.96 * TOTAL_SE,
            P_SCORE = 100 * TOTAL_EXCESS / TOTAL_EXPECTED
        )

    return(summary_by_event)
}

# New function: calculate overall summary statistics by pattern
calculate_overall_summary <- function(out_spline) {
    # Calculate overall summary statistics for each pattern
    # write.csv(out_spline, "calculate_overall_summary.csv")
    overall_summary <- out_spline %>%
        mutate(NO_DEATHS = as.numeric(NO_DEATHS)) %>%
        mutate(EXP_DEATHS = as.numeric(EXP_DEATHS)) %>%
        mutate(EXCESS_DEATHS = as.numeric(EXCESS_DEATHS)) %>%
        mutate(UPPER_LIMIT = as.numeric(UPPER_LIMIT)) %>%
        mutate(LOWER_LIMIT = as.numeric(LOWER_LIMIT)) %>%
        filter(event_index != "0" & !is.na(NO_DEATHS) & !is.na(event_index)) %>%
        group_by(COUNTRY, ISO3, WM_IDENTIFIER, SEX, AGE_GROUP, AREA, CAUSE) %>%
        reframe(
            TOTAL_DEATHS = sum(NO_DEATHS, na.rm = TRUE),
            TOTAL_EXPECTED = sum(EXP_DEATHS, na.rm = TRUE),
            TOTAL_EXCESS = sum(NO_DEATHS - EXP_DEATHS, na.rm = TRUE),
            # Reverse engineer standard error from confidence intervals and calculate total variance
            TOTAL_SE = sqrt(sum(((UPPER_LIMIT - LOWER_LIMIT) / (2*1.96))^2, na.rm = TRUE))
        ) %>%
        mutate(
            EXCESS_LOWER = TOTAL_EXCESS - 1.96 * TOTAL_SE,
            EXCESS_UPPER = TOTAL_EXCESS + 1.96 * TOTAL_SE,
            P_SCORE = 100 * TOTAL_EXCESS / TOTAL_EXPECTED,
            event_name = "All Events",
            event_index = "999"
        )

    return(overall_summary)
}

# Helper function: serial processing with model selection
process_serial_with_model <- function(n_pat, patterns, src, l_period, YRS2020, nys, DOM, out_spline, wm_ident, model_type) {
    message("Using serial processing with model type: ", model_type)
    flush.console()
    start_time <- Sys.time()

    # Process each pattern
    for (j in 1:n_pat) {
        pattern <- patterns[j]
        message("Processing pattern ", j, "/", n_pat, ": ", pattern)
        flush.console()

        # Extract pattern data
        pattern_parts <- strsplit(pattern, " ")[[1]]
        patt_src <- src %>%
            filter(SEX == pattern_parts[1],
                   AGE_GROUP == pattern_parts[2],
                   AREA == pattern_parts[3],
                   CAUSE == pattern_parts[4])

        # Skip if insufficient data
        if (nrow(patt_src) < 10) {
            message("Skipping pattern due to insufficient data")
            flush.console()
            next
        }

        # Prepare historical data
        hist_src <- patt_src %>%
             filter(event_index == "0")

        # Skip if insufficient historical data
        if (nrow(hist_src) < 10) {
            message("Skipping pattern due to insufficient historical data")
            flush.console()
            next
        }

        # Call appropriate model function based on model_type
        message("[DEBUG] Model type in process_serial_with_model: ", model_type)
        flush.console()

        if (model_type == "poisson") {
            message("[DEBUG] Calling fit_and_predict_poisson for pattern: ", pattern)
            flush.console()
            model_results <- fit_and_predict_poisson(patt_src, hist_src, l_period, nys, DOM)
            message("[DEBUG] fit_and_predict_poisson returned: ", paste(names(model_results), collapse = ", "))
            flush.console()
        } else if (model_type == "zip") {
            message("[DEBUG] Calling fit_and_predict_zip for pattern: ", pattern)
            flush.console()
            model_results <- fit_and_predict_zip(patt_src, hist_src, l_period, nys, DOM)
            message("[DEBUG] fit_and_predict_zip returned: ", paste(names(model_results), collapse = ", "))
            flush.console()
        } else {
            # Default to negative binomial
            message("[DEBUG] Using negative binomial model for type: ", model_type)
            flush.console()
            model_results <- fit_and_predict(patt_src, hist_src, l_period, nys, DOM)
            message("[DEBUG] fit_and_predict returned: ", paste(names(model_results), collapse = ", "))
            flush.console()
        }

        # Skip if model failed
        if (length(model_results) == 0 || is.data.frame(model_results)) {
            message("Model failed for this pattern")
            flush.console()
            next
        }

        # Update output with model results
        out_spline <- update_output(out_spline, model_results, j, pattern, l_period,
                                   patt_src, hist_src, YRS2020)
    }

    message("Total processing time: ", round(difftime(Sys.time(), start_time, units = "mins"), 1), " minutes")
    flush.console()

    # Note: Summary statistics will be calculated in fcn.spline function
    return(out_spline)
}

# New function: serial processing
process_serial <- function(n_pat, patterns, src, l_period, YRS2020, nys, DOM, out_spline, wm_ident) {
    message("Using serial processing...")
    start_time <- Sys.time()

    # Create result list
    results <- list()

    for (j in seq_len(n_pat)) {
        pattern <- patterns[j]
        message("Processing pattern in serial: ", pattern)

        patt_src <- src[paste(src$SEX, src$AGE_GROUP, src$AREA, src$CAUSE) == pattern, ]
        hist_src <- patt_src[patt_src$event_index == "0", ]

        if (sum(hist_src$NO_DEATHS, na.rm = TRUE) <= 200) {
            message("Skipping insufficient data pattern: ", pattern)
            next
        }

        model_results <- fit_and_predict(patt_src, hist_src, l_period, YRS2020, nys, DOM)
        if (is.null(model_results)) {
            message("Model fitting failed, skipping pattern: ", pattern)
            next
        }

        # Update output data frame and store in result list
        result <- update_output(out_spline, model_results, j, pattern, l_period,
                              patt_src, hist_src, year_predict = sort(unique(out_spline$YEAR)))

        if (!is.null(result) && nrow(result) > 0) {
            results[[j]] <- result
            message("Successfully processed pattern: ", pattern, " (", nrow(result), " rows)")
        }
    }

    # Combine all non-empty results
    if (length(results) > 0) {
        out_spline <- do.call(rbind, results[!sapply(results, is.null)])
        message("Successfully combined results from ", length(results[!sapply(results, is.null)]), " patterns")
    } else {
        warning("No successfully processed patterns!")
        return(data.frame())
    }

    end_time <- Sys.time()
    total_time <- difftime(end_time, start_time, units = "mins")
    message("Serial processing completed, total time: ", round(total_time, 2), " minutes")

    # Final processing
    out_spline <- finalize_output(out_spline, wm_ident)
    message("\n[fcn.spline] Serial processing completed")
    flush.console()
    return(out_spline)
}

# 移除并行处理函数，只保留串行处理

fit_and_predict_poisson <- function(patt_src, hist_src, l_period, nys, DOM) {
    message("Using General Poisson Model...")
    flush.console()

    src_pandemic <- patt_src
    hist_src$logdays <- log(hist_src$DAYS)
    src_pandemic$logdays <- log(src_pandemic$DAYS)

    if (l_period > 51) {
        # Weekly data
        num.cycle <- 52
        len.cycle <- 7
        num_k <- 9
    } else {
        num.cycle <- 12
        len.cycle <- 30
        num_k <- 5
    }

    t.start <- Sys.time()

    # Fit model with Poisson family
    fit <- tryCatch(
        {
            mgcv::gam(
                NO_DEATHS ~ offset(log(DAYS)) + YEAR +
                    s(PERIOD, bs = "cc", fx = TRUE, k = num_k),
                knots = list(PERIOD = c(0, num.cycle)),
                method = "REML", family = poisson(), data = hist_src
            )
        },
        error = function(e) {
            warning(
                "Zero-Inflated Poisson model fitting failed for pattern: ",
                paste(unique(
                    patt_src$SEX, patt_src$AGE_GROUP,
                    patt_src$AREA, patt_src$CAUSE
                ), collapse = " ")
            )
            return(NULL)
        }
    )
    if (is.null(fit)) {
        return(data.frame())
    }

    # Model prediction
    estim <- mgcv::predict.gam(fit, newdata = patt_src, se.fit = TRUE)

    # For Poisson, we use qpois instead of qnbinom
    set.seed(1)
    a <- matrix(
        rnorm(
            n = 1000 * length(estim$fit),
            mean = estim$fit, sd = estim$se.fit
        ),
        ncol = 1000
    )
    estim.median <- apply(a, 1, function(x) {
        mean(qpois(lambda = exp(x), p = 0.5))
    })
    estim.lower <- apply(a, 1, function(x) {
        mean(qpois(lambda = exp(x), p = 0.025))
    })
    estim.upper <- apply(a, 1, function(x) {
        mean(qpois(lambda = exp(x), p = 0.975))
    })

    # Ensure non-negative values
    estim.median[estim.median < 0] <- 0
    estim.lower[estim.lower < 0] <- 0
    estim.upper[estim.upper < 0] <- 0

    # Standardization
    estim.median.std <- len.cycle * estim.median / exp(src_pandemic$logdays)
    estim.lower.std <- len.cycle * estim.lower / exp(src_pandemic$logdays)
    estim.upper.std <- len.cycle * estim.upper / exp(src_pandemic$logdays)

    message("Poisson pattern processing time: ", round(difftime(Sys.time(), t.start, units = "secs"), 1), " sec")
    flush.console()
    return(list(
        src_pandemic = src_pandemic,
        estim.median = estim.median,
        estim.lower = estim.lower,
        estim.upper = estim.upper
    ))
}


fit_and_predict_zip <- function(patt_src, hist_src, l_period, nys, DOM) {
    message("Using Zero-Inflated Poisson Model...")
    flush.console()

    t.start <- Sys.time()

    # 数据预处理，与 fit_and_predict_poisson 保持一致
    src_pandemic <- patt_src
    hist_src$logdays <- log(hist_src$DAYS)
    src_pandemic$logdays <- log(src_pandemic$DAYS)

    # 周期参数
    if (l_period > 51) {
        num.cycle <- 52
        len.cycle <- 7
        num_k <- 9
    } else {
        num.cycle <- 12
        len.cycle <- 30
        num_k <- 5
    }

    # 检查零膨胀适用性
    check_zip_applicability <- function(data) {
        zero_ratio <- mean(data$NO_DEATHS == 0)
        message("Zero proportion: ", round(zero_ratio * 100, 1), "%")
        if (zero_ratio < 0.05) {
            warning("Low zero proportion, switching to regular Poisson")
            return(FALSE)
        }
        return(TRUE)
    }

    if (!check_zip_applicability(hist_src)) {
        family <- poisson()
    } else {
        family <- ziP(theta = log(mean(hist_src$NO_DEATHS == 0)))
    }

    # 模型拟合
    fit <- tryCatch(
        {
            mgcv::gam(
                NO_DEATHS ~ offset(logdays) + YEAR +
                    s(PERIOD, bs = "cc", fx = TRUE, k = num_k),
                knots = list(PERIOD = c(0, num.cycle)),
                method = "REML",
                family = family,
                data = hist_src
            )
        },
        error = function(e) {
            warning("GAM fitting failed: ", conditionMessage(e))
            return(NULL)
        }
    )

    if (is.null(fit) || !fit$converged || any(is.na(coef(fit)))) {
        warning("Model failed to converge, using fallback")
        return(data.frame())
    }

    # 预测过程：修复条件判断
    if (family$family == "poisson") { # 使用 family$family 检查
        message("Running Poisson prediction branch...")
        estim <- mgcv::predict.gam(fit, newdata = src_pandemic, type = "link", se.fit = TRUE)

        # 调试信息
        message("Range of estim$fit: ", min(estim$fit), " to ", max(estim$fit))
        message("Range of estim$se.fit: ", min(estim$se.fit), " to ", max(estim$se.fit))

        set.seed(1)
        a <- matrix(
            rnorm(n = 1000 * length(estim$fit), mean = estim$fit, sd = estim$se.fit),
            ncol = 1000
        )
        message("Range of a before cap: ", min(a), " to ", max(a))
        a <- pmin(a, 700) # 防止溢出
        lambda_sim <- exp(a)
        message("Range of lambda_sim: ", min(lambda_sim), " to ", max(lambda_sim))

        estim.median <- apply(lambda_sim, 1, function(x) {
            mean(qpois(p = 0.5, lambda = x))
        })
        estim.lower <- apply(lambda_sim, 1, function(x) {
            mean(qpois(p = 0.025, lambda = x))
        })
        estim.upper <- apply(lambda_sim, 1, function(x) {
            mean(qpois(p = 0.975, lambda = x))
        })
    } else {
        message("Running ZIP prediction branch...")
        estim <- mgcv::predict.gam(fit, newdata = src_pandemic, type = "response", se.fit = TRUE)
        estim$fit <- pmax(estim$fit, 1e-8)
        estim$se.fit <- pmin(pmax(estim$se.fit, 1e-8), 10)
        set.seed(1)
        a <- matrix(
            rnorm(1000 * length(estim$fit), estim$fit, estim$se.fit),
            ncol = 1000
        ) %>% exp()
        quantile_calc <- function(x, probs) {
            tryCatch(
                quantile(x, probs, na.rm = TRUE),
                error = function(e) {
                    warning("Quantile calculation failed: ", conditionMessage(e))
                    rep(NA, length(probs))
                }
            )
        }
        estim.median <- apply(a, 1, quantile_calc, probs = 0.5)
        estim.lower <- apply(a, 1, quantile_calc, probs = 0.025)
        estim.upper <- apply(a, 1, quantile_calc, probs = 0.975)
    }

    # 确保非负值
    estim.median[estim.median < 0] <- 0
    estim.lower[estim.lower < 0] <- 0
    estim.upper[estim.upper < 0] <- 0

    # Standardization
    estim.median.std <- len.cycle * estim.median / exp(src_pandemic$logdays)
    estim.lower.std <- len.cycle * estim.lower / exp(src_pandemic$logdays)
    estim.upper.std <- len.cycle * estim.upper / exp(src_pandemic$logdays)

    message(
        "Zero-Inflated Poisson pattern processing time: ",
        round(difftime(Sys.time(), t.start, units = "secs"), 1), " sec"
    )
    flush.console()

    return(list(
        src_pandemic = src_pandemic,
        estim.median = estim.median,
        estim.lower = estim.lower,
        estim.upper = estim.upper
    ))
}


# 简化主函数，移除并行处理相关代码
fcn.spline <- function(src, model_type = "Negative Binomial") {
    message("\n[fcn.spline] Starting calculation with model type: ", model_type, "...")
    flush.console()
    # Preload necessary libraries
    required_packages <- c("dplyr", "reshape2", "readxl", "stringr", "mgcv")
    lapply(required_packages, require, character.only = TRUE)

    # Constant definitions
    DOM <- c(31, 28, 31, 30, 31, 28, 31, 31, 30, 31, 30, 31)
    MOY <- c("Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec")
    YRS2020 <- 2015

    # Data preprocessing
    src <- src %>%
        filter(PERIOD <= 52) %>%
        arrange(SEX, AGE_GROUP, AREA, CAUSE, YEAR, PERIOD) %>%
        mutate(NO_DEATHS = as.numeric(NO_DEATHS))

    nys <- length(unique(src$YEAR))
    max_period <- max(src$PERIOD, na.rm = TRUE)
    wm_ident <- ifelse(max_period == 12, "Month", "Week")
    l_period <- ifelse(max_period == 12, 12, 52)

    # Calculate dates
    src <- calculate_dates(src, max_period, nys, DOM, MOY)

    # Initialize output data frame
    out_spline <- initialize_output(src, YRS2020, wm_ident, l_period)

    # Get unique patterns
    patterns <- src %>%
        select(SEX, AGE_GROUP, AREA, CAUSE) %>%
        distinct() %>%
        mutate(patterns = paste(SEX, AGE_GROUP, AREA, CAUSE)) %>%
        pull(patterns)

    n_pat <- length(patterns)

    # Choose processing method based on model type
    message("[DEBUG] Processing with model_type: ", model_type)
    flush.console()

    if (model_type == "poisson") {
        # Use Poisson model
        message("[DEBUG] Using Poisson model processing")
        flush.console()
        out_spline <- process_serial_with_model(n_pat, patterns, src, l_period, YRS2020, nys, DOM, out_spline, wm_ident, model_type)
    } else if (model_type == "zip") {
        # Use Zero-Inflated Poisson model
        message("[DEBUG] Using Zero-Inflated Poisson model processing")
        flush.console()
        out_spline <- process_serial_with_model(n_pat, patterns, src, l_period, YRS2020, nys, DOM, out_spline, wm_ident, model_type)
    } else {
        # Default: Use Negative Binomial model
        message("[DEBUG] Using Negative Binomial model processing")
        flush.console()
        out_spline <- process_serial(n_pat, patterns, src, l_period, YRS2020, nys, DOM, out_spline, wm_ident)
    }

    # Calculate summary statistics
    message("\n[DEBUG] Calculating summary statistics for model type: ", model_type)
    flush.console()

    # Check if out_spline is valid
    if (is.data.frame(out_spline) && nrow(out_spline) > 0) {
        summary_by_event <- calculate_summary_by_event(out_spline)
        overall_summary <- calculate_overall_summary(out_spline)

        # Combine all summary results
        all_summaries <- bind_rows(summary_by_event, overall_summary)

        # Add attributes
        attr(out_spline, "summary_by_event") <- summary_by_event
        attr(out_spline, "overall_summary") <- overall_summary
        attr(out_spline, "all_summaries") <- all_summaries

        message("[DEBUG] Summary statistics calculated successfully")
        message("[DEBUG] summary_by_event rows: ", nrow(summary_by_event))
        message("[DEBUG] overall_summary rows: ", nrow(overall_summary))
    } else {
        message("[DEBUG] Warning: out_spline is not a valid data frame or is empty")
        message("[DEBUG] out_spline class: ", class(out_spline))
        if (is.data.frame(out_spline)) {
            message("[DEBUG] out_spline dimensions: ", nrow(out_spline), " x ", ncol(out_spline))
        }
    }

    return(out_spline)
}

# # Use parallel processing (default)
# res2 <- fcn.spline(result)

# # Use serial processing
# res2 <- fcn.spline(result, parallel = FALSE)

# # Save detailed results
# write.xlsx(res2, "z:/2025年工作文件/2025年3月5日ACMcalculator2/WPROACM/data/event_excess_death2.xlsx")

# # Save summary results
# write.xlsx(
#     attr(res2, "all_summaries"),
#     "z:/2025年工作文件/2025年3月5日ACMcalculator2/WPROACM/data/event_excess_death2_summary.xlsx"
# )


# # Save detailed results
# write.xlsx(rv_data, "Z:/2025年工作文件/2025年3月25日ACMcal/raw_data.xlsx")
# write.csv(rv_data, "Z:/2025年工作文件/2025年3月25日ACMcal/raw_data.csv")
# write.xlsx(res2, "Z:/2025年工作文件/2025年3月25日ACMcal/event_excess_death2.xlsx")

# # Save summary results
# write.xlsx(
#     attr(res2, "all_summaries"),
#     "z:/2025年工作文件/2025年3月5日ACMcalculator2/WPROACM/data/event_excess_death2_summary.xlsx"
# )
