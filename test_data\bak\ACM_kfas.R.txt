# 加载必要的包
library(KFAS)
library(dplyr)
library(lubridate)
library(ISOweek)

# 加载公共函数（假设与 NB 和 BSTS 模型相同）
source("modules/common_functions.R")

# KFAS 模型拟合和预测函数
fit_and_predict_kfas <- function(patt_src, hist_src, l_period) {
    t.start <- Sys.time()

    # 创建一个空的结果对象，在出错时返回
    empty_result <- list(
        src_pandemic = patt_src,
        estim.median = numeric(nrow(patt_src)),
        estim.lower = numeric(nrow(patt_src)),
        estim.upper = numeric(nrow(patt_src)),
        forecast_result = NULL
    )

    # 获取年份数量
    nys <- length(unique(patt_src$YEAR))

    # 初始化 src_pandemic
    src_pandemic <- patt_src

    # 根据周期类型设置参数（月/周）
    if (l_period > 51) {
        # 周数据
        num.cycle <- 52
        len.cycle <- 7
        # 添加天数列
        hist_src$DAYS <- 7
        hist_src$logdays <- log(hist_src$DAYS)
        src_pandemic <- patt_src %>%
            mutate(DAYS = 7, logdays = log(7))
    } else {
        # 月数据
        num.cycle <- 12
        len.cycle <- 30
        # 添加天数列
        DOM <- c(31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31)
        hist_src$DAYS <- DOM[hist_src$PERIOD]
        # 闰年调整
        hist_src$DAYS[hist_src$PERIOD == 2 & hist_src$YEAR %% 4 == 0] <- 29
        hist_src$logdays <- log(hist_src$DAYS)

        # 确保 src_pandemic 有 DAYS 列
        if (!"DAYS" %in% names(src_pandemic)) {
            src_pandemic$DAYS <- DOM[src_pandemic$PERIOD]
            # 闰年调整
            src_pandemic$DAYS[src_pandemic$PERIOD == 2 & src_pandemic$YEAR %% 4 == 0] <- 29
        }
        src_pandemic <- src_pandemic %>%
            mutate(logdays = log(DAYS))
    }

    # 构造时间序列（支持月和周数据）
    if (l_period > 51) {
        # 周数据：使用 ISOweek 包计算日期
        hist_src <- hist_src %>%
            arrange(YEAR, PERIOD) %>%
            mutate(
                iso_week = sprintf("%d-W%02d-4", YEAR, PERIOD),
                date = ISOweek2date(iso_week)
            )

        src_pandemic <- src_pandemic %>%
            arrange(YEAR, PERIOD) %>%
            mutate(
                iso_week = sprintf("%d-W%02d-4", YEAR, PERIOD),
                date = ISOweek2date(iso_week)
            )
    } else {
        # 月数据：将日期设置为每月的 15 号
        hist_src <- hist_src %>%
            arrange(YEAR, PERIOD) %>%
            mutate(date = as.Date(paste(YEAR, PERIOD, "15", sep = "-")))

        src_pandemic <- src_pandemic %>%
            arrange(YEAR, PERIOD) %>%
            mutate(date = as.Date(paste(YEAR, PERIOD, "15", sep = "-")))
    }

    # 动态确定未来预测的周期数（n.ahead）
    message("[DEBUG] 计算预测期数...")

    # 检查日期是否有效
    if (any(is.na(hist_src$date))) {
        message("[WARNING] hist_src 中有缺失的日期值")
    }
    if (any(is.na(src_pandemic$date))) {
        message("[WARNING] src_pandemic 中有缺失的日期值")
    }

    # 安全获取最大日期
    if (nrow(hist_src) == 0 || all(is.na(hist_src$date))) {
        message("[ERROR] hist_src 没有有效的日期")
        return(empty_result)
    }
    if (nrow(src_pandemic) == 0 || all(is.na(src_pandemic$date))) {
        message("[ERROR] src_pandemic 没有有效的日期")
        return(empty_result)
    }

    # 获取所有年份，确保包括 2015 年以后的所有数据
    all_years <- sort(unique(src_pandemic$YEAR))
    min_year <- min(all_years)
    message("[INFO] 数据包含的年份: ", paste(all_years, collapse = ", "))
    message("[INFO] 最早年份: ", min_year)

    last_hist_date <- max(hist_src$date, na.rm = TRUE)
    last_pandemic_date <- max(src_pandemic$date, na.rm = TRUE)

    message("[DEBUG] last_hist_date = ", as.character(last_hist_date))
    message("[DEBUG] last_pandemic_date = ", as.character(last_pandemic_date))

    # 确保预测覆盖整个时间段，包括 2015 年以后的所有数据
    # 计算需要预测的总周期数
    total_periods <- nrow(src_pandemic)
    message("[INFO] 总周期数: ", total_periods)

    # 设置预测期数为总周期数，确保覆盖所有时间点
    n.ahead <- total_periods
    message("[INFO] 设置预测期数为总周期数: ", n.ahead)

    # 旧的逻辑，仅作为参考
    if (FALSE) {
        # 根据周期类型计算预测期数
        if (l_period > 51) {
            # 周数据
            n.ahead <- tryCatch({
                as.numeric(difftime(last_pandemic_date, last_hist_date, units = "weeks"))
            }, error = function(e) {
                message("[ERROR] 计算周数差异失败: ", e$message)
                return(0)
            })
        } else {
            # 月数据
            n.ahead <- tryCatch({
                interval(last_hist_date, last_pandemic_date) %/% months(1)
            }, error = function(e) {
                message("[ERROR] 计算月数差异失败: ", e$message)
                return(0)
            })
        }

        # 确保 n.ahead 是非负整数
        n.ahead <- max(0, floor(n.ahead))
    }

    message("[DEBUG] 预测期数 n.ahead = ", n.ahead)

    # 构造时间序列对象
    hist_ts <- ts(hist_src$NO_DEATHS, frequency = num.cycle)

    # 构建 KFAS 状态空间模型
    message("[DEBUG] 构建 KFAS 状态空间模型...")

    # 检查时间序列数据
    if (length(hist_ts) < 2 * num.cycle) {
        message("[WARNING] 历史数据不足两个完整周期，可能导致模型不稳定")
    }

    # 检查数据是否包含 NA 或负值
    if (any(is.na(hist_ts))) {
        message("[WARNING] 历史数据包含 NA 值，将替换为 0")
        hist_ts[is.na(hist_ts)] <- 0
    }

    if (any(hist_ts < 0)) {
        message("[WARNING] 历史数据包含负值，将替换为 0")
        hist_ts[hist_ts < 0] <- 0
    }

    # 构建模型
    message("[DEBUG] 构建改进的 KFAS 状态空间模型...")
    model <- tryCatch({
        # 使用改进的模型，平衡复杂性和稳定性
        # 使用一阶趋势项，但允许其有变化（Q = NA 而不是固定值）
        # 使用季节性组件，并允许其有小的变化
        SSModel(
            hist_ts ~ SSMtrend(1, Q = NA) + SSMseasonal(num.cycle, Q = NA),
            H = NA
        )
    }, error = function(e) {
        message("[ERROR] 构建改进的 KFAS 模型失败: ", e$message)
        message("[INFO] 尝试使用基本模型...")
        tryCatch({
            SSModel(
                hist_ts ~ SSMtrend(1, Q = 0) + SSMseasonal(num.cycle, Q = 0),
                H = NA
            )
        }, error = function(e2) {
            message("[ERROR] 基本模型也失败: ", e2$message)
            return(NULL)
        })
    })

    if (is.null(model)) {
        message("[ERROR] 无法创建 KFAS 模型")
        return(empty_result)
    }

    # 拟合模型
    message("[DEBUG] 拟合 KFAS 模型...")
    fit <- tryCatch(
        {
            fitSSM(model, inits = c(1, 1, 1))
        },
        error = function(e) {
            message("[ERROR] KFAS 模型拟合失败: ", e$message)
            return(NULL)
        }
    )

    if (is.null(fit)) {
        message("[ERROR] KFAS 模型拟合失败")
        return(empty_result)
    }

    message("[DEBUG] KFAS 模型拟合成功")

    # 分别计算拟合结果和预测结果
    message("[DEBUG] 分别计算拟合结果和预测结果...")

    # 1. 获取拟合结果（对历史数据的拟合）
    message("[DEBUG] 获取拟合结果...")
    fit_result <- tryCatch({
        fitted(fit$model, filtered = TRUE)
    }, error = function(e) {
        message("[ERROR] 获取拟合结果失败: ", e$message)
        return(NULL)
    })

    if (is.null(fit_result)) {
        message("[ERROR] 获取拟合结果失败，返回空结果")
        return(empty_result)
    }

    # 获取拟合结果的方差（如果有）
    fit.median <- as.numeric(fit_result)

    # 检查方差属性是否存在
    if (!is.null(attr(fit_result, "variance"))) {
        message("[DEBUG] 使用模型提供的方差计算拟合结果的置信区间")
        variance <- as.numeric(attr(fit_result, "variance"))
        # 确保方差是正数
        variance[variance < 0] <- 0
        fit.lower <- fit.median - 1.96 * sqrt(variance)
        fit.upper <- fit.median + 1.96 * sqrt(variance)
    } else {
        message("[WARNING] 模型没有提供方差，使用默认方法计算拟合结果的置信区间")
        # 使用默认方法计算置信区间（假设标准误差为估计值的 10%）
        fit.lower <- fit.median * 0.8
        fit.upper <- fit.median * 1.2
    }

    # 2. 获取预测结果（对未来数据的预测）
    message("[DEBUG] 获取预测结果...")

    # 计算预测期数，即未来数据的长度
    future_periods <- length(which(src_pandemic$date > last_hist_date))
    if (future_periods <= 0) {
        message("[INFO] 没有未来数据需要预测，使用默认预测期数")
        future_periods <- 12  # 默认预测 12 个周期
    }
    message("[DEBUG] 预测未来 ", future_periods, " 个周期")

    # 检查是否有未来数据可用于改进预测
    future_data <- src_pandemic[src_pandemic$date > last_hist_date, ]

    pred_result <- tryCatch({
        # 标准预测
        std_pred <- predict(fit$model, n.ahead = future_periods, interval = "confidence", level = 0.95)

        if (nrow(future_data) > 0 && !all(is.na(future_data$NO_DEATHS))) {
            message("[INFO] 使用未来数据的季节性模式改进预测")

            # 提取季节性模式
            if (num.cycle > 1) {
                # 计算季节性因子
                seasonal_pattern <- aggregate(hist_src$NO_DEATHS, by=list(hist_src$PERIOD), FUN=mean, na.rm=TRUE)
                names(seasonal_pattern) <- c("PERIOD", "avg_deaths")

                # 标准化季节性因子
                seasonal_factor <- seasonal_pattern$avg_deaths / mean(seasonal_pattern$avg_deaths)

                # 应用季节性因子到预测结果
                for (i in 1:nrow(std_pred)) {
                    if (i <= nrow(future_data)) {
                        period_idx <- (future_data$PERIOD[i] - 1) %% num.cycle + 1
                        std_pred[i, "fit"] <- std_pred[i, "fit"] * seasonal_factor[period_idx]
                        std_pred[i, "lwr"] <- std_pred[i, "lwr"] * seasonal_factor[period_idx]
                        std_pred[i, "upr"] <- std_pred[i, "upr"] * seasonal_factor[period_idx]
                    }
                }
            }
        }

        std_pred
    }, error = function(e) {
        message("[ERROR] 预测失败: ", e$message)
        message("[INFO] 尝试使用标准预测方法...")
        tryCatch({
            predict(fit$model, n.ahead = future_periods, interval = "confidence", level = 0.95)
        }, error = function(e2) {
            message("[ERROR] 标准预测也失败: ", e2$message)
            return(NULL)
        })
    })

    # 处理预测结果
    if (is.null(pred_result) || nrow(pred_result) == 0) {
        message("[WARNING] 预测失败或没有预测结果")
        pred.median <- numeric(0)
        pred.lower <- numeric(0)
        pred.upper <- numeric(0)
    } else {
        # 提取预测结果
        if (ncol(pred_result) >= 3) {
            message("[DEBUG] 提取预测结果，长度: ", nrow(pred_result))
            pred.median <- as.numeric(pred_result[, "fit"])
            pred.lower <- as.numeric(pred_result[, "lwr"])
            pred.upper <- as.numeric(pred_result[, "upr"])
        } else {
            message("[WARNING] 预测结果格式不正确，使用默认方法计算置信区间")
            pred.median <- as.numeric(pred_result)
            # 使用默认方法计算置信区间（假设标准误差为估计值的 10%）
            pred.lower <- pred.median * 0.8
            pred.upper <- pred.median * 1.2
        }
    }

    # 将拟合结果和预测结果映射到 src_pandemic 的长度
    message("[DEBUG] 将拟合结果和预测结果映射到 src_pandemic...")
    message("[DEBUG] src_pandemic 行数 = ", nrow(src_pandemic))
    message("[DEBUG] 拟合结果长度 = ", length(fit.median))
    message("[DEBUG] 预测结果长度 = ", length(pred.median))

    # 检查日期列是否有效
    if (any(is.na(src_pandemic$date))) {
        message("[WARNING] src_pandemic 中有缺失的日期值")
        # 将NA日期设置为最小日期
        src_pandemic$date[is.na(src_pandemic$date)] <- min(src_pandemic$date, na.rm = TRUE)
    }

    # 初始化输出向量
    full_estim.median <- rep(NA, nrow(src_pandemic))
    full_estim.lower <- rep(NA, nrow(src_pandemic))
    full_estim.upper <- rep(NA, nrow(src_pandemic))

    # 分别处理历史数据和未来数据
    hist_indices <- which(src_pandemic$date <= last_hist_date)
    future_indices <- which(src_pandemic$date > last_hist_date)

    message("[DEBUG] 历史数据索引数量 = ", length(hist_indices))
    message("[DEBUG] 未来数据索引数量 = ", length(future_indices))

    # 1. 处理历史数据部分（拟合结果）
    if (length(hist_indices) > 0) {
        # 确保我们不会超出数组边界
        n_hist <- min(length(hist_indices), length(fit.median))
        if (n_hist > 0) {
            message("[DEBUG] 填充历史数据部分（拟合结果）")
            full_estim.median[hist_indices[1:n_hist]] <- fit.median[1:n_hist]
            full_estim.lower[hist_indices[1:n_hist]] <- fit.lower[1:n_hist]
            full_estim.upper[hist_indices[1:n_hist]] <- fit.upper[1:n_hist]
        }
    }

    # 2. 处理未来数据部分（预测结果）
    if (length(future_indices) > 0) {
        if (length(pred.median) == 0) {
            # 如果没有预测结果，使用历史数据的最后一个值填充
            message("[INFO] 没有预测结果，使用历史数据的最后一个值填充")
            if (length(hist_indices) > 0 && length(fit.median) > 0) {
                last_median <- fit.median[length(fit.median)]
                last_lower <- fit.lower[length(fit.lower)]
                last_upper <- fit.upper[length(fit.upper)]

                full_estim.median[future_indices] <- last_median
                full_estim.lower[future_indices] <- last_lower
                full_estim.upper[future_indices] <- last_upper
            }
        } else if (length(future_indices) <= length(pred.median)) {
            # 如果未来索引少于或等于预测结果
            message("[DEBUG] 填充未来数据部分（预测结果）")
            full_estim.median[future_indices] <- pred.median[1:length(future_indices)]
            full_estim.lower[future_indices] <- pred.lower[1:length(future_indices)]
            full_estim.upper[future_indices] <- pred.upper[1:length(future_indices)]
        } else {
            # 如果未来索引多于预测结果，填充可用的部分
            message("[DEBUG] 填充未来数据部分（预测结果），但预测结果不足")
            n_future <- length(pred.median)
            full_estim.median[future_indices[1:n_future]] <- pred.median
            full_estim.lower[future_indices[1:n_future]] <- pred.lower
            full_estim.upper[future_indices[1:n_future]] <- pred.upper

            # 剩余部分使用最后一个预测值填充
            if (length(future_indices) > n_future) {
                message("[INFO] 预测结果不足，使用最后一个预测值填充剩余 ", length(future_indices) - n_future, " 个时间点")

                if (n_future > 0) {
                    last_median <- pred.median[n_future]
                    last_lower <- pred.lower[n_future]
                    last_upper <- pred.upper[n_future]

                    remaining_indices <- future_indices[(n_future + 1):length(future_indices)]
                    full_estim.median[remaining_indices] <- last_median
                    full_estim.lower[remaining_indices] <- last_lower
                    full_estim.upper[remaining_indices] <- last_upper
                }
            }
        }
    }

    # 确保所有年份都有预测值，包括 2015 年以后的所有数据
    # 检查是否有任何缺失的预测值
    missing_indices <- which(is.na(full_estim.median))
    if (length(missing_indices) > 0) {
        message("[INFO] 有 ", length(missing_indices), " 个时间点缺失预测值，使用已有预测值的平均值填充")

        # 使用已有预测值的平均值填充
        valid_indices <- which(!is.na(full_estim.median))
        if (length(valid_indices) > 0) {
            avg_median <- mean(full_estim.median[valid_indices], na.rm = TRUE)
            avg_lower <- mean(full_estim.lower[valid_indices], na.rm = TRUE)
            avg_upper <- mean(full_estim.upper[valid_indices], na.rm = TRUE)

            full_estim.median[missing_indices] <- avg_median
            full_estim.lower[missing_indices] <- avg_lower
            full_estim.upper[missing_indices] <- avg_upper
        }
    }

    # 确保非负值（死亡人数不能为负）
    full_estim.median[full_estim.median < 0] <- 0
    full_estim.lower[full_estim.lower < 0] <- 0
    full_estim.upper[full_estim.upper < 0] <- 0

    message("模式处理时间: ", round(difftime(Sys.time(), t.start, units = "secs"), 1), " 秒")

    # 输出格式与 fit_and_predict_nb() 一致
    return(list(
        src_pandemic = src_pandemic,
        estim.median = full_estim.median,
        estim.lower = full_estim.lower,
        estim.upper = full_estim.upper
    ))
}

# 更新输出函数（统一处理所有预测结果）
update_output_kfas <- function(out_data, model_results, pattern, year_predict) {
    message("[DEBUG] update_output_kfas 开始处理模式: ", pattern)

    # 检查模型结果是否存在
    if (is.null(model_results)) {
        message("[ERROR] 模型结果为 NULL")
        return(out_data)
    }

    src_pandemic <- model_results$src_pandemic
    estim.median <- model_results$estim.median
    estim.lower <- model_results$estim.lower
    estim.upper <- model_results$estim.upper

    if (is.null(src_pandemic) || is.null(estim.median) || is.null(estim.lower) || is.null(estim.upper)) {
        message("[WARNING] 模式 ", pattern, " 的模型结果缺失必要组件")
        return(out_data)
    }

    # 检查数据结构
    message("[DEBUG] src_pandemic 行数: ", nrow(src_pandemic))
    message("[DEBUG] estim.median 长度: ", length(estim.median))
    message("[DEBUG] 模式分解: ", paste(strsplit(pattern, " ")[[1]], collapse = ", "))

    # 分解模式
    pattern_parts <- tryCatch({
        strsplit(pattern, " ")[[1]]
    }, error = function(e) {
        message("[ERROR] 无法分解模式: ", e$message)
        return(c("Total", "Total", "Total", "Total"))
    })

    # 确保模式有四个部分
    if (length(pattern_parts) < 4) {
        message("[WARNING] 模式部分不足 4 个，添加缺失的部分")
        pattern_parts <- c(pattern_parts, rep("Total", 4 - length(pattern_parts)))
    }

    # 初始化结果数据框
    result_df <- out_data[0, ]

    # 获取周期数和预测年份数
    l_period <- max(out_data$PERIOD, na.rm = TRUE)
    nyear_predict <- length(year_predict)

    message("[DEBUG] 周期数: ", l_period, ", 预测年份数: ", nyear_predict)
    message("[DEBUG] 预测年份: ", paste(year_predict, collapse = ", "))

    # 处理每个年份和周期
    for (iyear_predict in 1:nyear_predict) {
        y <- year_predict[iyear_predict]
        for (k in 1:l_period) {
            # 在 src_pandemic 中查找匹配的记录
            a <- src_pandemic$YEAR == y & src_pandemic$PERIOD == k

            # 如果没有匹配的记录，跳过
            if (sum(a) == 0) {
                next
            }

            # 在 out_data 中查找匹配的记录
            current_records <- tryCatch({
                out_data[
                    out_data$SEX == pattern_parts[1] &
                    out_data$AGE_GROUP == pattern_parts[2] &
                    out_data$AREA == pattern_parts[3] &
                    out_data$CAUSE == pattern_parts[4] &
                    out_data$YEAR == y &
                    out_data$PERIOD == k,
                ]
            }, error = function(e) {
                message("[ERROR] 查询当前记录失败: ", e$message)
                return(out_data[0, ])
            })

            # 如果找到匹配的记录并且有匹配的 src_pandemic 记录，则更新记录
            if (nrow(current_records) > 0 && sum(a) > 0) {
                # 安全获取索引
                a_indices <- which(a)
                if (length(a_indices) > 0) {
                    # 只使用第一个匹配的索引，避免多个匹配导致的问题
                    idx <- a_indices[1]
                    if (idx <= length(estim.median) && idx <= length(estim.lower) && idx <= length(estim.upper)) {
                        current_records$ESTIMATE <- estim.median[idx]
                        current_records$LOWER_LIMIT <- estim.lower[idx]
                        current_records$UPPER_LIMIT <- estim.upper[idx]
                        result_df <- rbind(result_df, current_records)
                    } else {
                        message("[WARNING] 索引超出范围: ", idx, " > ",
                                min(length(estim.median), length(estim.lower), length(estim.upper)))
                    }
                }
            }
        }
    }

    message("[DEBUG] update_output_kfas 完成，返回行数: ", nrow(result_df))
    return(result_df)
}

# KFAS 模型主函数
fcn_kfas <- function(src) {
    message("\n[fcn_kfas] 开始 KFAS 模型计算...")
    flush.console()
    start_time <- Sys.time()

    # 检查输入数据
    if (is.null(src) || nrow(src) == 0) {
        message("[ERROR] 输入数据为空")
        return(data.frame())
    }

    message("[INFO] 输入数据行数: ", nrow(src))

    # 预处理数据
    src <- tryCatch({
        src %>%
            filter(PERIOD <= 52) %>%
            arrange(SEX, AGE_GROUP, AREA, CAUSE, YEAR, PERIOD) %>%
            mutate(NO_DEATHS = as.numeric(NO_DEATHS))
    }, error = function(e) {
        message("[ERROR] 数据预处理失败: ", e$message)
        return(data.frame())
    })

    if (is.null(src) || nrow(src) == 0) {
        message("[ERROR] 预处理后数据为空")
        return(data.frame())
    }

    # 获取基本参数
    nys <- length(unique(src$YEAR))
    max_period <- max(src$PERIOD, na.rm = TRUE)
    wm_ident <- ifelse(max_period == 12, "Month", "Week")
    l_period <- ifelse(max_period == 12, 12, 52)

    message("[INFO] 年份数: ", nys, ", 最大周期: ", max_period, ", 标识符: ", wm_ident)

    # 计算日期 - 使用自定义方法而不是 calculate_dates 函数
    message("[INFO] 计算日期...")

    # 检查是否已经有日期列
    if ("date" %in% names(src)) {
        message("[INFO] 数据已包含 date 列，使用现有日期")
    } else {
        message("[INFO] 数据中没有 date 列，创建日期")

        # 如果没有日期列，根据周期类型创建
        if (max_period == 12) {
            # 月数据
            src$date <- as.Date(paste(src$YEAR, src$PERIOD, "15", sep = "-"))
        } else {
            # 周数据 - 使用 ISOweek 包
            dates <- character(nrow(src))
            for (i in 1:nrow(src)) {
                dates[i] <- sprintf("%d-W%02d-4", src$YEAR[i], src$PERIOD[i])
            }
            src$date <- as.Date(ISOweek2date(dates))
        }
    }

    # 检查是否已经有 DAYS 列
    if (!"DAYS" %in% names(src)) {
        message("[INFO] 添加 DAYS 列")
        if (max_period == 12) {
            # 月数据
            DOM <- c(31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31)
            src$DAYS <- DOM[src$PERIOD]
            # 闰年调整
            src$DAYS[src$PERIOD == 2 & src$YEAR %% 4 == 0] <- 29
        } else {
            # 周数据
            src$DAYS <- 7
        }
    }

    # 检查日期列是否有效
    if (any(is.na(src$date))) {
        message("[WARNING] 有缺失的日期值，将使用插值")
        # 简单的插值方法：使用最小日期
        min_date <- min(src$date, na.rm = TRUE)
        src$date[is.na(src$date)] <- min_date
    }

    # 初始化输出数据框
    out_data <- tryCatch({
        initialize_output(src, wm_ident, l_period)
    }, error = function(e) {
        message("[ERROR] 初始化输出数据框失败: ", e$message)
        return(data.frame())
    })

    if (is.null(out_data) || nrow(out_data) == 0) {
        message("[ERROR] 初始化输出数据框为空")
        return(data.frame())
    }

    # 获取唯一模式
    patterns <- tryCatch({
        src %>%
            select(SEX, AGE_GROUP, AREA, CAUSE) %>%
            distinct() %>%
            mutate(patterns = paste(SEX, AGE_GROUP, AREA, CAUSE)) %>%
            pull(patterns)
    }, error = function(e) {
        message("[ERROR] 获取唯一模式失败: ", e$message)
        return(character(0))
    })

    n_pat <- length(patterns)
    if (n_pat == 0) {
        message("[ERROR] 没有可用的模式")
        return(data.frame())
    }

    message("[INFO] 找到 ", n_pat, " 个模式")

    # 初始化结果列表
    results <- list()

    # 处理每个模式
    for (j in 1:n_pat) {
        pattern <- patterns[j]
        message("[INFO] 处理模式 ", j, "/", n_pat, ": ", pattern)

        # 提取模式数据
        pattern_parts <- tryCatch({
            strsplit(pattern, " ")[[1]]
        }, error = function(e) {
            message("[ERROR] 分解模式失败: ", e$message)
            return(NULL)
        })

        if (is.null(pattern_parts) || length(pattern_parts) < 4) {
            message("[WARNING] 模式格式不正确，跳过")
            next
        }

        patt_src <- tryCatch({
            src[
                src$SEX == pattern_parts[1] &
                src$AGE_GROUP == pattern_parts[2] &
                src$AREA == pattern_parts[3] &
                src$CAUSE == pattern_parts[4],
            ]
        }, error = function(e) {
            message("[ERROR] 提取模式数据失败: ", e$message)
            return(NULL)
        })

        if (is.null(patt_src)) {
            message("[WARNING] 无法提取模式数据，跳过")
            next
        }

        # 如果数据不足，则跳过
        if (nrow(patt_src) < 10) {
            message("[WARNING] 由于数据不足，跳过模式 (", nrow(patt_src), " 行)")
            next
        }

        # 准备历史数据
        hist_src <- tryCatch({
            patt_src[patt_src$event_index == "0", ]
        }, error = function(e) {
            message("[ERROR] 提取历史数据失败: ", e$message)
            return(NULL)
        })

        if (is.null(hist_src)) {
            message("[WARNING] 无法提取历史数据，跳过")
            next
        }

        # 如果历史数据不足，则跳过
        if (nrow(hist_src) < 10) {
            message("[WARNING] 由于历史数据不足，跳过模式 (", nrow(hist_src), " 行)")
            next
        }

        # 模型拟合和预测
        message("[INFO] 开始模型拟合和预测...")
        model_results <- tryCatch({
            fit_and_predict_kfas(patt_src, hist_src, l_period)
        }, error = function(e) {
            message("[ERROR] 模型拟合和预测失败: ", e$message)
            return(data.frame())
        })

        # 如果模型失败，则跳过
        if (is.null(model_results) || (is.data.frame(model_results) && nrow(model_results) == 0)) {
            message("[WARNING] 此模式的模型失败")
            next
        }

        # 更新输出
        message("[INFO] 更新输出结果...")
        year_predict <- sort(unique(out_data$YEAR))
        result <- tryCatch({
            update_output_kfas(out_data, model_results, pattern, year_predict)
        }, error = function(e) {
            message("[ERROR] 更新输出失败: ", e$message)
            return(NULL)
        })

        if (is.null(result)) {
            message("[WARNING] 更新输出结果为空，跳过")
            next
        }

        # 存储结果
        if (nrow(result) > 0) {
            results[[j]] <- result
            message("[INFO] 成功处理模式: ", pattern, " (", nrow(result), " 行)")
        } else {
            message("[WARNING] 模式处理结果为空: ", pattern)
        }
    }

    # 合并结果
    message("[INFO] 合并所有模式的结果...")
    out_data <- NULL
    if (length(results) > 0) {
        # 过滤掉空结果
        valid_results <- results[!sapply(results, is.null)]
        if (length(valid_results) > 0) {
            out_data <- tryCatch({
                do.call(rbind, valid_results)
            }, error = function(e) {
                message("[ERROR] 合并结果失败: ", e$message)
                return(NULL)
            })
            message("[INFO] 成功合并 ", length(valid_results), " 个模式的结果")
        } else {
            message("[WARNING] 没有有效的模式结果可合并")
        }
    } else {
        message("[WARNING] 没有成功处理的模式!")
    }

    if (is.null(out_data) || nrow(out_data) == 0) {
        message("[ERROR] 最终结果为空")
        return(data.frame())
    }

    # 处理结果
    message("[INFO] 处理最终结果...")
    if (!is.null(out_data)) {
        out_data <- tryCatch({
            processed_data <- process_model_results(out_data, "KFAS")
            # 转换为 tbl_df
            if (!is.null(processed_data)) {
                message("[INFO] 将结果转换为 tbl_df")
                processed_data <- dplyr::as_tibble(processed_data)
            }
            processed_data
        }, error = function(e) {
            message("[ERROR] 处理最终结果失败: ", e$message)
            # 返回原始结果转换为 tbl_df
            dplyr::as_tibble(out_data)
        })
    }

    end_time <- Sys.time()
    total_time <- difftime(end_time, start_time, units = "mins")
    message("[INFO] KFAS 模型计算完成，总时间: ", round(total_time, 2), " 分钟")
    flush.console()

    return(out_data)
}

# 计算超额死亡
calculate_excess_deaths <- function(recorded_deaths, expected_deaths) {
    excess_deaths <- ifelse(is.na(recorded_deaths), NA, recorded_deaths - expected_deaths$ESTIMATE)
    excess_deaths_lower <- ifelse(is.na(recorded_deaths), NA, recorded_deaths - expected_deaths$UPPER_LIMIT)
    excess_deaths_upper <- ifelse(is.na(recorded_deaths), NA, recorded_deaths - expected_deaths$LOWER_LIMIT)
    return(data.frame(
        Excess_Deaths = excess_deaths,
        Excess_Deaths_Lower = excess_deaths_lower,
        Excess_Deaths_Upper = excess_deaths_upper
    ))
}

# 主函数：计算超额死亡
calculate_acm_excess_kfas <- function(src) {
    # 运行 KFAS 模型
    kfas_results <- fcn_kfas(src)

    # 提取实际死亡人数
    recorded_deaths <- src %>%
        arrange(SEX, AGE_GROUP, AREA, CAUSE, YEAR, PERIOD) %>%
        pull(NO_DEATHS)

    # 计算超额死亡
    if (!is.null(kfas_results)) {
        excess_deaths <- calculate_excess_deaths(recorded_deaths, kfas_results)
        kfas_results <- cbind(kfas_results, excess_deaths)
    }

    return(kfas_results)
}