# 加载必要的包
library(KFAS)
library(dplyr)
library(lubridate)
library(ISOweek)

# 加载公共函数（假设与 NB 和 BSTS 模型相同）
source("modules/common_functions.R")

# KFAS 模型拟合和预测函数
# 修改后的 fit_and_predict_kfas 函数
# 修改后的 fit_and_predict_kfas 函数
fit_and_predict_kfas <- function(patt_src, hist_src, l_period) {
    t.start <- Sys.time()

    # 创建一个空的结果对象，在出错时返回
    empty_result <- list(
        src_pandemic = patt_src,
        estim.median = numeric(nrow(patt_src)),
        estim.lower = numeric(nrow(patt_src)),
        estim.upper = numeric(nrow(patt_src)),
        forecast_result = NULL
    )

    # 获取年份数量
    nys <- length(unique(patt_src$YEAR))

    # 初始化 src_pandemic
    src_pandemic <- patt_src

    # 根据周期类型设置参数（月/周）
    if (l_period > 51) {
        # 周数据
        num.cycle <- 52
        len.cycle <- 7
        hist_src$DAYS <- 7
        hist_src$logdays <- log(hist_src$DAYS)
        src_pandemic <- patt_src %>%
            mutate(DAYS = 7, logdays = log(7))
    } else {
        # 月数据
        num.cycle <- 12
        len.cycle <- 30
        DOM <- c(31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31)
        hist_src$DAYS <- DOM[hist_src$PERIOD]
        hist_src$DAYS[hist_src$PERIOD == 2 & hist_src$YEAR %% 4 == 0] <- 29
        hist_src$logdays <- log(hist_src$DAYS)

        if (!"DAYS" %in% names(src_pandemic)) {
            src_pandemic$DAYS <- DOM[src_pandemic$PERIOD]
            src_pandemic$DAYS[src_pandemic$PERIOD == 2 & src_pandemic$YEAR %% 4 == 0] <- 29
        }
        src_pandemic <- src_pandemic %>%
            mutate(logdays = log(DAYS))
    }

    # 构造时间序列（支持月和周数据）
    if (l_period > 51) {
        hist_src <- hist_src %>%
            arrange(YEAR, PERIOD) %>%
            mutate(
                iso_week = sprintf("%d-W%02d-4", YEAR, PERIOD),
                date = ISOweek2date(iso_week)
            )
        src_pandemic <- src_pandemic %>%
            arrange(YEAR, PERIOD) %>%
            mutate(
                iso_week = sprintf("%d-W%02d-4", YEAR, PERIOD),
                date = ISOweek2date(iso_week)
            )
    } else {
        hist_src <- hist_src %>%
            arrange(YEAR, PERIOD) %>%
            mutate(date = as.Date(paste(YEAR, PERIOD, "15", sep = "-")))
        src_pandemic <- src_pandemic %>%
            arrange(YEAR, PERIOD) %>%
            mutate(date = as.Date(paste(YEAR, PERIOD, "15", sep = "-")))
    }

    # 检查日期有效性
    if (any(is.na(hist_src$date)) || any(is.na(src_pandemic$date))) {
        message("[WARNING] 日期列中存在 NA 值")
        return(empty_result)
    }

    # 按时间排序
    src_pandemic <- src_pandemic %>% arrange(date)
    hist_src <- hist_src %>% arrange(date)

    # 确定事件区间
    event_indices <- unique(src_pandemic$event_index[src_pandemic$event_index != "0"])
    event_periods <- list()
    for (event in event_indices) {
        event_data <- src_pandemic %>% filter(event_index == event)
        if (nrow(event_data) > 0) {
            event_periods[[event]] <- list(
                start_date = min(event_data$date),
                end_date = max(event_data$date),
                indices = which(src_pandemic$event_index == event)
            )
        }
    }

    # 初始化临时输出向量（存储分段预测结果）
    temp_estim.median <- rep(NA, nrow(src_pandemic))
    temp_estim.lower <- rep(NA, nrow(src_pandemic))
    temp_estim.upper <- rep(NA, nrow(src_pandemic))

    # 初始化累积训练数据
    cumulative_data <- hist_src

    # 分段拟合和预测
    for (event in names(event_periods)) {
        message("[INFO] 处理事件区间: ", event)

        # 当前事件区间的起止时间
        event_start <- event_periods[[event]]$start_date
        event_end <- event_periods[[event]]$end_date
        event_indices <- event_periods[[event]]$indices

        # 准备训练数据：所有早于当前事件结束时间的累积数据
        train_data <- cumulative_data %>% filter(date <= event_end)
        if (nrow(train_data) < 2 * num.cycle) {
            message("[WARNING] 训练数据不足两个周期，跳过事件 ", event)
            next
        }

        # 构造时间序列
        train_ts <- ts(train_data$NO_DEATHS, frequency = num.cycle)

        # 清理数据
        train_ts[is.na(train_ts)] <- 0
        train_ts[train_ts < 0] <- 0

        # 构建 KFAS 模型
        model <- tryCatch(
            {
                SSModel(
                    train_ts ~ SSMtrend(1, Q = NA) + SSMseasonal(num.cycle, Q = NA),
                    H = NA
                )
            },
            error = function(e) {
                message("[ERROR] 构建模型失败: ", e$message)
                return(NULL)
            }
        )

        if (is.null(model)) {
            message("[WARNING] 事件 ", event, " 模型构建失败")
            next
        }

        # 拟合模型
        fit <- tryCatch(
            {
                fitSSM(model, inits = c(1, 1, 1))
            },
            error = function(e) {
                message("[ERROR] 模型拟合失败: ", e$message)
                return(NULL)
            }
        )

        if (is.null(fit)) {
            message("[WARNING] 事件 ", event, " 模型拟合失败")
            next
        }

        # 预测当前事件区间
        event_data <- src_pandemic %>% filter(event_index == event)
        n.ahead <- nrow(event_data)

        pred_result <- tryCatch(
            {
                predict(fit$model, n.ahead = n.ahead, interval = "confidence", level = 0.95)
            },
            error = function(e) {
                message("[ERROR] 预测失败: ", e$message)
                return(NULL)
            }
        )

        if (is.null(pred_result)) {
            message("[WARNING] 事件 ", event, " 预测失败")
            next
        }

        # 提取预测结果
        pred.median <- as.numeric(pred_result[, "fit"])
        pred.lower <- as.numeric(pred_result[, "lwr"])
        pred.upper <- as.numeric(pred_result[, "upr"])

        # 存储分段预测结果
        if (length(event_indices) == length(pred.median)) {
            temp_estim.median[event_indices] <- pred.median
            temp_estim.lower[event_indices] <- pred.lower
            temp_estim.upper[event_indices] <- pred.upper
        }

        # 将预测值添加到累积数据中
        pred_data <- event_data %>% mutate(NO_DEATHS = pred.median)
        cumulative_data <- bind_rows(cumulative_data, pred_data) %>% arrange(date)
    }

    # 处理非事件区间（临时填充）
    non_event_indices <- which(src_pandemic$event_index == "0")
    if (length(non_event_indices) > 0) {
        hist_ts <- ts(hist_src$NO_DEATHS, frequency = num.cycle)
        hist_ts[is.na(hist_ts)] <- 0
        hist_ts[hist_ts < 0] <- 0

        model <- tryCatch(
            {
                SSModel(
                    hist_ts ~ SSMtrend(1, Q = NA) + SSMseasonal(num.cycle, Q = NA),
                    H = NA
                )
            },
            error = function(e) {
                message("[ERROR] 非事件区间模型构建失败: ", e$message)
                return(empty_result)
            }
        )

        fit <- tryCatch(
            {
                fitSSM(model, inits = c(1, 1, 1))
            },
            error = function(e) {
                message("[ERROR] 非事件区间模型拟合失败: ", e$message)
                return(empty_result)
            }
        )

        fit_result <- tryCatch(
            {
                fitted(fit$model, filtered = TRUE)
            },
            error = function(e) {
                message("[ERROR] 非事件区间拟合结果获取失败: ", e$message)
                return(empty_result)
            }
        )

        fit.median <- as.numeric(fit_result)
        if (!is.null(attr(fit_result, "variance"))) {
            variance <- as.numeric(attr(fit_result, "variance"))
            variance[variance < 0] <- 0
            fit.lower <- fit.median - 1.96 * sqrt(variance)
            fit.upper <- fit.median + 1.96 * sqrt(variance)
        } else {
            fit.lower <- fit.median * 0.8
            fit.upper <- fit.median * 1.2
        }

        # 临时填充非事件区间
        non_event_mapping <- match(
            paste(src_pandemic$YEAR[non_event_indices], src_pandemic$PERIOD[non_event_indices]),
            paste(hist_src$YEAR, hist_src$PERIOD)
        )

        temp_estim.median[non_event_indices] <- fit.median[non_event_mapping]
        temp_estim.lower[non_event_indices] <- fit.lower[non_event_mapping]
        temp_estim.upper[non_event_indices] <- fit.upper[non_event_mapping]
    }

    # 最终拟合：使用所有非事件数据和事件区间的预测值
    message("[INFO] 开始最终拟合，覆盖整个 src_pandemic 时间范围...")

    # 准备最终拟合数据
    final_data <- src_pandemic %>%
        mutate(NO_DEATHS = case_when(
            event_index == "0" ~ NO_DEATHS,
            TRUE ~ temp_estim.median
        )) %>%
        arrange(date)

    # 检查 final_data 是否完整
    if (any(is.na(final_data$NO_DEATHS))) {
        message("[WARNING] final_data 中存在 NA 值，使用平均值填充")
        valid_values <- final_data$NO_DEATHS[!is.na(final_data$NO_DEATHS)]
        if (length(valid_values) > 0) {
            final_data$NO_DEATHS[is.na(final_data$NO_DEATHS)] <- mean(valid_values, na.rm = TRUE)
        } else {
            message("[ERROR] final_data 中没有有效值")
            return(empty_result)
        }
    }

    # 构造最终时间序列
    final_ts <- ts(final_data$NO_DEATHS, frequency = num.cycle)
    final_ts[final_ts < 0] <- 0

    # 检查数据量
    if (length(final_ts) < 2 * num.cycle) {
        message("[WARNING] 最终数据不足两个周期，使用分段结果")
        return(list(
            src_pandemic = src_pandemic,
            estim.median = temp_estim.median,
            estim.lower = temp_estim.lower,
            estim.upper = temp_estim.upper
        ))
    }

    # 构建最终 KFAS 模型
    final_model <- tryCatch(
        {
            SSModel(
                final_ts ~ SSMtrend(1, Q = NA) + SSMseasonal(num.cycle, Q = NA),
                H = NA
            )
        },
        error = function(e) {
            message("[ERROR] 最终模型构建失败: ", e$message)
            return(NULL)
        }
    )

    if (is.null(final_model)) {
        message("[WARNING] 最终模型构建失败，使用分段结果")
        return(list(
            src_pandemic = src_pandemic,
            estim.median = temp_estim.median,
            estim.lower = temp_estim.lower,
            estim.upper = temp_estim.upper
        ))
    }

    # 拟合最终模型
    final_fit <- tryCatch(
        {
            fitSSM(final_model, inits = c(1, 1, 1))
        },
        error = function(e) {
            message("[ERROR] 最终模型拟合失败: ", e$message)
            return(NULL)
        }
    )

    if (is.null(final_fit)) {
        message("[WARNING] 最终模型拟合失败，使用分段结果")
        return(list(
            src_pandemic = src_pandemic,
            estim.median = temp_estim.median,
            estim.lower = temp_estim.lower,
            estim.upper = temp_estim.upper
        ))
    }

    # 获取最终结果（拟合 + 预测）
    # 由于 final_data 已覆盖整个 src_pandemic 时间范围，仅需 fitted
    final_result <- tryCatch(
        {
            fitted(final_fit$model, filtered = TRUE)
        },
        error = function(e) {
            message("[ERROR] 最终拟合结果获取失败: ", e$message)
            return(NULL)
        }
    )

    if (is.null(final_result)) {
        message("[WARNING] 最终拟合结果获取失败，使用分段结果")
        return(list(
            src_pandemic = src_pandemic,
            estim.median = temp_estim.median,
            estim.lower = temp_estim.lower,
            estim.upper = temp_estim.upper
        ))
    }

    # 提取最终结果
    final_median <- as.numeric(final_result)
    if (!is.null(attr(final_result, "variance"))) {
        variance <- as.numeric(attr(final_result, "variance"))
        variance[variance < 0] <- 0
        final_lower <- final_median - 1.96 * sqrt(variance)
        final_upper <- final_median + 1.96 * sqrt(variance)
    } else {
        message("[WARNING] 最终模型未提供方差，使用默认置信区间")
        final_lower <- final_median * 0.8
        final_upper <- final_median * 1.2
    }

    # 初始化最终输出向量
    full_estim.median <- rep(NA, nrow(src_pandemic))
    full_estim.lower <- rep(NA, nrow(src_pandemic))
    full_estim.upper <- rep(NA, nrow(src_pandemic))

    # 填充整个 src_pandemic 的预测值
    if (length(final_median) == nrow(src_pandemic)) {
        full_estim.median <- final_median
        full_estim.lower <- final_lower
        full_estim.upper <- final_upper
    } else {
        message("[WARNING] 最终预测结果长度不匹配，尝试调整")
        n_valid <- min(length(final_median), nrow(src_pandemic))
        full_estim.median[1:n_valid] <- final_median[1:n_valid]
        full_estim.lower[1:n_valid] <- final_lower[1:n_valid]
        full_estim.upper[1:n_valid] <- final_upper[1:n_valid]

        # 如果 src_pandemic 更长，预测剩余时间点
        if (nrow(src_pandemic) > length(final_median)) {
            n_ahead <- nrow(src_pandemic) - length(final_median)
            extra_pred <- tryCatch(
                {
                    predict(final_fit$model, n.ahead = n_ahead, interval = "confidence", level = 0.95)
                },
                error = function(e) {
                    message("[ERROR] 额外预测失败: ", e$message)
                    return(NULL)
                }
            )

            if (!is.null(extra_pred)) {
                extra_median <- as.numeric(extra_pred[, "fit"])
                extra_lower <- as.numeric(extra_pred[, "lwr"])
                extra_upper <- as.numeric(extra_pred[, "upr"])

                full_estim.median[(n_valid + 1):nrow(src_pandemic)] <- extra_median
                full_estim.lower[(n_valid + 1):nrow(src_pandemic)] <- extra_lower
                full_estim.upper[(n_valid + 1):nrow(src_pandemic)] <- extra_upper
            } else {
                # 如果预测失败，使用最后一个值填充
                full_estim.median[(n_valid + 1):nrow(src_pandemic)] <- final_median[n_valid]
                full_estim.lower[(n_valid + 1):nrow(src_pandemic)] <- final_lower[n_valid]
                full_estim.upper[(n_valid + 1):nrow(src_pandemic)] <- final_upper[n_valid]
            }
        }
    }

    # 处理缺失值
    missing_indices <- which(is.na(full_estim.median))
    if (length(missing_indices) > 0) {
        message("[INFO] 填充 ", length(missing_indices), " 个缺失预测值")
        valid_indices <- which(!is.na(full_estim.median))
        if (length(valid_indices) > 0) {
            avg_median <- mean(full_estim.median[valid_indices], na.rm = TRUE)
            avg_lower <- mean(full_estim.lower[valid_indices], na.rm = TRUE)
            avg_upper <- mean(full_estim.upper[valid_indices], na.rm = TRUE)

            full_estim.median[missing_indices] <- avg_median
            full_estim.lower[missing_indices] <- avg_lower
            full_estim.upper[missing_indices] <- avg_upper
        } else {
            message("[WARNING] 没有有效预测值可用于填充")
        }
    }

    # 确保非负值
    full_estim.median[full_estim.median < 0] <- 0
    full_estim.lower[full_estim.lower < 0] <- 0
    full_estim.upper[full_estim.upper < 0] <- 0

    message("模式处理时间: ", round(difftime(Sys.time(), t.start, units = "secs"), 1), " 秒")

    return(list(
        src_pandemic = src_pandemic,
        estim.median = full_estim.median,
        estim.lower = full_estim.lower,
        estim.upper = full_estim.upper
    ))
}

# 更新输出函数（统一处理所有预测结果）
update_output_kfas <- function(out_data, model_results, pattern, year_predict) {
    message("[DEBUG] update_output_kfas 开始处理模式: ", pattern)

    # 检查模型结果是否存在
    if (is.null(model_results)) {
        message("[ERROR] 模型结果为 NULL")
        return(out_data)
    }

    src_pandemic <- model_results$src_pandemic
    estim.median <- model_results$estim.median
    estim.lower <- model_results$estim.lower
    estim.upper <- model_results$estim.upper

    if (is.null(src_pandemic) || is.null(estim.median) || is.null(estim.lower) || is.null(estim.upper)) {
        message("[WARNING] 模式 ", pattern, " 的模型结果缺失必要组件")
        return(out_data)
    }

    # 检查数据结构
    message("[DEBUG] src_pandemic 行数: ", nrow(src_pandemic))
    message("[DEBUG] estim.median 长度: ", length(estim.median))
    message("[DEBUG] 模式分解: ", paste(strsplit(pattern, ";")[[1]], collapse = ", "))

    # 分解模式
    pattern_parts <- tryCatch({
        strsplit(pattern, ";")[[1]]
    }, error = function(e) {
        message("[ERROR] 无法分解模式: ", e$message)
        return(c("Total", "Total", "Total", "Total"))
    })

    # 确保模式有四个部分
    if (length(pattern_parts) < 4) {
        message("[WARNING] 模式部分不足 4 个，添加缺失的部分")
        pattern_parts <- c(pattern_parts, rep("Total", 4 - length(pattern_parts)))
    }

    # 初始化结果数据框
    result_df <- out_data[0, ]

    # 获取周期数和预测年份数
    l_period <- max(out_data$PERIOD, na.rm = TRUE)
    nyear_predict <- length(year_predict)

    message("[DEBUG] 周期数: ", l_period, ", 预测年份数: ", nyear_predict)
    message("[DEBUG] 预测年份: ", paste(year_predict, collapse = ", "))

    # 处理每个年份和周期
    for (iyear_predict in 1:nyear_predict) {
        y <- year_predict[iyear_predict]
        for (k in 1:l_period) {
            # 在 src_pandemic 中查找匹配的记录
            a <- src_pandemic$YEAR == y & src_pandemic$PERIOD == k

            # 如果没有匹配的记录，跳过
            if (sum(a) == 0) {
                next
            }

            # 在 out_data 中查找匹配的记录
            current_records <- tryCatch({
                out_data[
                    out_data$SEX == pattern_parts[1] &
                    out_data$AGE_GROUP == pattern_parts[2] &
                    out_data$AREA == pattern_parts[3] &
                    out_data$CAUSE == pattern_parts[4] &
                    out_data$YEAR == y &
                    out_data$PERIOD == k,
                ]
            }, error = function(e) {
                message("[ERROR] 查询当前记录失败: ", e$message)
                return(out_data[0, ])
            })

            # 如果找到匹配的记录并且有匹配的 src_pandemic 记录，则更新记录
            if (nrow(current_records) > 0 && sum(a) > 0) {
                # 安全获取索引
                a_indices <- which(a)
                if (length(a_indices) > 0) {
                    # 只使用第一个匹配的索引，避免多个匹配导致的问题
                    idx <- a_indices[1]
                    if (idx <= length(estim.median) && idx <= length(estim.lower) && idx <= length(estim.upper)) {
                        current_records$ESTIMATE <- estim.median[idx]
                        current_records$LOWER_LIMIT <- estim.lower[idx]
                        current_records$UPPER_LIMIT <- estim.upper[idx]
                        result_df <- rbind(result_df, current_records)
                    } else {
                        message("[WARNING] 索引超出范围: ", idx, " > ",
                                min(length(estim.median), length(estim.lower), length(estim.upper)))
                    }
                }
            }
        }
    }

    message("[DEBUG] update_output_kfas 完成，返回行数: ", nrow(result_df))
    return(result_df)
}

# KFAS 模型主函数
fcn_kfas <- function(src) {
    message("\n[fcn_kfas] 开始 KFAS 模型计算...")
    flush.console()
    start_time <- Sys.time()

    # 检查输入数据
    if (is.null(src) || nrow(src) == 0) {
        message("[ERROR] 输入数据为空")
        return(data.frame())
    }

    message("[INFO] 输入数据行数: ", nrow(src))

    # 预处理数据
    src <- tryCatch({
        src %>%
            filter(PERIOD <= 52) %>%
            arrange(SEX, AGE_GROUP, AREA, CAUSE, YEAR, PERIOD) %>%
            mutate(NO_DEATHS = as.numeric(NO_DEATHS))
    }, error = function(e) {
        message("[ERROR] 数据预处理失败: ", e$message)
        return(data.frame())
    })

    if (is.null(src) || nrow(src) == 0) {
        message("[ERROR] 预处理后数据为空")
        return(data.frame())
    }

    # 获取基本参数
    nys <- length(unique(src$YEAR))
    max_period <- max(src$PERIOD, na.rm = TRUE)
    wm_ident <- ifelse(max_period == 12, "Month", "Week")
    l_period <- ifelse(max_period == 12, 12, 52)

    message("[INFO] 年份数: ", nys, ", 最大周期: ", max_period, ", 标识符: ", wm_ident)

    # 计算日期 - 使用自定义方法而不是 calculate_dates 函数
    message("[INFO] 计算日期...")

    # 检查是否已经有日期列
    if ("date" %in% names(src)) {
        message("[INFO] 数据已包含 date 列，使用现有日期")
    } else {
        message("[INFO] 数据中没有 date 列，创建日期")

        # 如果没有日期列，根据周期类型创建
        if (max_period == 12) {
            # 月数据
            src$date <- as.Date(paste(src$YEAR, src$PERIOD, "15", sep = "-"))
        } else {
            # 周数据 - 使用 ISOweek 包
            dates <- character(nrow(src))
            for (i in 1:nrow(src)) {
                dates[i] <- sprintf("%d-W%02d-4", src$YEAR[i], src$PERIOD[i])
            }
            src$date <- as.Date(ISOweek2date(dates))
        }
    }

    # 检查是否已经有 DAYS 列
    if (!"DAYS" %in% names(src)) {
        message("[INFO] 添加 DAYS 列")
        if (max_period == 12) {
            # 月数据
            DOM <- c(31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31)
            src$DAYS <- DOM[src$PERIOD]
            # 闰年调整
            src$DAYS[src$PERIOD == 2 & src$YEAR %% 4 == 0] <- 29
        } else {
            # 周数据
            src$DAYS <- 7
        }
    }

    # 检查日期列是否有效
    if (any(is.na(src$date))) {
        message("[WARNING] 有缺失的日期值，将使用插值")
        # 简单的插值方法：使用最小日期
        min_date <- min(src$date, na.rm = TRUE)
        src$date[is.na(src$date)] <- min_date
    }

    # 初始化输出数据框
    out_data <- tryCatch({
        initialize_output(src, wm_ident, l_period)
    }, error = function(e) {
        message("[ERROR] 初始化输出数据框失败: ", e$message)
        return(data.frame())
    })

    if (is.null(out_data) || nrow(out_data) == 0) {
        message("[ERROR] 初始化输出数据框为空")
        return(data.frame())
    }

    # 获取唯一模式
    patterns <- tryCatch({
        src %>%
            select(SEX, AGE_GROUP, AREA, CAUSE) %>%
            distinct() %>%
            mutate(patterns = paste(SEX, AGE_GROUP, AREA, CAUSE, sep = ";")) %>%
            pull(patterns)
    }, error = function(e) {
        message("[ERROR] 获取唯一模式失败: ", e$message)
        return(character(0))
    })

    n_pat <- length(patterns)
    if (n_pat == 0) {
        message("[ERROR] 没有可用的模式")
        return(data.frame())
    }

    message("[INFO] 找到 ", n_pat, " 个模式")

    # 初始化结果列表
    results <- list()

    # 处理每个模式
    for (j in 1:n_pat) {
        pattern <- patterns[j]
        message("[INFO] 处理模式 ", j, "/", n_pat, ": ", pattern)

        # 提取模式数据
        pattern_parts <- tryCatch({
            strsplit(pattern, ";")[[1]]
        }, error = function(e) {
            message("[ERROR] 分解模式失败: ", e$message)
            return(NULL)
        })

        if (is.null(pattern_parts) || length(pattern_parts) < 4) {
            message("[WARNING] 模式格式不正确，跳过")
            next
        }

        patt_src <- tryCatch({
            src[
                src$SEX == pattern_parts[1] &
                src$AGE_GROUP == pattern_parts[2] &
                src$AREA == pattern_parts[3] &
                src$CAUSE == pattern_parts[4],
            ]
        }, error = function(e) {
            message("[ERROR] 提取模式数据失败: ", e$message)
            return(NULL)
        })

        if (is.null(patt_src)) {
            message("[WARNING] 无法提取模式数据，跳过")
            next
        }

        # 如果数据不足，则跳过
        if (nrow(patt_src) < 10) {
            message("[WARNING] 由于数据不足，跳过模式 (", nrow(patt_src), " 行)")
            next
        }

        # 准备历史数据
        hist_src <- tryCatch({
            # patt_src[patt_src$event_index == "0", ]
            patt_src
        }, error = function(e) {
            message("[ERROR] 提取历史数据失败: ", e$message)
            return(NULL)
        })

        if (is.null(hist_src)) {
            message("[WARNING] 无法提取历史数据，跳过")
            next
        }

        # 如果历史数据不足，则跳过
        if (nrow(hist_src) < 10) {
            message("[WARNING] 由于历史数据不足，跳过模式 (", nrow(hist_src), " 行)")
            next
        }

        # 模型拟合和预测
        message("[INFO] 开始模型拟合和预测...")
        model_results <- tryCatch({
            fit_and_predict_kfas(patt_src, hist_src, l_period)
        }, error = function(e) {
            message("[ERROR] 模型拟合和预测失败: ", e$message)
            return(data.frame())
        })

        # 如果模型失败，则跳过
        if (is.null(model_results) || (is.data.frame(model_results) && nrow(model_results) == 0)) {
            message("[WARNING] 此模式的模型失败")
            next
        }

        # 更新输出
        message("[INFO] 更新输出结果...")
        year_predict <- sort(unique(out_data$YEAR))
        result <- tryCatch({
            update_output_kfas(out_data, model_results, pattern, year_predict)
        }, error = function(e) {
            message("[ERROR] 更新输出失败: ", e$message)
            return(NULL)
        })

        if (is.null(result)) {
            message("[WARNING] 更新输出结果为空，跳过")
            next
        }

        # 存储结果
        if (nrow(result) > 0) {
            results[[j]] <- result
            message("[INFO] 成功处理模式: ", pattern, " (", nrow(result), " 行)")
        } else {
            message("[WARNING] 模式处理结果为空: ", pattern)
        }
    }

    # 合并结果
    message("[INFO] 合并所有模式的结果...")
    out_data <- NULL
    if (length(results) > 0) {
        # 过滤掉空结果
        valid_results <- results[!sapply(results, is.null)]
        if (length(valid_results) > 0) {
            out_data <- tryCatch({
                do.call(rbind, valid_results)
            }, error = function(e) {
                message("[ERROR] 合并结果失败: ", e$message)
                return(NULL)
            })
            message("[INFO] 成功合并 ", length(valid_results), " 个模式的结果")
        } else {
            message("[WARNING] 没有有效的模式结果可合并")
        }
    } else {
        message("[WARNING] 没有成功处理的模式!")
    }

    if (is.null(out_data) || nrow(out_data) == 0) {
        message("[ERROR] 最终结果为空")
        return(data.frame())
    }

    # 处理结果
    message("[INFO] 处理最终结果...")
    if (!is.null(out_data)) {
        out_data <- tryCatch({
            processed_data <- process_model_results(out_data, "KFAS")
            # 转换为 tbl_df
            if (!is.null(processed_data)) {
                message("[INFO] 将结果转换为 tbl_df")
                processed_data <- dplyr::as_tibble(processed_data)
            }
            processed_data
        }, error = function(e) {
            message("[ERROR] 处理最终结果失败: ", e$message)
            # 返回原始结果转换为 tbl_df
            dplyr::as_tibble(out_data)
        })
    }

    end_time <- Sys.time()
    total_time <- difftime(end_time, start_time, units = "mins")
    message("[INFO] KFAS 模型计算完成，总时间: ", round(total_time, 2), " 分钟")
    flush.console()

    return(out_data)
}

# 计算超额死亡
calculate_excess_deaths <- function(recorded_deaths, expected_deaths) {
    excess_deaths <- ifelse(is.na(recorded_deaths), NA, recorded_deaths - expected_deaths$ESTIMATE)
    excess_deaths_lower <- ifelse(is.na(recorded_deaths), NA, recorded_deaths - expected_deaths$UPPER_LIMIT)
    excess_deaths_upper <- ifelse(is.na(recorded_deaths), NA, recorded_deaths - expected_deaths$LOWER_LIMIT)
    return(data.frame(
        Excess_Deaths = excess_deaths,
        Excess_Deaths_Lower = excess_deaths_lower,
        Excess_Deaths_Upper = excess_deaths_upper
    ))
}

# 主函数：计算超额死亡
calculate_acm_excess_kfas <- function(src) {
    # 运行 KFAS 模型
    kfas_results <- fcn_kfas(src)

    # 提取实际死亡人数
    recorded_deaths <- src %>%
        arrange(SEX, AGE_GROUP, AREA, CAUSE, YEAR, PERIOD) %>%
        pull(NO_DEATHS)

    # 计算超额死亡
    if (!is.null(kfas_results)) {
        excess_deaths <- calculate_excess_deaths(recorded_deaths, kfas_results)
        kfas_results <- cbind(kfas_results, excess_deaths)
    }

    return(kfas_results)
}