# --- 1. Load Libraries ---
# Assume main libraries are already loaded in app.R
# library(shiny)
# library(ggplot2)
# library(dplyr)
# library(DT)
# library(lubridate)
# library(shinyjs)
# Add plotly library
# library(plotly)

# Ensure necessary libraries are loaded
library(plotly)
library(RColorBrewer)

# Safely calculate the midpoint of two dates
date_midpoint <- function(date1, date2) {
  if (inherits(date1, "Date") || inherits(date1, "POSIXt")) {
    date1_num <- as.numeric(date1)
  } else {
    date1_num <- date1
  }

  if (inherits(date2, "Date") || inherits(date2, "POSIXt")) {
    date2_num <- as.numeric(date2)
  } else {
    date2_num <- date2
  }

  mid_num <- (date1_num + date2_num) / 2

  if (inherits(date1, "Date")) {
    return(as.Date(mid_num, origin = "1970-01-01"))
  } else if (inherits(date1, "POSIXt")) {
    return(as.POSIXct(mid_num, origin = "1970-01-01"))
  } else {
    return(mid_num)
  }
}

# Add locale setting to English to ensure month labels are in English
# Save current locale setting
original_locale <- Sys.getlocale("LC_TIME")
# Set locale to English
Sys.setlocale("LC_TIME", "English")

# --- 2. Define Module UI ---
plot_prediction_module_ui <- function(id) {
  ns <- NS(id)
  fluidPage(
    # titlePanel("Mortality Data Visualization"),
    sidebarLayout(
      sidebarPanel(
        width = 3,
        h4("Data Filtering"),
        uiOutput(ns("sex_selector")),
        uiOutput(ns("age_group_selector")),
        h4("Model Selection"),
        uiOutput(ns("model_selector")),
        hr(),
# Time Range Selection
        h4("Time Range Selection"),
        uiOutput(ns("time_slider")),
        hr(),
        h4("Download Plots (PDF)"),
        downloadButton(ns("downloadACMPlot"), "Download ACM Plot"),
        br(), br(),
        downloadButton(ns("downloadEDPlot"), "Download Excess Deaths Plot"),
        br(), br(),
        downloadButton(ns("downloadEPPlot"), "Download P-Score Plot")
      ),
      mainPanel(
        width = 9,
        tabsetPanel(
          id = ns("plotTabs"),
          tabPanel(
            "All Cause Mortality",
            br(),
            h4("All Cause Mortality (Recorded vs. Expected)"),
# Replace plotOutput with plotlyOutput
            plotlyOutput(ns("ACMplot"), height = "600px")
          ),
          tabPanel(
            "Excess Deaths",
            br(),
            tabsetPanel(
              id = ns("edTabs"),
              tabPanel(
                "Bar Plot",
                br(),
                h4("Excess Deaths"),
# Replace plotOutput with plotlyOutput
                plotlyOutput(ns("EDplot"), height = "600px")
              ),
              tabPanel(
                "Line Plot",
                br(),
                h4("Excess Deaths (Line Plot)"),
# Replace plotOutput with plotlyOutput
                plotlyOutput(ns("EDLinePlot"), height = "600px")
              )
            )
          ),
          tabPanel(
            "P-Score",
            br(),
            tabsetPanel(
              id = ns("psTabs"),
              tabPanel(
                "Bar Plot",
                br(),
                h4("P-Score of Excess Deaths (%)"),
                # 将plotOutput替换为plotlyOutput
                plotlyOutput(ns("EPplot"), height = "600px")
              ),
              tabPanel(
                "Line Plot",
                br(),
                h4("P-Score (Line Plot)"),
                # 将plotOutput替换为plotlyOutput
                plotlyOutput(ns("EPLinePlot"), height = "600px")
              )
            )
          )
        )
      )
    )
  )
}

# --- 3. Define Module Server ---
plot_prediction_module_server <- function(id, rv) {
  moduleServer(id, function(input, output, session) {
# Use namespace
    ns <- session$ns

# Dynamically generate UI selectors
    output$sex_selector <- renderUI({
      req(rv$total_prediction)
      unique_sex <- sort(unique(rv$total_prediction$SEX), na.last = TRUE, decreasing = TRUE)
      selectInput(ns("selected_sex"), "Select Sex:",
        choices = unique_sex,
        selected = if (length(unique_sex) > 0) unique_sex[1] else NULL,
        multiple = FALSE
      )
    })

    output$age_group_selector <- renderUI({
      req(rv$total_prediction)
      unique_age_group <- sort(unique(rv$total_prediction$AGE_GROUP), na.last = TRUE, decreasing = TRUE)
      selectInput(ns("selected_age_group"), "Select Age Group:",
        choices = unique_age_group,
        selected = if (length(unique_age_group) > 0) unique_age_group[1] else NULL,
        multiple = FALSE
      )
    })

    output$model_selector <- renderUI({
      req(rv$total_prediction)
      unique_models <- sort(unique(rv$total_prediction$Model))
      checkboxGroupInput(ns("selected_models"), "Select Models to Display:",
        choices = unique_models,
        selected = "Negative Binomial Regression"
      )
    })

# Create dynamic time slider
    output$time_slider <- renderUI({
      req(rv$total_prediction, input$selected_sex, input$selected_age_group, input$selected_models)

      data <- rv$total_prediction %>%
        filter(
          SEX == input$selected_sex,
          AGE_GROUP == input$selected_age_group,
          Model %in% input$selected_models
        )

      data_dates <- data %>%
        mutate(
          TimePoint = case_when(
            max(PERIOD, na.rm = TRUE) > 12 ~
              as.Date(paste(YEAR, 1, 1, sep = "-")) + weeks(PERIOD - 1) + days(3),
            TRUE ~ as.Date(paste(YEAR, PERIOD, 15, sep = "-"))
          )
        ) %>%
        pull(TimePoint)

      if (length(data_dates) == 0) {
        return(NULL)
      }

      date_range <- range(data_dates, na.rm = TRUE)

      sliderInput(ns("time_range"),
        "Select Time Period:",
        min = date_range[1],
        max = date_range[2],
        value = date_range,
        timeFormat = "%Y-%m",
        step = 7,
        width = "100%"
      )
    })

# Filter data
    filtered_total_data <- reactive({
      req(
        rv$total_prediction, input$selected_sex,
        input$selected_age_group, input$selected_models,
        input$time_range
      )

      data <- rv$total_prediction %>%
        filter(
          SEX == input$selected_sex,
          AGE_GROUP == input$selected_age_group,
          Model %in% input$selected_models
        )

      max_period <- max(data$PERIOD, na.rm = TRUE)

      data %>%
        mutate(
          TimePoint = case_when(
            max_period > 12 ~
              as.Date(paste(YEAR, 1, 1, sep = "-")) + weeks(PERIOD - 1) + days(3),
            TRUE ~ as.Date(paste(YEAR, PERIOD, 15, sep = "-"))
          )
        ) %>%
        filter(
          TimePoint >= input$time_range[1],
          TimePoint <= input$time_range[2]
        ) %>%
        arrange(TimePoint)
    })

# Add time formatting function
    format_time_labels <- function(dates, data) {
    # Check if it is weekly data
      max_period <- max(data$PERIOD, na.rm = TRUE)
      if (max_period > 12) {
    # Weekly data formatting
        sapply(dates, function(d) {
          year <- format(d, "%Y")
          week <- isoweek(d)
          sprintf("%s-%02d", year, week)
        })
      } else {
    # Monthly data formatting
        format(dates, "%Y-%m")
      }
    }

    # 绘图函数
    create_acm_plot <- function(data) {
      if (is.null(data) || nrow(data) == 0) {
        return(ggplot() +
          annotate("text", x = 0.5, y = 0.5, label = "No data available for selected filters.") +
          theme_void())
      }

      # Calculate x-axis breaks
      date_range <- range(data$TimePoint, na.rm = TRUE)
      time_breaks <- seq(date_range[1], date_range[2], by = "3 months")

      plot_subtitle <- paste("Sex:", input$selected_sex, "| Age Group:", input$selected_age_group)

      # Calculate the maximum and minimum values for positioning and y-axis limits
      maxpos <- max(data$NO_DEATHS, na.rm = TRUE)
      minpos <- min(data$NO_DEATHS, na.rm = TRUE)
      if ("UPPER_LIMIT" %in% names(data)) {
        maxpos <- max(maxpos, max(data$UPPER_LIMIT, na.rm = TRUE))
      }
      if ("LOWER_LIMIT" %in% names(data)) {
        minpos <- min(minpos, min(data$LOWER_LIMIT, na.rm = TRUE))
      }

      # Adjust y-axis limits to reduce whitespace
      y_min <- max(0, minpos * 0.95) # Start slightly below the minimum, but not below 0
      y_max <- maxpos * 1.1 # Extend slightly above the maximum to accommodate labels

      p <- ggplot(data, aes(x = TimePoint)) +
        geom_line(aes(y = NO_DEATHS, color = "Recorded"), linewidth = 1) +
        geom_line(aes(y = EXP_DEATHS, color = Model, linetype = Model), linewidth = 0.8) +
        {
          if ("LOWER_LIMIT" %in% names(data) && "UPPER_LIMIT" %in% names(data)) {
            geom_ribbon(aes(ymin = LOWER_LIMIT, ymax = UPPER_LIMIT, fill = Model), alpha = 0.2)
          }
        } +
        # Add event annotations with colored blocks instead of vertical lines
        {
          event_data <- data %>%
            filter(event_index != "0" & !is.na(event_index)) %>%
            filter(!is.na(event_name)) %>%
            group_by(event_index, event_name) %>%
            summarise(
              start_time = min(TimePoint),
              end_time = max(TimePoint),
              max_deaths = max(NO_DEATHS),
              .groups = "drop"
            ) %>%
            mutate(
              # Adjust label position to be closer to the data
              label_y = maxpos * 0.98, # Place labels just below the max value
              # Generate different colors for different events
              event_color = factor(event_index)
            )

          if (nrow(event_data) > 0) {
            list(
              # Add colored rectangles for event periods
              geom_rect(
                data = event_data,
                aes(
                  xmin = start_time,
                  xmax = end_time,
                  ymin = y_min,
                  ymax = y_max,
                  fill = event_name
                ),
                alpha = 0.15,
                inherit.aes = FALSE
              ),
              # Add event labels
              geom_text(
                data = event_data,
                aes(
                  x = start_time + (end_time - start_time)/2, # Center the label in the event period
                  y = label_y,
                  label = event_name
                ),
                hjust = 0.5, # Center text horizontally
                vjust = -0.5, # Position above the rectangle
                size = 3,
                color = "darkred"
              ),
              # Add a scale for the event fills
              scale_fill_brewer(
                name = "Events",
                palette = "Pastel1"
              )
            )
          }
        } +
        scale_x_date(
          breaks = time_breaks,
          labels = function(x) format_time_labels(x, data),
          expand = expansion(mult = c(0.02, 0.02))
        ) +
        scale_y_continuous(limits = c(y_min, y_max), expand = expansion(mult = c(0, 0.05))) + # Set dynamic y-axis limits
        scale_color_manual(
          name = "Series",
          values = c("Recorded" = "black", "Historical Average" = "cyan3", "Negative Binomial Regression" = "indianred"),
          breaks = c("Recorded", "Historical Average", "Negative Binomial Regression")
        ) +
        scale_linetype_manual(
          name = "Series",
          values = c("Historical Average" = "dashed", "Negative Binomial Regression" = "dotted"),
          guide = "none"
        ) +
        scale_fill_manual(
          name = "95% CI",
          values = c("Historical Average" = "cyan3", "Negative Binomial Regression" = "indianred"),
          breaks = c("Historical Average", "Negative Binomial Regression")
        ) +
        labs(
          title = "All Cause Mortality: Recorded vs. Expected",
          subtitle = plot_subtitle,
          x = "Time",
          y = "Number of Deaths"
        ) +
        theme_bw() +
        theme(axis.text.x = element_text(angle = 45, hjust = 1), legend.position = "bottom")

      return(p)
    }

    create_ed_plot <- function(data) {
      if (is.null(data) || nrow(data) == 0) {
        return(ggplot() +
          annotate("text", x = 0.5, y = 0.5, label = "No data available for selected filters.") +
          theme_void())
      }

      # 过滤无死亡记录的数据并计算超额死亡
      data <- data %>%
        filter(NO_DEATHS > 0) %>%
        mutate(EXCESS_DEATHS = NO_DEATHS - EXP_DEATHS)

      if (nrow(data) == 0) {
        return(ggplot() +
          annotate("text", x = 0.5, y = 0.5, label = "No data with reported deaths available.") +
          theme_void())
      }

      # 计算置信区间
      if ("LOWER_LIMIT" %in% names(data) && "UPPER_LIMIT" %in% names(data)) {
        data <- data %>% mutate(
          EXCESS_LOWER = NO_DEATHS - UPPER_LIMIT,
          EXCESS_UPPER = NO_DEATHS - LOWER_LIMIT
        )
        ci_caption <- "Note: Error bars represent 95% CI for excess deaths."
      } else {
        ci_caption <- NULL
      }

      # 计算x轴刻度
      date_range <- range(data$TimePoint, na.rm = TRUE)
      n_breaks <- min(20, length(unique(data$TimePoint)))
      time_breaks <- seq(date_range[1], date_range[2], length.out = n_breaks)

      plot_subtitle <- paste("Sex:", input$selected_sex, "| Age Group:", input$selected_age_group)

      p <- ggplot(data, aes(x = TimePoint, y = EXCESS_DEATHS, fill = Model)) +
        geom_col(position = "dodge", width = 30, alpha = 0.7) +
        {
          if ("EXCESS_LOWER" %in% names(data) && "EXCESS_UPPER" %in% names(data)) {
            geom_errorbar(aes(ymin = EXCESS_LOWER, ymax = EXCESS_UPPER),
              position = position_dodge(30),
              width = 5, color = "gray40"
            )
          }
        } +
        # # 修复事件标注部分
        # {
        #   # 检查是否存在事件数据
        #   if ("event_index" %in% names(data) && "event_name" %in% names(data)) {
        #     event_data <- data %>%
        #       filter(!is.na(event_index), event_index != "0", !is.na(event_name)) %>%
        #       group_by(event_index, event_name) %>%
        #       summarise(
        #         start_time = min(TimePoint, na.rm = TRUE),
        #         end_time = max(TimePoint, na.rm = TRUE),
        #         max_excess = max(EXCESS_DEATHS, na.rm = TRUE),
        #         .groups = 'drop'
        #       )

        #     if (nrow(event_data) > 0) {
        #       list(
        #         geom_vline(data = event_data,
        #                   aes(xintercept = start_time),
        #                   linetype = "dashed",
        #                   color = "darkred",
        #                   alpha = 0.5),
        #         geom_vline(data = event_data,
        #                   aes(xintercept = end_time),
        #                   linetype = "dashed",
        #                   color = "darkred",
        #                   alpha = 0.5),
        #         geom_text(data = event_data,
        #                 aes(x = start_time,
        #                     y = max_excess * 0.9,
        #                     label = event_name),
        #                 angle = 90,
        #                 hjust = -0.1,
        #                 size = 3,
        #                 color = "darkred")
        #       )
        #     }
        #   }
        # } +
        geom_hline(yintercept = 0, linetype = "dashed", color = "black") +
        scale_x_date(
          breaks = time_breaks,
          labels = function(x) format_time_labels(x, data),
          expand = expansion(mult = c(0.02, 0.02))
        ) +
        scale_fill_manual(
          name = "Model",
          values = c("Historical Average" = "cyan3", "Negative Binomial Regression" = "indianred")
        ) +
        labs(
          title = "Excess Deaths (Recorded - Expected)",
          subtitle = plot_subtitle,
          caption = ci_caption,
          x = "Time",
          y = "Excess Deaths"
        ) +
        theme_bw() +
        theme(axis.text.x = element_text(angle = 45, hjust = 1), legend.position = "bottom")

      return(p)
    }

    create_ed_line_plot <- function(data) {
      if (is.null(data) || nrow(data) == 0) {
        return(ggplot() +
          annotate("text", x = 0.5, y = 0.5, label = "No data available for selected filters.") +
          theme_void())
      }

      # 过滤无死亡记录的数据并计算超额死亡
      data <- data %>%
        filter(NO_DEATHS > 0) %>%
        mutate(EXCESS_DEATHS = NO_DEATHS - EXP_DEATHS)

      if (nrow(data) == 0) {
        return(ggplot() +
          annotate("text", x = 0.5, y = 0.5, label = "No data with reported deaths available.") +
          theme_void())
      }

      # 计算置信区间
      if ("LOWER_LIMIT" %in% names(data) && "UPPER_LIMIT" %in% names(data)) {
        data <- data %>% mutate(
          EXCESS_LOWER = NO_DEATHS - UPPER_LIMIT,
          EXCESS_UPPER = NO_DEATHS - LOWER_LIMIT
        )
        ci_caption <- "Note: Shaded areas represent 95% CI for excess deaths."
      } else {
        ci_caption <- NULL
      }

      # 计算x轴刻度
      date_range <- range(data$TimePoint, na.rm = TRUE)
      time_breaks <- seq(date_range[1], date_range[2], by = "3 months")

      plot_subtitle <- paste("Sex:", input$selected_sex, "| Age Group:", input$selected_age_group)

      p <- ggplot(data, aes(x = TimePoint, y = EXCESS_DEATHS, color = Model)) +
        geom_line(linewidth = 1) +
        {
          if ("EXCESS_LOWER" %in% names(data) && "EXCESS_UPPER" %in% names(data)) {
            geom_ribbon(aes(ymin = EXCESS_LOWER, ymax = EXCESS_UPPER, fill = Model), alpha = 0.2, color = NA)
          }
        } +
        # 添加事件标注 - 使用彩色块代替垂直线
        {
          event_data <- data %>%
            filter(event_index != "0" & !is.na(event_index)) %>%
            filter(!is.na(event_name)) %>%
            group_by(event_index, event_name) %>%
            summarise(
              start_time = min(TimePoint),
              end_time = max(TimePoint),
              max_excess = max(EXCESS_DEATHS),
              .groups = "drop"
            )

          # 计算y轴范围
          y_min <- min(0, min(data$EXCESS_DEATHS, na.rm = TRUE))
          y_max <- max(data$EXCESS_DEATHS, na.rm = TRUE) * 1.1

          if (nrow(event_data) > 0) {
            list(
              # 添加彩色矩形表示事件期间
              geom_rect(
                data = event_data,
                aes(
                  xmin = start_time,
                  xmax = end_time,
                  ymin = y_min,
                  ymax = y_max,
                  fill = event_name
                ),
                alpha = 0.08, # 增加透明度，使背景更淡
                inherit.aes = FALSE
              ),
              # 添加事件标签
              geom_text(
                data = event_data,
                aes(
                  x = start_time + (end_time - start_time)/2, # 将标签居中放置在事件期间
                  y = y_max * 0.95, # Position consistently at 95% of the max y value
                  label = event_name
                ),
                hjust = 0.5, # 水平居中文本
                vjust = -0.5, # 将文本放在矩形上方
                size = 3,
                color = "darkred"
              ),
              # 添加事件填充的比例尺
              scale_fill_brewer(
                name = "Events",
                palette = "Set3" # 使用Set3调色板，提供更多对比色且较浅的颜色
              )
            )
          }
        } +
        geom_hline(yintercept = 0, linetype = "dashed", color = "black") +
        scale_x_date(
          breaks = time_breaks,
          labels = function(x) format_time_labels(x, data),
          expand = expansion(mult = c(0.02, 0.02))
        ) +
        scale_color_manual(
          name = "Model",
          values = c("Historical Average" = "cyan3", "Negative Binomial Regression" = "indianred")
        ) +
        scale_fill_manual(
          name = "95% CI",
          values = c("Historical Average" = "cyan3", "Negative Binomial Regression" = "indianred")
        ) +
        labs(
          title = "Excess Deaths (Line Plot)",
          subtitle = plot_subtitle,
          caption = ci_caption,
          x = "Time",
          y = "Excess Deaths"
        ) +
        theme_bw() +
        theme(axis.text.x = element_text(angle = 45, hjust = 1), legend.position = "bottom")

      return(p)
    }

    create_ep_plot <- function(data) {
      if (is.null(data) || nrow(data) == 0) {
        return(ggplot() +
          annotate("text", x = 0.5, y = 0.5, label = "No data available for selected filters.") +
          theme_void())
      }

      # 过滤无死亡记录或预期死亡为零的数据
      data <- data %>%
        filter(NO_DEATHS > 0, abs(EXP_DEATHS) > 1e-6) %>%
        mutate(
          EXCESS_DEATHS = NO_DEATHS - EXP_DEATHS,
          P_SCORE = (EXCESS_DEATHS / EXP_DEATHS) * 100
        )

      if (nrow(data) == 0) {
        return(ggplot() +
          annotate("text", x = 0.5, y = 0.5, label = "No data with reported deaths and valid expected deaths available.") +
          theme_void())
      }

      # 计算P-Score置信区间
      if ("LOWER_LIMIT" %in% names(data) && "UPPER_LIMIT" %in% names(data)) {
        data <- data %>% mutate(
          P_LOWER = ((NO_DEATHS - UPPER_LIMIT) / EXP_DEATHS) * 100,
          P_UPPER = ((NO_DEATHS - LOWER_LIMIT) / EXP_DEATHS) * 100
        )
        ci_caption <- "Note: Error bars represent 95% CI for P-Score."
      } else {
        ci_caption <- NULL
      }

      # 计算x轴刻度
      date_range <- range(data$TimePoint, na.rm = TRUE)
      n_breaks <- min(20, length(unique(data$TimePoint)))
      time_breaks <- seq(date_range[1], date_range[2], length.out = n_breaks)

      plot_subtitle <- paste("Sex:", input$selected_sex, "| Age Group:", input$selected_age_group)

      p <- ggplot(data, aes(x = TimePoint, y = P_SCORE, fill = Model)) +
        geom_col(position = "dodge", width = 30, alpha = 0.7) +
        {
          if ("P_LOWER" %in% names(data) && "P_UPPER" %in% names(data)) {
            geom_errorbar(aes(ymin = P_LOWER, ymax = P_UPPER),
              position = position_dodge(30),
              width = 5, color = "gray40"
            )
          }
        } +
        # # 修复事件标注部分
        # {
        #   # 检查是否存在事件数据
        #   if ("event_index" %in% names(data) && "event_name" %in% names(data)) {
        #     event_data <- data %>%
        #       filter(!is.na(event_index), event_index != "0", !is.na(event_name)) %>%
        #       group_by(event_index, event_name) %>%
        #       summarise(
        #         start_time = min(TimePoint, na.rm = TRUE),
        #         end_time = max(TimePoint, na.rm = TRUE),
        #         max_pscore = max(P_SCORE, na.rm = TRUE),
        #         .groups = 'drop'
        #       )

        #     if (nrow(event_data) > 0) {
        #       list(
        #         geom_vline(data = event_data,
        #                   aes(xintercept = start_time),
        #                   linetype = "dashed",
        #                   color = "darkred",
        #                   alpha = 0.5),
        #         geom_vline(data = event_data,
        #                   aes(xintercept = end_time),
        #                   linetype = "dashed",
        #                   color = "darkred",
        #                   alpha = 0.5),
        #         geom_text(data = event_data,
        #                 aes(x = start_time,
        #                     y = max_pscore * 0.9,
        #                     label = event_name),
        #                 angle = 90,
        #                 hjust = -0.1,
        #                 size = 3,
        #                 color = "darkred")
        #       )
        #     }
        #   }
        # } +
        geom_hline(yintercept = 0, linetype = "dashed", color = "black") +
        scale_x_date(
          breaks = time_breaks,
          labels = function(x) format_time_labels(x, data),
          expand = expansion(mult = c(0.02, 0.02))
        ) +
        scale_fill_manual(
          name = "Model",
          values = c("Historical Average" = "cyan3", "Negative Binomial Regression" = "indianred")
        ) +
        scale_y_continuous(labels = scales::percent_format(scale = 1)) +
        labs(
          title = "P-Score of Excess Deaths (%)",
          subtitle = plot_subtitle,
          caption = ci_caption,
          x = "Time",
          y = "P-Score"
        ) +
        theme_bw() +
        theme(axis.text.x = element_text(angle = 45, hjust = 1), legend.position = "bottom")

      return(p)
    }

    create_ep_line_plot <- function(data) {
      if (is.null(data) || nrow(data) == 0) {
        return(ggplot() +
          annotate("text", x = 0.5, y = 0.5, label = "No data available for selected filters.") +
          theme_void())
      }

      # 过滤无死亡记录或预期死亡为零的数据
      data <- data %>%
        filter(NO_DEATHS > 0, abs(EXP_DEATHS) > 1e-6) %>%
        mutate(
          EXCESS_DEATHS = NO_DEATHS - EXP_DEATHS,
          P_SCORE = (EXCESS_DEATHS / EXP_DEATHS) * 100
        )

      if (nrow(data) == 0) {
        return(ggplot() +
          annotate("text", x = 0.5, y = 0.5, label = "No data with reported deaths and valid expected deaths available.") +
          theme_void())
      }

      # 计算P-Score置信区间
      if ("LOWER_LIMIT" %in% names(data) && "UPPER_LIMIT" %in% names(data)) {
        data <- data %>% mutate(
          P_LOWER = ((NO_DEATHS - UPPER_LIMIT) / EXP_DEATHS) * 100,
          P_UPPER = ((NO_DEATHS - LOWER_LIMIT) / EXP_DEATHS) * 100
        )
        ci_caption <- "Note: Shaded areas represent 95% CI for P-Score."
      } else {
        ci_caption <- NULL
      }

      # 计算x轴刻度
      date_range <- range(data$TimePoint, na.rm = TRUE)
      time_breaks <- seq(date_range[1], date_range[2], by = "3 months")

      plot_subtitle <- paste("Sex:", input$selected_sex, "| Age Group:", input$selected_age_group)

      p <- ggplot(data, aes(x = TimePoint, y = P_SCORE, color = Model)) +
        geom_line(linewidth = 1) +
        {
          if ("P_LOWER" %in% names(data) && "P_UPPER" %in% names(data)) {
            geom_ribbon(aes(ymin = P_LOWER, ymax = P_UPPER, fill = Model), alpha = 0.2, color = NA)
          }
        } +
        # 添加事件标注 - 使用彩色块代替垂直线
        {
          event_data <- data %>%
            filter(event_index != "0" & !is.na(event_index)) %>%
            filter(!is.na(event_name)) %>%
            group_by(event_index, event_name) %>%
            summarise(
              start_time = min(TimePoint),
              end_time = max(TimePoint),
              max_pscore = max(P_SCORE),
              .groups = "drop"
            )

          # 计算y轴范围
          y_min <- min(0, min(data$P_SCORE, na.rm = TRUE))
          y_max <- max(data$P_SCORE, na.rm = TRUE) * 1.1

          if (nrow(event_data) > 0) {
            list(
              # 添加彩色矩形表示事件期间
              geom_rect(
                data = event_data,
                aes(
                  xmin = start_time,
                  xmax = end_time,
                  ymin = y_min,
                  ymax = y_max,
                  fill = event_name
                ),
                alpha = 0.08, # 增加透明度，使背景更淡
                inherit.aes = FALSE
              ),
              # 添加事件标签
              geom_text(
                data = event_data,
                aes(
                  x = start_time + (end_time - start_time)/2, # 将标签居中放置在事件期间
                  y = y_max * 0.95, # Position consistently at 95% of the max y value
                  label = event_name
                ),
                hjust = 0.5, # 水平居中文本
                vjust = -0.5, # 将文本放在矩形上方
                size = 3,
                color = "darkred"
              ),
              # 添加事件填充的比例尺
              scale_fill_brewer(
                name = "Events",
                palette = "Set3" # 使用Set3调色板，提供更多对比色且较浅的颜色
              )
            )
          }
        } +
        geom_hline(yintercept = 0, linetype = "dashed", color = "black") +
        scale_x_date(
          breaks = time_breaks,
          labels = function(x) format_time_labels(x, data),
          expand = expansion(mult = c(0.02, 0.02))
        ) +
        scale_color_manual(
          name = "Model",
          values = c("Historical Average" = "cyan3", "Negative Binomial Regression" = "indianred")
        ) +
        scale_fill_manual(
          name = "95% CI",
          values = c("Historical Average" = "cyan3", "Negative Binomial Regression" = "indianred")
        ) +
        scale_y_continuous(labels = scales::percent_format(scale = 1)) +
        labs(
          title = "P-Score (Line Plot)",
          subtitle = plot_subtitle,
          caption = ci_caption,
          x = "Time",
          y = "P-Score"
        ) +
        theme_bw() +
        theme(axis.text.x = element_text(angle = 45, hjust = 1), legend.position = "bottom")

      return(p)
    }

    # --- Render Plot Outputs ---
    # 修改渲染方法，避免ggplotly转换错误
    output$ACMplot <- renderPlotly({
      message("Rendering ACM plot...")

      tryCatch(
        {
          data <- filtered_total_data()
          if (is.null(data) || nrow(data) == 0) {
            message("No data available for ACM plot")
            return(plot_ly() %>%
              add_annotations(
                text = "No data available for selected filters.",
                x = 0.5, y = 0.5, showarrow = FALSE
              ))
          }

          # Create the base Plotly object
          p <- plot_ly(data, x = ~TimePoint) %>%
            add_trace(
              y = ~NO_DEATHS, name = "Recorded", type = "scatter", mode = "lines",
              line = list(color = "black", width = 2)
            )

          # Add expected deaths lines for each model
          for (model_name in unique(data$Model)) {
            model_data <- data[data$Model == model_name, ]
            line_color <- if (model_name == "Historical Average") "cyan3" else "indianred"
            line_dash <- if (model_name == "Historical Average") "dash" else "dot"

            p <- p %>% add_trace(
              data = model_data,
              y = ~EXP_DEATHS,
              name = paste(model_name, "Expected"),
              type = "scatter",
              mode = "lines",
              line = list(color = line_color, width = 1.5, dash = line_dash)
            )

            if ("LOWER_LIMIT" %in% names(model_data) && "UPPER_LIMIT" %in% names(model_data)) {
              p <- p %>% add_ribbons(
                data = model_data,
                y = ~EXP_DEATHS,
                ymin = ~LOWER_LIMIT,
                ymax = ~UPPER_LIMIT,
                name = paste(model_name, "95% CI"),
                line = list(color = "transparent"),
                fillcolor = adjustcolor(line_color, alpha.f = 0.2),
                showlegend = FALSE
              )
            }
          }

          # Calculate the maximum and minimum y-values for positioning and y-axis limits
          max_y <- max(data$NO_DEATHS, na.rm = TRUE)
          min_y <- min(data$NO_DEATHS, na.rm = TRUE)
          if ("UPPER_LIMIT" %in% names(data)) {
            max_y <- max(max_y, max(data$UPPER_LIMIT, na.rm = TRUE))
          }
          if ("LOWER_LIMIT" %in% names(data)) {
            min_y <- min(min_y, min(data$LOWER_LIMIT, na.rm = TRUE))
          }

          # Adjust y-axis limits to reduce whitespace
          y_min <- max(0, min_y * 0.95) # Start slightly below the minimum, but not below 0
          y_max <- max_y * 1.1 # Extend slightly above the maximum to accommodate labels

          # Add event markers as colored rectangles instead of vertical lines
          event_data <- data %>%
            filter(event_index != "0" & !is.na(event_index)) %>%
            filter(!is.na(event_name)) %>%
            group_by(event_index, event_name) %>%
            summarise(
              start_time = min(TimePoint),
              end_time = max(TimePoint),
              max_deaths = max(NO_DEATHS),
              .groups = "drop"
            )

          # Define a color palette for events - using colors different from the main plot
          # Using Set3 palette which provides more contrasting and lighter colors
          event_colors <- RColorBrewer::brewer.pal(min(12, max(3, nrow(event_data))), "Set3")

          if (nrow(event_data) > 0) {
            # First add colored rectangles for each event period
            for (i in 1:nrow(event_data)) {
              # Use modulo to cycle through colors if more events than colors
              color_index <- ((i-1) %% length(event_colors)) + 1

              # 确保日期类型正确
              start_time <- event_data$start_time[i]
              end_time <- event_data$end_time[i]

              p <- p %>%
                add_trace(
                  type = "scatter",
                  mode = "none",
                  x = c(start_time, end_time, end_time, start_time),
                  y = c(y_min, y_min, y_max, y_max),
                  fill = "toself",
                  fillcolor = event_colors[color_index],
                  line = list(color = "transparent"),
                  opacity = 0.08, # Increase transparency for lighter background
                  name = event_data$event_name[i],
                  showlegend = TRUE,
                  hoverinfo = "name"
                ) %>%
                add_annotations(
                  x = date_midpoint(event_data$start_time[i], event_data$end_time[i]), # Center the label
                  y = y_max * 0.95, # Position consistently at 95% of the max y value
                  text = event_data$event_name[i],
                  showarrow = FALSE,
                  font = list(color = "darkred", size = 10)
                )
            }
          }

          # Set the layout with dynamic y-axis range
          p <- p %>% layout(
            title = list(
              text = "All Cause Mortality: Recorded vs. Expected",
              font = list(size = 16)
            ),
            xaxis = list(
              title = "Time",
              tickangle = 45
            ),
            yaxis = list(
              title = "Number of Deaths",
              range = c(y_min, y_max) # Set dynamic y-axis range
            ),
            legend = list(orientation = "h", y = -0.2),
            hovermode = "closest"
          )

          return(p)
        },
        error = function(e) {
          message("Error in ACM plot: ", e$message)
          return(plot_ly() %>%
            add_annotations(
              text = paste("Error:", e$message),
              x = 0.5, y = 0.5, showarrow = FALSE
            ))
        }
      )
    })


    # 修改 ED 图表渲染函数
    output$EDplot <- renderPlotly({
      # 添加调试信息
      message("Rendering ED plot...")

      # 使用tryCatch捕获错误
      tryCatch(
        {
          data <- filtered_total_data()
          if (is.null(data) || nrow(data) == 0) {
            message("No data available for ED plot")
            return(plot_ly() %>%
              add_annotations(
                text = "No data available for selected filters.",
                x = 0.5, y = 0.5, showarrow = FALSE
              ))
          }

          # 过滤无死亡记录的数据并计算超额死亡
          data <- data %>%
            filter(NO_DEATHS > 0) %>%
            mutate(EXCESS_DEATHS = NO_DEATHS - EXP_DEATHS)

          if (nrow(data) == 0) {
            return(plot_ly() %>%
              add_annotations(
                text = "No data with reported deaths available.",
                x = 0.5, y = 0.5, showarrow = FALSE
              ))
          }

          # 计算置信区间
          has_ci <- FALSE
          if ("LOWER_LIMIT" %in% names(data) && "UPPER_LIMIT" %in% names(data)) {
            data <- data %>% mutate(
              EXCESS_LOWER = NO_DEATHS - UPPER_LIMIT,
              EXCESS_UPPER = NO_DEATHS - LOWER_LIMIT
            )
            has_ci <- TRUE
          }

          message("ED plot created successfully")

          # 创建基础 plotly 对象
          p <- plot_ly()

          # 为每个模型添加柱状图
          for (model_name in unique(data$Model)) {
            model_data <- data[data$Model == model_name, ]
            bar_color <- if (model_name == "Historical Average") "rgba(0, 139, 139, 0.7)" else "rgba(205, 92, 92, 0.7)"

            p <- p %>% add_bars(
              data = model_data,
              x = ~TimePoint,
              y = ~EXCESS_DEATHS,
              name = model_name,
              marker = list(color = bar_color),
              error_y = if (has_ci) {
                list(
                  type = "data",
                  array = model_data$EXCESS_UPPER - model_data$EXCESS_DEATHS,
                  arrayminus = model_data$EXCESS_DEATHS - model_data$EXCESS_LOWER,
                  color = "gray40",
                  thickness = 0.5,
                  width = 3
                )
              } else {
                NULL
              }
            )
          }

          # 添加事件标记为彩色矩形而不是垂直线
          event_data <- data %>%
            filter(!is.na(event_index), event_index != "0", !is.na(event_name)) %>%
            group_by(event_index, event_name) %>%
            summarise(
              start_time = min(TimePoint, na.rm = TRUE),
              end_time = max(TimePoint, na.rm = TRUE),
              max_excess = max(EXCESS_DEATHS, na.rm = TRUE),
              .groups = "drop"
            )

          # 定义事件的色彩谱 - 使用与主图不同的颜色方案
          # 使用Set3调色板，它提供更多对比色且较浅的颜色
          event_colors <- RColorBrewer::brewer.pal(min(12, max(3, nrow(event_data))), "Set3")

          if (nrow(event_data) > 0) {
            # 为每个事件期间添加彩色矩形
            for (i in 1:nrow(event_data)) {
              # 如果事件数量超过颜色数量，使用模运算循环使用颜色
              color_index <- ((i-1) %% length(event_colors)) + 1

              y_min_val <- min(0, min(data$EXCESS_DEATHS, na.rm = TRUE))
              y_max_val <- max(data$EXCESS_DEATHS, na.rm = TRUE) * 1.1

              # 确保日期类型正确
              start_time <- event_data$start_time[i]
              end_time <- event_data$end_time[i]

              p <- p %>%
                add_trace(
                  type = "scatter",
                  mode = "none",
                  x = c(start_time, end_time, end_time, start_time),
                  y = c(y_min_val, y_min_val, y_max_val, y_max_val),
                  fill = "toself",
                  fillcolor = event_colors[color_index],
                  line = list(color = "transparent"),
                  opacity = 0.08, # 增加透明度，使背景更淡
                  name = event_data$event_name[i],
                  showlegend = TRUE,
                  hoverinfo = "name"
                ) %>%
                add_annotations(
                  x = date_midpoint(event_data$start_time[i], event_data$end_time[i]), # 将标签居中
                  y = y_max_val * 0.95, # Position consistently at 95% of the max y value
                  text = event_data$event_name[i],
                  showarrow = FALSE,
                  font = list(color = "darkred", size = 10)
                )
            }
          }

          # 设置布局
          plot_subtitle <- paste("Sex:", input$selected_sex, "| Age Group:", input$selected_age_group)

          p <- p %>% layout(
            title = list(
              text = paste("Excess Deaths<br><sup>", plot_subtitle, "</sup>"),
              font = list(size = 16)
            ),
            xaxis = list(
              title = "Time",
              tickangle = 45
            ),
            yaxis = list(title = "Excess Deaths"),
            barmode = "group",
            legend = list(orientation = "h", y = -0.2),
            hovermode = "closest"
          )

          return(p)
        },
        error = function(e) {
          message("Error in ED plot: ", e$message)
          return(plot_ly() %>%
            add_annotations(
              text = paste("Error:", e$message),
              x = 0.5, y = 0.5, showarrow = FALSE
            ))
        }
      )
    })

    # 修改 ED Line 图表渲染函数
    output$EDLinePlot <- renderPlotly({
      # 添加调试信息
      message("Rendering ED Line plot...")

      # 使用tryCatch捕获错误
      tryCatch(
        {
          data <- filtered_total_data()
          if (is.null(data) || nrow(data) == 0) {
            message("No data available for ED Line plot")
            return(plot_ly() %>%
              add_annotations(
                text = "No data available for selected filters.",
                x = 0.5, y = 0.5, showarrow = FALSE
              ))
          }

          # 过滤无死亡记录的数据并计算超额死亡
          data <- data %>%
            filter(NO_DEATHS > 0) %>%
            mutate(EXCESS_DEATHS = NO_DEATHS - EXP_DEATHS)

          if (nrow(data) == 0) {
            return(plot_ly() %>%
              add_annotations(
                text = "No data with reported deaths available.",
                x = 0.5, y = 0.5, showarrow = FALSE
              ))
          }

          # 计算置信区间
          has_ci <- FALSE
          if ("LOWER_LIMIT" %in% names(data) && "UPPER_LIMIT" %in% names(data)) {
            data <- data %>% mutate(
              EXCESS_LOWER = NO_DEATHS - UPPER_LIMIT,
              EXCESS_UPPER = NO_DEATHS - LOWER_LIMIT
            )
            has_ci <- TRUE
          }

          message("ED Line plot created successfully")

          # 创建基础 plotly 对象
          p <- plot_ly()

          # 为每个模型添加线图
          for (model_name in unique(data$Model)) {
            model_data <- data[data$Model == model_name, ]
            line_color <- if (model_name == "Historical Average") "cyan3" else "indianred"

            p <- p %>% add_trace(
              data = model_data,
              x = ~TimePoint,
              y = ~EXCESS_DEATHS,
              name = model_name,
              type = "scatter",
              mode = "lines",
              line = list(color = line_color, width = 2)
            )

            # 添加置信区间
            if (has_ci) {
              p <- p %>% add_ribbons(
                data = model_data,
                x = ~TimePoint,
                ymin = ~EXCESS_LOWER,
                ymax = ~EXCESS_UPPER,
                name = paste(model_name, "95% CI"),
                line = list(color = "transparent"),
                fillcolor = adjustcolor(line_color, alpha.f = 0.2),
                showlegend = FALSE
              )
            }
          }

          # 添加事件标记为彩色矩形而不是垂直线
          event_data <- data %>%
            filter(!is.na(event_index), event_index != "0", !is.na(event_name)) %>%
            group_by(event_index, event_name) %>%
            summarise(
              start_time = min(TimePoint, na.rm = TRUE),
              end_time = max(TimePoint, na.rm = TRUE),
              max_excess = max(EXCESS_DEATHS, na.rm = TRUE),
              .groups = "drop"
            )

          # 定义事件的色彩谱 - 使用与主图不同的颜色方案
          # 使用Set3调色板，它提供更多对比色且较浅的颜色
          event_colors <- RColorBrewer::brewer.pal(min(12, max(3, nrow(event_data))), "Set3")

          if (nrow(event_data) > 0) {
            # 为每个事件期间添加彩色矩形
            for (i in 1:nrow(event_data)) {
              # 如果事件数量超过颜色数量，使用模运算循环使用颜色
              color_index <- ((i-1) %% length(event_colors)) + 1

              y_min_val <- min(0, min(data$EXCESS_DEATHS, na.rm = TRUE))
              y_max_val <- max(data$EXCESS_DEATHS, na.rm = TRUE) * 1.1

              # 确保日期类型正确
              start_time <- event_data$start_time[i]
              end_time <- event_data$end_time[i]

              p <- p %>%
                add_trace(
                  type = "scatter",
                  mode = "none",
                  x = c(start_time, end_time, end_time, start_time),
                  y = c(y_min_val, y_min_val, y_max_val, y_max_val),
                  fill = "toself",
                  fillcolor = event_colors[color_index],
                  line = list(color = "transparent"),
                  opacity = 0.08, # 增加透明度，使背景更淡
                  name = event_data$event_name[i],
                  showlegend = TRUE,
                  hoverinfo = "name"
                ) %>%
                add_annotations(
                  x = date_midpoint(event_data$start_time[i], event_data$end_time[i]), # 将标签居中
                  y = y_max_val * 0.95, # Position consistently at 95% of the max y value
                  text = event_data$event_name[i],
                  showarrow = FALSE,
                  font = list(color = "darkred", size = 10)
                )
            }
          }

          # 设置布局
          plot_subtitle <- paste("Sex:", input$selected_sex, "| Age Group:", input$selected_age_group)

          p <- p %>% layout(
            title = list(
              text = paste("Excess Deaths (Line Plot)<br><sup>", plot_subtitle, "</sup>"),
              font = list(size = 16)
            ),
            xaxis = list(
              title = "Time",
              tickangle = 45
            ),
            yaxis = list(title = "Excess Deaths"),
            legend = list(orientation = "h", y = -0.2),
            hovermode = "closest"
          )

          return(p)
        },
        error = function(e) {
          message("Error in ED Line plot: ", e$message)
          return(plot_ly() %>%
            add_annotations(
              text = paste("Error:", e$message),
              x = 0.5, y = 0.5, showarrow = FALSE
            ))
        }
      )
    })

    # 修改 P-Score 图表渲染函数
    output$EPplot <- renderPlotly({
      # 添加调试信息
      message("Rendering EP plot...")

      # 使用tryCatch捕获错误
      tryCatch(
        {
          data <- filtered_total_data()
          if (is.null(data) || nrow(data) == 0) {
            message("No data available for EP plot")
            return(plot_ly() %>%
              add_annotations(
                text = "No data available for selected filters.",
                x = 0.5, y = 0.5, showarrow = FALSE
              ))
          }

          # 过滤无死亡记录的数据并计算P-Score
          data <- data %>%
            filter(NO_DEATHS > 0, EXP_DEATHS > 0) %>%
            mutate(P_SCORE = (NO_DEATHS - EXP_DEATHS) / EXP_DEATHS * 100)

          if (nrow(data) == 0) {
            return(plot_ly() %>%
              add_annotations(
                text = "No data with reported deaths available.",
                x = 0.5, y = 0.5, showarrow = FALSE
              ))
          }

          # 计算置信区间
          has_ci <- FALSE
          if ("LOWER_LIMIT" %in% names(data) && "UPPER_LIMIT" %in% names(data)) {
            data <- data %>% mutate(
              P_SCORE_LOWER = (NO_DEATHS - UPPER_LIMIT) / EXP_DEATHS * 100,
              P_SCORE_UPPER = (NO_DEATHS - LOWER_LIMIT) / EXP_DEATHS * 100
            )
            has_ci <- TRUE
          }

          message("EP plot created successfully")

          # 创建基础 plotly 对象
          p <- plot_ly()

          # 为每个模型添加柱状图
          for (model_name in unique(data$Model)) {
            model_data <- data[data$Model == model_name, ]
            bar_color <- if (model_name == "Historical Average") "rgba(0, 139, 139, 0.7)" else "rgba(205, 92, 92, 0.7)"

            p <- p %>% add_bars(
              data = model_data,
              x = ~TimePoint,
              y = ~P_SCORE,
              name = model_name,
              marker = list(color = bar_color),
              error_y = if (has_ci) {
                list(
                  type = "data",
                  array = model_data$P_SCORE_UPPER - model_data$P_SCORE,
                  arrayminus = model_data$P_SCORE - model_data$P_SCORE_LOWER,
                  color = "gray40",
                  thickness = 0.5,
                  width = 3
                )
              } else {
                NULL
              }
            )
          }

          # 添加事件标记为彩色矩形而不是垂直线
          event_data <- data %>%
            filter(!is.na(event_index), event_index != "0", !is.na(event_name)) %>%
            group_by(event_index, event_name) %>%
            summarise(
              start_time = min(TimePoint, na.rm = TRUE),
              end_time = max(TimePoint, na.rm = TRUE),
              max_pscore = max(P_SCORE, na.rm = TRUE),
              .groups = "drop"
            )

          # 定义事件的色彩谱 - 使用与主图不同的颜色方案
          # 使用Set3调色板，它提供更多对比色且较浅的颜色
          event_colors <- RColorBrewer::brewer.pal(min(12, max(3, nrow(event_data))), "Set3")

          if (nrow(event_data) > 0) {
            # 为每个事件期间添加彩色矩形
            for (i in 1:nrow(event_data)) {
              # 如果事件数量超过颜色数量，使用模运算循环使用颜色
              color_index <- ((i-1) %% length(event_colors)) + 1

              y_min_val <- min(0, min(data$P_SCORE, na.rm = TRUE))
              y_max_val <- max(data$P_SCORE, na.rm = TRUE) * 1.1

              # 确保日期类型正确
              start_time <- event_data$start_time[i]
              end_time <- event_data$end_time[i]

              p <- p %>%
                add_trace(
                  type = "scatter",
                  mode = "none",
                  x = c(start_time, end_time, end_time, start_time),
                  y = c(y_min_val, y_min_val, y_max_val, y_max_val),
                  fill = "toself",
                  fillcolor = event_colors[color_index],
                  line = list(color = "transparent"),
                  opacity = 0.08, # 增加透明度，使背景更淡
                  name = event_data$event_name[i],
                  showlegend = TRUE,
                  hoverinfo = "name"
                ) %>%
                add_annotations(
                  x = date_midpoint(event_data$start_time[i], event_data$end_time[i]), # 将标签居中
                  y = y_max_val * 0.95, # Position consistently at 95% of the max y value
                  text = event_data$event_name[i],
                  showarrow = FALSE,
                  font = list(color = "darkred", size = 10)
                )
            }
          }

          # 添加零线
          p <- p %>% add_trace(
            x = c(min(data$TimePoint, na.rm = TRUE), max(data$TimePoint, na.rm = TRUE)),
            y = c(0, 0),
            type = "scatter",
            mode = "lines",
            line = list(color = "black", dash = "dot", width = 1),
            showlegend = FALSE,
            hoverinfo = "none"
          )

          # 设置布局
          plot_subtitle <- paste("Sex:", input$selected_sex, "| Age Group:", input$selected_age_group)

          p <- p %>% layout(
            title = list(
              text = paste("P-Score of Excess Deaths (%)<br><sup>", plot_subtitle, "</sup>"),
              font = list(size = 16)
            ),
            xaxis = list(
              title = "Time",
              tickangle = 45
            ),
            yaxis = list(title = "P-Score (%)"),
            barmode = "group",
            legend = list(orientation = "h", y = -0.2),
            hovermode = "closest"
          )

          return(p)
        },
        error = function(e) {
          message("Error in EP plot: ", e$message)
          return(plot_ly() %>%
            add_annotations(
              text = paste("Error:", e$message),
              x = 0.5, y = 0.5, showarrow = FALSE
            ))
        }
      )
    })

    # 修改 P-Score 线图渲染函数
    output$EPLinePlot <- renderPlotly({
      # 添加调试信息
      message("Rendering EP Line plot...")

      # 使用tryCatch捕获错误
      tryCatch(
        {
          data <- filtered_total_data()
          if (is.null(data) || nrow(data) == 0) {
            message("No data available for EP Line plot")
            return(plot_ly() %>%
              add_annotations(
                text = "No data available for selected filters.",
                x = 0.5, y = 0.5, showarrow = FALSE
              ))
          }

          # 过滤无死亡记录的数据并计算P-Score
          data <- data %>%
            filter(NO_DEATHS > 0, EXP_DEATHS > 0) %>%
            mutate(P_SCORE = (NO_DEATHS - EXP_DEATHS) / EXP_DEATHS * 100)

          if (nrow(data) == 0) {
            return(plot_ly() %>%
              add_annotations(
                text = "No data with reported deaths available.",
                x = 0.5, y = 0.5, showarrow = FALSE
              ))
          }

          # 计算置信区间
          has_ci <- FALSE
          if ("LOWER_LIMIT" %in% names(data) && "UPPER_LIMIT" %in% names(data)) {
            data <- data %>% mutate(
              P_SCORE_LOWER = (NO_DEATHS - UPPER_LIMIT) / EXP_DEATHS * 100,
              P_SCORE_UPPER = (NO_DEATHS - LOWER_LIMIT) / EXP_DEATHS * 100
            )
            has_ci <- TRUE
          }

          message("EP Line plot created successfully")

          # 创建基础 plotly 对象
          p <- plot_ly()

          # 为每个模型添加线图
          for (model_name in unique(data$Model)) {
            model_data <- data[data$Model == model_name, ]
            line_color <- if (model_name == "Historical Average") "cyan3" else "indianred"

            p <- p %>% add_trace(
              data = model_data,
              x = ~TimePoint,
              y = ~P_SCORE,
              name = model_name,
              type = "scatter",
              mode = "lines",
              line = list(color = line_color, width = 2)
            )

            # 添加置信区间
            if (has_ci) {
              p <- p %>% add_ribbons(
                data = model_data,
                x = ~TimePoint,
                ymin = ~P_SCORE_LOWER,
                ymax = ~P_SCORE_UPPER,
                name = paste(model_name, "95% CI"),
                line = list(color = "transparent"),
                fillcolor = adjustcolor(line_color, alpha.f = 0.2),
                showlegend = FALSE
              )
            }
          }

          # 添加事件标记为彩色矩形而不是垂直线
          event_data <- data %>%
            filter(!is.na(event_index), event_index != "0", !is.na(event_name)) %>%
            group_by(event_index, event_name) %>%
            summarise(
              start_time = min(TimePoint, na.rm = TRUE),
              end_time = max(TimePoint, na.rm = TRUE),
              max_pscore = max(P_SCORE, na.rm = TRUE),
              .groups = "drop"
            )

          # 定义事件的色彩谱 - 使用与主图不同的颜色方案
          # 使用Set3调色板，它提供更多对比色且较浅的颜色
          event_colors <- RColorBrewer::brewer.pal(min(12, max(3, nrow(event_data))), "Set3")

          if (nrow(event_data) > 0) {
            # 为每个事件期间添加彩色矩形
            for (i in 1:nrow(event_data)) {
              # 如果事件数量超过颜色数量，使用模运算循环使用颜色
              color_index <- ((i-1) %% length(event_colors)) + 1

              y_min_val <- min(0, min(data$P_SCORE, na.rm = TRUE))
              y_max_val <- max(data$P_SCORE, na.rm = TRUE) * 1.1

              # 确保日期类型正确
              start_time <- event_data$start_time[i]
              end_time <- event_data$end_time[i]

              p <- p %>%
                add_trace(
                  type = "scatter",
                  mode = "none",
                  x = c(start_time, end_time, end_time, start_time),
                  y = c(y_min_val, y_min_val, y_max_val, y_max_val),
                  fill = "toself",
                  fillcolor = event_colors[color_index],
                  line = list(color = "transparent"),
                  opacity = 0.08, # 增加透明度，使背景更淡
                  name = event_data$event_name[i],
                  showlegend = TRUE,
                  hoverinfo = "name"
                ) %>%
                add_annotations(
                  x = date_midpoint(event_data$start_time[i], event_data$end_time[i]), # 将标签居中
                  y = y_max_val * 0.95, # Position consistently at 95% of the max y value
                  text = event_data$event_name[i],
                  showarrow = FALSE,
                  font = list(color = "darkred", size = 10)
                )
            }
          }

          # 添加零线
          p <- p %>% add_trace(
            x = c(min(data$TimePoint, na.rm = TRUE), max(data$TimePoint, na.rm = TRUE)),
            y = c(0, 0),
            type = "scatter",
            mode = "lines",
            line = list(color = "black", dash = "dot", width = 1),
            showlegend = FALSE,
            hoverinfo = "none"
          )

          # 设置布局
          plot_subtitle <- paste("Sex:", input$selected_sex, "| Age Group:", input$selected_age_group)

          p <- p %>% layout(
            title = list(
              text = paste("P-Score (Line Plot)<br><sup>", plot_subtitle, "</sup>"),
              font = list(size = 16)
            ),
            xaxis = list(
              title = "Time",
              tickangle = 45
            ),
            yaxis = list(title = "P-Score (%)"),
            legend = list(orientation = "h", y = -0.2),
            hovermode = "closest"
          )

          return(p)
        },
        error = function(e) {
          message("Error in EP Line plot: ", e$message)
          return(plot_ly() %>%
            add_annotations(
              text = paste("Error:", e$message),
              x = 0.5, y = 0.5, showarrow = FALSE
            ))
        }
      )
    })
    # --- Download handlers ---
    output$downloadACMPlot <- downloadHandler(
      filename = function() {
        paste0("ACM_Plot_", input$selected_sex, "_", input$selected_age_group, "_", Sys.Date(), ".pdf")
      },
      content = function(file) {
        req(filtered_total_data())
        tryCatch(
          {
            plot_obj <- create_acm_plot(filtered_total_data())
            ggsave(file, plot = plot_obj, device = "pdf", width = 11, height = 7, units = "in")
          },
          error = function(e) {
            message("Error in downloading ACM plot: ", e$message)
          }
        )
      }
    )

    output$downloadEDPlot <- downloadHandler(
      filename = function() {
        paste0("ExcessDeaths_Plot_", input$selected_sex, "_", input$selected_age_group, "_", Sys.Date(), ".pdf")
      },
      content = function(file) {
        req(filtered_total_data())
        tryCatch(
          {
            plot_obj <- create_ed_plot(filtered_total_data())
            ggsave(file, plot = plot_obj, device = "pdf", width = 11, height = 7, units = "in")
          },
          error = function(e) {
            message("Error in downloading ED plot: ", e$message)
          }
        )
      }
    )

    output$downloadEPPlot <- downloadHandler(
      filename = function() {
        paste0("PScore_Plot_", input$selected_sex, "_", input$selected_age_group, "_", Sys.Date(), ".pdf")
      },
      content = function(file) {
        req(filtered_total_data())
        tryCatch(
          {
            plot_obj <- create_ep_plot(filtered_total_data())
            ggsave(file, plot = plot_obj, device = "pdf", width = 11, height = 7, units = "in")
          },
          error = function(e) {
            message("Error in downloading EP plot: ", e$message)
          }
        )
      }
    )

    # 保存数据到全局响应式值
    observe({
      rv$plot_time_data <- filtered_total_data()
    })

    # 恢复原始区域设置
    # onSessionEnded(function() {
    #   Sys.setlocale("LC_TIME", original_locale)
    # })
  })
}
