# Report Module
# 负责生成包含表格、图表和总结文字的HTML报告

# 加载必要的库
library(shiny)
library(dplyr)
library(DT)
library(ggplot2)
library(plotly)
library(htmltools)
library(rmarkdown)
library(scales)  # 用于格式化轴标签
library(RColorBrewer)  # 用于颜色调色板
library(base64enc)  # 用于base64编码图像

# Source plotting functions
source("modules/plot_functions_report.R")

# Define UI interface for report module
report_module_ui <- function(id) {
  ns <- NS(id)
  fluidPage(
    # Add necessary CSS
    tags$head(
      tags$style(HTML("
        .shiny-output-error { visibility: hidden; }
        .shiny-output-error:before { visibility: hidden; }

        /* 数据未就绪提示样式 */
        .data-not-ready {
            text-align: center;
            padding: 50px;
            color: #666;
            background: #f9f9f9;
            border-radius: 5px;
            margin: 20px;
        }

        /* 报告生成按钮样式 */
        .report-btn {
            margin-top: 20px;
            margin-bottom: 20px;
        }
      "))
    ),

    # 添加数据检查条件面板
    conditionalPanel(
      condition = "!output.has_model_data",
      ns = ns,
      div(
        class = "data-not-ready",
        style = "text-align: center; margin-top: 50px;",
        h3("Model results are not available. Please run the model first...", style = "color: #666;"),
        br(),
        actionButton(ns("goto_model_tab"), "Go to Model tab",
          style = "margin-top: 20px; background-color: #337ab7; color: white;"
        )
      )
    ),

    # 原有的UI内容放在条件面板中
    conditionalPanel(
      condition = "output.has_model_data",
      ns = ns,
      fluidRow(
        # Left side for report options
        column(
          3,
          wellPanel(
            h4("Report Options"),

            # Sex filter
            uiOutput(ns("sex_filter")),

            # Age group filter
            uiOutput(ns("age_group_filter")),

            # Model filter
            uiOutput(ns("model_filter")),

            # Event filter
            uiOutput(ns("event_filter")),

            # Report sections to include
            h4("Report Sections"),
            checkboxInput(ns("include_summary"), "Include Summary", value = TRUE),
            checkboxInput(ns("include_tables"), "Include Tables", value = TRUE),
            checkboxInput(ns("include_plots"), "Include Plots", value = TRUE),

            # Generate report button
            div(class = "report-btn",
                actionButton(ns("generate_report"), "Generate Report",
                             class = "btn-primary btn-block")
            ),

            # Download report button (only shown after report is generated)
            conditionalPanel(
              condition = "output.report_ready",
              ns = ns,
              downloadButton(ns("download_report"), "Download Report",
                             class = "btn-success btn-block")
            )
          )
        ),

        # Right side for report preview
        column(
          9,
          wellPanel(
            h4("Report Preview"),
            htmlOutput(ns("report_preview"))
          )
        )
      )
    )
  )
}

# Define server logic for report module
report_module_server <- function(id, rv) {
  moduleServer(
    id,
    function(input, output, session) {
      # 添加数据状态检查
      output$has_model_data <- reactive({
        !is.null(rv$total_prediction) && !is.null(rv$summary_by_event) && !is.null(rv$overall_summary)
      })
      outputOptions(output, "has_model_data", suspendWhenHidden = FALSE)

      # 报告生成状态
      report_generated <- reactiveVal(FALSE)

      # 报告内容
      report_content <- reactiveVal(NULL)

      # 报告文件路径
      report_file_path <- reactiveVal(NULL)

      # 报告是否准备好
      output$report_ready <- reactive({
        report_generated()
      })
      outputOptions(output, "report_ready", suspendWhenHidden = FALSE)

      # 动态生成过滤器
      output$sex_filter <- renderUI({
        req(rv$overall_summary)
        choices <- unique(rv$overall_summary$SEX)
        selectInput(session$ns("selected_sex"), "Select Sex:",
          choices = choices,
          selected = if ("Total" %in% choices) "Total" else choices[1]
        )
      })

      output$age_group_filter <- renderUI({
        req(rv$overall_summary)
        choices <- unique(rv$overall_summary$AGE_GROUP)
        selectInput(session$ns("selected_age_group"), "Select Age Group:",
          choices = choices,
          selected = if ("Total" %in% choices) "Total" else choices[1]
        )
      })

      output$model_filter <- renderUI({
        req(rv$overall_summary)
        choices <- unique(rv$overall_summary$Model)
        checkboxGroupInput(session$ns("selected_models"), "Select Models:",
          choices = choices,
          selected = choices
        )
      })

      output$event_filter <- renderUI({
        req(rv$summary_by_event)
        choices <- unique(rv$summary_by_event$event_name)
        selectInput(session$ns("selected_event"), "Select Event:",
          choices = c("All", choices),
          selected = "All"
        )
      })

      # 过滤数据
      filtered_total_predictions <- reactive({
        req(rv$total_prediction, input$selected_sex, input$selected_age_group, input$selected_models)

        data <- rv$total_prediction %>%
          filter(
            SEX == input$selected_sex,
            AGE_GROUP == input$selected_age_group,
            Model %in% input$selected_models
          )

        return(data)
      })

      filtered_summary_by_event <- reactive({
        req(rv$summary_by_event, input$selected_sex, input$selected_age_group, input$selected_models)

        data <- rv$summary_by_event %>%
          filter(
            SEX == input$selected_sex,
            AGE_GROUP == input$selected_age_group,
            Model %in% input$selected_models
          )

        # 如果选择了特定事件，则进一步过滤
        if (!is.null(input$selected_event) && input$selected_event != "All") {
          data <- data %>% filter(event_name == input$selected_event)
        }

        return(data)
      })

      filtered_overall_summary <- reactive({
        req(rv$overall_summary, input$selected_sex, input$selected_age_group, input$selected_models)

        data <- rv$overall_summary %>%
          filter(
            SEX == input$selected_sex,
            AGE_GROUP == input$selected_age_group,
            Model %in% input$selected_models
          )

        return(data)
      })

      # 创建总结文字
      create_summary_text <- function(overall_summary, summary_by_event) {
        # 创建总体摘要
        overall_text <- ""

        if (nrow(overall_summary) > 0) {
          overall_text <- "<h3>Overall Summary</h3>"

          # 检查列名 - 添加调试信息
          message("Overall summary columns: ", paste(names(overall_summary), collapse=", "))

          # 检查列名 - 使用正确的列名
          excess_col <- intersect(c("TOTAL_EXCESS", "total_excess", "EXCESS_DEATHS"), names(overall_summary))[1]
          pscore_col <- intersect(c("P_SCORE", "p_score", "p_score_pct"), names(overall_summary))[1]

          message("Overall - Found excess_col: ", excess_col)
          message("Overall - Found pscore_col: ", pscore_col)

          if (!is.na(excess_col) && !is.na(pscore_col)) {
            # 创建表格式的显示
            overall_text <- paste0(
              overall_text,
              "<table class='table table-striped table-bordered'>",
              "<thead><tr><th>Model</th><th>Total Excess Deaths (95% CI)</th><th>Average P-Score (%) (95% CI)</th></tr></thead>",
              "<tbody>"
            )

            # 按模型分组
            models <- unique(overall_summary$Model)
            for (model in models) {
              model_data <- overall_summary %>% filter(Model == model)

              # 获取该模型的总超额死亡和平均P-score
              # 添加调试信息
              message("Overall - Model: ", model, ", rows: ", nrow(model_data))
              if (nrow(model_data) > 0) {
                message("Overall - Model data columns: ", paste(names(model_data), collapse=", "))
              }

              # 使用更安全的方式获取数据
              model_excess <- NA
              model_pscore <- NA
              model_excess_lower <- NA
              model_excess_upper <- NA
              model_pscore_lower <- NA
              model_pscore_upper <- NA

              if (nrow(model_data) > 0 && excess_col %in% names(model_data)) {
                model_excess <- model_data[[excess_col]][1]
                message("Overall - Found model_excess: ", model_excess)

                # 检查是否有置信区间列
                if ("EXCESS_LOWER" %in% names(model_data) && "EXCESS_UPPER" %in% names(model_data)) {
                  model_excess_lower <- model_data[["EXCESS_LOWER"]][1]
                  model_excess_upper <- model_data[["EXCESS_UPPER"]][1]
                  message("Overall - Found model_excess CI: (", model_excess_lower, ", ", model_excess_upper, ")")
                }
              }

              if (nrow(model_data) > 0 && pscore_col %in% names(model_data)) {
                model_pscore <- model_data[[pscore_col]][1]
                message("Overall - Found model_pscore: ", model_pscore)

                # 计算P-Score的置信区间（如果有EXCESS_LOWER和EXCESS_UPPER）
                if ("EXCESS_LOWER" %in% names(model_data) && "EXCESS_UPPER" %in% names(model_data) && "TOTAL_EXPECTED" %in% names(model_data)) {
                  total_expected <- model_data[["TOTAL_EXPECTED"]][1]
                  if (!is.na(total_expected) && total_expected > 0) {
                    model_pscore_lower <- 100 * model_data[["EXCESS_LOWER"]][1] / total_expected
                    model_pscore_upper <- 100 * model_data[["EXCESS_UPPER"]][1] / total_expected
                    message("Overall - Calculated model_pscore CI: (", model_pscore_lower, ", ", model_pscore_upper, ")")
                  }
                }
              }

              # 添加到表格中，包含置信区间
              excess_text <- if (!is.na(model_excess)) {
                if (!is.na(model_excess_lower) && !is.na(model_excess_upper)) {
                  paste0(round(model_excess), " (", round(model_excess_lower), ", ", round(model_excess_upper), ")")
                } else {
                  as.character(round(model_excess))
                }
              } else {
                "N/A"
              }

              pscore_text <- if (!is.na(model_pscore)) {
                if (!is.na(model_pscore_lower) && !is.na(model_pscore_upper)) {
                  paste0(round(model_pscore, 2), " (", round(model_pscore_lower, 2), ", ", round(model_pscore_upper, 2), ")")
                } else {
                  as.character(round(model_pscore, 2))
                }
              } else {
                "N/A"
              }

              overall_text <- paste0(
                overall_text,
                "<tr>",
                "<td><strong>", model, "</strong></td>",
                "<td>", excess_text, "</td>",
                "<td>", pscore_text, "</td>",
                "</tr>"
              )
            }

            # 关闭表格
            overall_text <- paste0(overall_text, "</tbody></table>")
          } else {
            overall_text <- paste0(
              overall_text,
              "<p>Unable to display summary data due to missing columns.</p>"
            )
          }
        }

        # 创建事件摘要
        event_text <- ""

        if (nrow(summary_by_event) > 0) {
          event_text <- "<h3>Event-Specific Summary</h3>"

          # 直接显示每个事件的超额死亡和P-score
          # 按事件名称分组
          events <- unique(summary_by_event$event_name)

          for (event in events) {
            event_text <- paste0(
              event_text,
              "<h4>", event, "</h4>"
            )

            # 获取该事件的数据
            event_data <- summary_by_event %>% filter(event_name == event)

            # 检查是否有数据
            if (nrow(event_data) > 0) {
              # 检查列名 - 使用正确的列名
              excess_col <- intersect(c("TOTAL_EXCESS", "excess", "EXCESS", "EXCESS_DEATHS"), names(event_data))[1]
              pscore_col <- intersect(c("P_SCORE", "p_score", "p_score_pct"), names(event_data))[1]

              # 添加调试信息
              message("Event data columns: ", paste(names(event_data), collapse=", "))
              message("Found excess_col: ", excess_col)
              message("Found pscore_col: ", pscore_col)

              if (!is.na(excess_col) && !is.na(pscore_col)) {
                # 创建表格式的显示
                event_text <- paste0(
                  event_text,
                  "<table class='table table-striped table-bordered'>",
                  "<thead><tr><th>Model</th><th>Excess Deaths (95% CI)</th><th>P-Score (%) (95% CI)</th></tr></thead>",
                  "<tbody>"
                )

                # 按模型分组
                models <- unique(event_data$Model)
                for (model in models) {
                  model_data <- event_data %>% filter(Model == model)

                  # 获取该模型的超额死亡和P-score
                  # 添加调试信息
                  message("Model: ", model, ", rows: ", nrow(model_data))
                  if (nrow(model_data) > 0) {
                    message("Model data columns: ", paste(names(model_data), collapse=", "))
                  }

                  # 使用更安全的方式获取数据
                  model_excess <- NA
                  model_pscore <- NA
                  model_excess_lower <- NA
                  model_excess_upper <- NA
                  model_pscore_lower <- NA
                  model_pscore_upper <- NA

                  if (nrow(model_data) > 0 && excess_col %in% names(model_data)) {
                    model_excess <- model_data[[excess_col]][1]
                    message("Found model_excess: ", model_excess)

                    # 检查是否有置信区间列
                    if ("EXCESS_LOWER" %in% names(model_data) && "EXCESS_UPPER" %in% names(model_data)) {
                      model_excess_lower <- model_data[["EXCESS_LOWER"]][1]
                      model_excess_upper <- model_data[["EXCESS_UPPER"]][1]
                      message("Found model_excess CI: (", model_excess_lower, ", ", model_excess_upper, ")")
                    }
                  }

                  if (nrow(model_data) > 0 && pscore_col %in% names(model_data)) {
                    model_pscore <- model_data[[pscore_col]][1]
                    message("Found model_pscore: ", model_pscore)

                    # 计算P-Score的置信区间（如果有EXCESS_LOWER和EXCESS_UPPER）
                    if ("EXCESS_LOWER" %in% names(model_data) && "EXCESS_UPPER" %in% names(model_data) && "TOTAL_EXPECTED" %in% names(model_data)) {
                      total_expected <- model_data[["TOTAL_EXPECTED"]][1]
                      if (!is.na(total_expected) && total_expected > 0) {
                        model_pscore_lower <- 100 * model_data[["EXCESS_LOWER"]][1] / total_expected
                        model_pscore_upper <- 100 * model_data[["EXCESS_UPPER"]][1] / total_expected
                        message("Calculated model_pscore CI: (", model_pscore_lower, ", ", model_pscore_upper, ")")
                      }
                    }
                  }

                  # 添加到表格中，包含置信区间
                  excess_text <- if (!is.na(model_excess)) {
                    if (!is.na(model_excess_lower) && !is.na(model_excess_upper)) {
                      paste0(round(model_excess), " (", round(model_excess_lower), ", ", round(model_excess_upper), ")")
                    } else {
                      as.character(round(model_excess))
                    }
                  } else {
                    "N/A"
                  }

                  pscore_text <- if (!is.na(model_pscore)) {
                    if (!is.na(model_pscore_lower) && !is.na(model_pscore_upper)) {
                      paste0(round(model_pscore, 2), " (", round(model_pscore_lower, 2), ", ", round(model_pscore_upper, 2), ")")
                    } else {
                      as.character(round(model_pscore, 2))
                    }
                  } else {
                    "N/A"
                  }

                  event_text <- paste0(
                    event_text,
                    "<tr>",
                    "<td><strong>", model, "</strong></td>",
                    "<td>", excess_text, "</td>",
                    "<td>", pscore_text, "</td>",
                    "</tr>"
                  )
                }

                # 关闭表格
                event_text <- paste0(event_text, "</tbody></table>")
              } else {
                event_text <- paste0(
                  event_text,
                  "<p>Unable to display event data due to missing columns.</p>"
                )
              }
            } else {
              event_text <- paste0(
                event_text,
                "<p>No data available for this event.</p>"
              )
            }
          }
        }

        # 组合所有文字
        return(paste0(overall_text, event_text))
      }

      # 创建表格HTML
      create_tables_html <- function(total_predictions, summary_by_event, overall_summary) {
        tables_html <- ""

        # 总预测结果表格
        if (nrow(total_predictions) > 0) {
          # 限制显示的行数
          display_rows <- min(10, nrow(total_predictions))

          # 使用HTML表格代替DT::datatable
          table_data <- head(total_predictions, display_rows)

          # 创建HTML表格
          html_table <- "<table class='table table-striped table-bordered'>"

          # 添加表头
          html_table <- paste0(html_table, "<thead><tr>")
          for (col_name in names(table_data)) {
            html_table <- paste0(html_table, "<th>", col_name, "</th>")
          }
          html_table <- paste0(html_table, "</tr></thead>")

          # 添加表体
          html_table <- paste0(html_table, "<tbody>")
          for (i in 1:nrow(table_data)) {
            html_table <- paste0(html_table, "<tr>")
            for (j in 1:ncol(table_data)) {
              cell_value <- table_data[i, j]
              # 处理NA值
              if (is.na(cell_value)) {
                cell_value <- ""
              }
              html_table <- paste0(html_table, "<td>", cell_value, "</td>")
            }
            html_table <- paste0(html_table, "</tr>")
          }
          html_table <- paste0(html_table, "</tbody></table>")

          tables_html <- paste0(
            tables_html,
            "<h3>Total Prediction Results</h3>",
            "<p>Showing first ", display_rows, " rows of ", nrow(total_predictions), " total rows.</p>",
            html_table
          )
        }

        # 按事件汇总表格
        if (nrow(summary_by_event) > 0) {
          # 使用HTML表格代替DT::datatable
          table_data <- summary_by_event

          # 创建HTML表格
          html_table <- "<table class='table table-striped table-bordered'>"

          # 添加表头
          html_table <- paste0(html_table, "<thead><tr>")
          for (col_name in names(table_data)) {
            html_table <- paste0(html_table, "<th>", col_name, "</th>")
          }
          html_table <- paste0(html_table, "</tr></thead>")

          # 添加表体
          html_table <- paste0(html_table, "<tbody>")
          for (i in 1:nrow(table_data)) {
            html_table <- paste0(html_table, "<tr>")
            for (j in 1:ncol(table_data)) {
              cell_value <- table_data[i, j]
              # 处理NA值
              if (is.na(cell_value)) {
                cell_value <- ""
              }
              html_table <- paste0(html_table, "<td>", cell_value, "</td>")
            }
            html_table <- paste0(html_table, "</tr>")
          }
          html_table <- paste0(html_table, "</tbody></table>")

          tables_html <- paste0(
            tables_html,
            "<h3>Summary by Event</h3>",
            html_table
          )
        }

        # 总体汇总表格
        if (nrow(overall_summary) > 0) {
          # 使用HTML表格代替DT::datatable
          table_data <- overall_summary

          # 创建HTML表格
          html_table <- "<table class='table table-striped table-bordered'>"

          # 添加表头
          html_table <- paste0(html_table, "<thead><tr>")
          for (col_name in names(table_data)) {
            html_table <- paste0(html_table, "<th>", col_name, "</th>")
          }
          html_table <- paste0(html_table, "</tr></thead>")

          # 添加表体
          html_table <- paste0(html_table, "<tbody>")
          for (i in 1:nrow(table_data)) {
            html_table <- paste0(html_table, "<tr>")
            for (j in 1:ncol(table_data)) {
              cell_value <- table_data[i, j]
              # 处理NA值
              if (is.na(cell_value)) {
                cell_value <- ""
              }
              html_table <- paste0(html_table, "<td>", cell_value, "</td>")
            }
            html_table <- paste0(html_table, "</tr>")
          }
          html_table <- paste0(html_table, "</tbody></table>")

          tables_html <- paste0(
            tables_html,
            "<h3>Overall Summary</h3>",
            html_table
          )
        }

        return(tables_html)
      }

      # 生成报告
      observeEvent(input$generate_report, {
        # 显示进度条
        withProgress(message = "Generating report...", {
          # 获取过滤后的数据
          total_predictions <- filtered_total_predictions()
          summary_by_event <- filtered_summary_by_event()
          overall_summary <- filtered_overall_summary()

          # 创建报告HTML
          report_html <- paste0(
            "<div class='container'>",
            "<h1>Excess Mortality Analysis Report</h1>",
            "<h2>Generated on ", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "</h2>",
            "<hr>"
          )

          # 添加过滤器信息
          report_html <- paste0(
            report_html,
            "<div class='filter-info'>",
            "<h3>Filter Settings</h3>",
            "<ul>",
            "<li><strong>Sex:</strong> ", input$selected_sex, "</li>",
            "<li><strong>Age Group:</strong> ", input$selected_age_group, "</li>",
            "<li><strong>Models:</strong> ", paste(input$selected_models, collapse = ", "), "</li>"
          )

          if (!is.null(input$selected_event) && input$selected_event != "All") {
            report_html <- paste0(
              report_html,
              "<li><strong>Event:</strong> ", input$selected_event, "</li>"
            )
          }

          report_html <- paste0(
            report_html,
            "</ul>",
            "</div>",
            "<hr>"
          )

          # 添加摘要部分
          if (input$include_summary) {
            incProgress(0.2, detail = "Adding summary...")
            summary_text <- create_summary_text(overall_summary, summary_by_event)
            report_html <- paste0(
              report_html,
              "<div class='summary-section'>",
              "<h2>Summary</h2>",
              summary_text,
              "</div>",
              "<hr>"
            )
          }

          # 添加表格部分
          if (input$include_tables) {
            incProgress(0.4, detail = "Adding tables...")
            tables_html <- create_tables_html(total_predictions, summary_by_event, overall_summary)
            report_html <- paste0(
              report_html,
              "<div class='tables-section'>",
              "<h2>Tables</h2>",
              tables_html,
              "</div>",
              "<hr>"
            )
          }

          # 添加图表部分
          if (input$include_plots) {
            incProgress(0.7, detail = "Adding plots...")
            plots_html <- create_plots_html(total_predictions, input$selected_models)
            report_html <- paste0(
              report_html,
              "<div class='plots-section'>",
              "<h2>Plots</h2>",
              plots_html,
              "</div>"
            )
          }

          # 关闭HTML容器
          report_html <- paste0(
            report_html,
            "</div>"
          )

          # 保存报告内容
          report_content(report_html)

          # 生成临时HTML文件
          temp_file <- tempfile(fileext = ".html")

          # 添加CSS样式
          css_style <- "
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              margin: 20px;
              color: #333;
            }
            .container {
              max-width: 1200px;
              margin: 0 auto;
              padding: 20px;
            }
            h1, h2, h3, h4 {
              color: #2c3e50;
            }
            h1 {
              border-bottom: 2px solid #3498db;
              padding-bottom: 10px;
            }
            h2 {
              border-bottom: 1px solid #bdc3c7;
              padding-bottom: 5px;
              margin-top: 30px;
            }
            hr {
              border: 0;
              height: 1px;
              background: #eee;
              margin: 30px 0;
            }
            table {
              border-collapse: collapse;
              width: 100%;
              margin-bottom: 20px;
            }
            th, td {
              text-align: left;
              padding: 8px;
              border: 1px solid #ddd;
            }
            th {
              background-color: #f2f2f2;
            }
            tr:nth-child(even) {
              background-color: #f9f9f9;
            }
            .filter-info {
              background-color: #f8f9fa;
              padding: 15px;
              border-radius: 5px;
              margin-bottom: 20px;
            }
            .dataTables_wrapper {
              margin-bottom: 30px;
            }
          </style>
          "

          # 将CSS和HTML内容写入文件，添加Font Awesome图标库
          writeLines(paste0("<!DOCTYPE html><html><head><meta charset='UTF-8'><link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css'>", css_style, "</head><body>", report_html, "</body></html>"), temp_file)

          # 保存文件路径
          report_file_path(temp_file)

          # 设置报告已生成标志
          report_generated(TRUE)

          # 完成进度条
          incProgress(1, detail = "Report complete!")
        })
      })

      # 预览报告
      output$report_preview <- renderUI({
        if (report_generated()) {
          HTML(report_content())
        } else {
          HTML("<div style='text-align: center; margin-top: 50px;'><h3>Click 'Generate Report' to create a report based on your selected options.</h3></div>")
        }
      })

      # 下载报告
      output$download_report <- downloadHandler(
        filename = function() {
          paste0("Excess_Mortality_Report_", format(Sys.Date(), "%Y-%m-%d"), ".html")
        },
        content = function(file) {
          file.copy(report_file_path(), file)
        }
      )

      # 在 moduleServer 函数内部添加导航处理器
      observeEvent(input$goto_model_tab, {
        session$sendCustomMessage("navigateTab", "Model")
      })
    }
  )
}