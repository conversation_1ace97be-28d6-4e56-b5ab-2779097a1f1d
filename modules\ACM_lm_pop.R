# ACM Linear Model (GLM Quasipoisson)
# This script defines a function fcn_lm to calculate expected deaths
# using a generalized linear model (quasipoisson family).
# It aims to account for seasonality (PERIOD), trend (YEAR),
# and population structure (offset(log(POPULATION))).

# Ensure dplyr is loaded, as it's used for data manipulation (e.g., %>%)
# install.packages("dplyr") # If not already installed
# library(dplyr)

# It is assumed that a "common_functions.R" script is available in the
# "modules" subdirectory or an appropriate path, providing functions like:
# - calculate_dates(src, max_period, nys, DOM, MOY)
# - initialize_output(src, wm_ident, l_period)
# - process_model_results(out_df, model_name_string)
# If common_functions.R is in a different location, adjust the source path.
# try(source("modules/common_functions.R"), silent = TRUE)


# Helper function: Fit GLM (Quasipoisson) and predict
fit_and_predict_lm_pop <- function(patt_src, hist_src, l_period) {
    t.start <- Sys.time()

    # Ensure PERIOD is a factor for the model in historical data
    hist_src$PERIOD <- as.factor(hist_src$PERIOD)
    # For prediction, newdata (patt_src) should also have PERIOD as a factor with same levels
    # Ensure levels are consistent or handle appropriately.
    # For simplicity, glm will handle factor levels if patt_src$PERIOD is factor.
    patt_src$PERIOD <- as.factor(patt_src$PERIOD)
    # Align levels to avoid errors if patt_src has different periods than hist_src
    # (though typically patt_src is a superset or same set for periods)
    levels(patt_src$PERIOD) <- levels(hist_src$PERIOD)


    model_formula_str <- "NO_DEATHS ~ PERIOD + YEAR + offset(log(POPULATION))"
    model_description <- "GLM Quasipoisson (no population offset)"

    # Check if POPULATION column exists and is usable
    use_population_offset <- FALSE
    if ("POPULATION" %in% names(hist_src) && "POPULATION" %in% names(patt_src)) {
        hist_src$POPULATION <- as.numeric(hist_src$POPULATION)
        patt_src$POPULATION <- as.numeric(patt_src$POPULATION)
        
        if (!any(is.na(hist_src$POPULATION)) && !any(is.na(patt_src$POPULATION)) &&
            all(hist_src$POPULATION > 0, na.rm = TRUE) && all(patt_src$POPULATION > 0, na.rm = TRUE)) {
            use_population_offset <- TRUE
            model_formula_str <- "NO_DEATHS ~ PERIOD + YEAR + offset(log(POPULATION))"
            model_description <- "GLM Quasipoisson (with population offset)"
        } else {
            warning(paste("POPULATION column contains NA, zero, or negative values for the current pattern.",
                          "Model will be fitted without population offset."), call. = FALSE)
        }
    } else {
        warning(paste("POPULATION column not found for the current pattern.",
                      "Model will be fitted without population offset. Consider adding POPULATION data."), call. = FALSE)
    }
    
    message(paste("Fitting model:", model_description))
    
    fit <- tryCatch({
        # stats::glm explicitly calls glm from the stats package
        stats::glm(stats::as.formula(model_formula_str),
                   family = stats::quasipoisson(link = "log"),
                   data = hist_src,
                   control = stats::glm.control(maxit = 100)) # Increased iterations
    }, error = function(e) {
        warning(paste("GLM fitting failed for pattern:", e$message), call. = FALSE)
        return(NULL)
    })

    if (is.null(fit)) {
        return(data.frame()) # Return empty data frame if model fit failed
    }

    # Predictions
    pred_link_scale <- tryCatch({
        stats::predict(fit, newdata = patt_src, type = "link", se.fit = TRUE)
    }, error = function(e) {
        warning(paste("Prediction failed for pattern:", e$message), call. = FALSE)
        return(NULL)
    })

    if (is.null(pred_link_scale)) {
        return(data.frame()) # Return empty data frame if prediction failed
    }

    crit_val <- stats::qnorm(0.975) # For 95% CI
    patt_src$ESTIMATE_link <- pred_link_scale$fit
    patt_src$SE_link <- pred_link_scale$se.fit

    # Inverse transform (exp for log link)
    patt_src$ESTIMATE <- exp(patt_src$ESTIMATE_link)
    patt_src$LOWER_LIMIT <- exp(patt_src$ESTIMATE_link - crit_val * patt_src$SE_link)
    patt_src$UPPER_LIMIT <- exp(patt_src$ESTIMATE_link + crit_val * patt_src$SE_link)
    
    # Ensure non-negative predictions
    patt_src$ESTIMATE[patt_src$ESTIMATE < 0] <- 0
    patt_src$LOWER_LIMIT[patt_src$LOWER_LIMIT < 0] <- 0
    patt_src$UPPER_LIMIT[patt_src$UPPER_LIMIT < 0] <- 0 # Less likely for upper, but good practice

    message(paste("Pattern processed with", model_description, "in", round(difftime(Sys.time(), t.start, units = "secs"), 1), "seconds"))
    
    # Return patt_src, which now includes ESTIMATE, LOWER_LIMIT, UPPER_LIMIT
    return(patt_src)
}

# Helper function: Update output data frame structure with model results for a pattern
update_output_lm_pop <- function(out_data_template_for_pattern, model_results_for_pattern, l_period) {
    if (nrow(model_results_for_pattern) == 0) {
        message("Warning: No model results provided to update_output_lm_pop.")
        # Return the template unchanged but with NAs for estimates if it's not already like that
        out_data_template_for_pattern$ESTIMATE <- NA
        out_data_template_for_pattern$LOWER_LIMIT <- NA
        out_data_template_for_pattern$UPPER_LIMIT <- NA
        return(out_data_template_for_pattern)
    }
    
    output_for_this_pattern <- out_data_template_for_pattern
    
    for (i in 1:nrow(output_for_this_pattern)) {
        row_to_fill <- output_for_this_pattern[i, ]
        y <- row_to_fill$YEAR
        k <- row_to_fill$PERIOD

        # Find corresponding prediction in model_results_for_pattern
        # Ensure PERIOD in model_results_for_pattern is treated as numeric for matching k
        # model_results_for_pattern$PERIOD was factored, so coerce back carefully
        prediction_record <- model_results_for_pattern[
            model_results_for_pattern$YEAR == y &
            as.numeric(as.character(model_results_for_pattern$PERIOD)) == k,
        ]

        if (nrow(prediction_record) == 1) {
            output_for_this_pattern$ESTIMATE[i] <- prediction_record$ESTIMATE
            output_for_this_pattern$LOWER_LIMIT[i] <- prediction_record$LOWER_LIMIT
            output_for_this_pattern$UPPER_LIMIT[i] <- prediction_record$UPPER_LIMIT
        } else if (nrow(prediction_record) > 1) {
            message(paste("Warning: Multiple prediction records found for Year", y, "Period", k, "in pattern. Using the first one."))
            output_for_this_pattern$ESTIMATE[i] <- prediction_record$ESTIMATE[1]
            output_for_this_pattern$LOWER_LIMIT[i] <- prediction_record$LOWER_LIMIT[1]
            output_for_this_pattern$UPPER_LIMIT[i] <- prediction_record$UPPER_LIMIT[1]
        } else {
            # No prediction, ESTIMATE, LOWER, UPPER remain as initialized (e.g., NA)
             output_for_this_pattern$ESTIMATE[i] <- NA
             output_for_this_pattern$LOWER_LIMIT[i] <- NA
             output_for_this_pattern$UPPER_LIMIT[i] <- NA
            # message(paste("Note: No prediction record for Year", y, "Period", k, "in pattern."))
        }
    }
    return(output_for_this_pattern)
}


# Main function for Linear Model (GLM Quasipoisson)
fcn_lm_pop <- function(src) {
    message("\n[fcn_lm] Starting Linear Model (GLM Quasipoisson) calculation...")
    flush.console()
    start_time <- Sys.time()

    # Ensure common functions are available
    common_fcns <- c("calculate_dates", "initialize_output", "process_model_results")
    if (!all(sapply(common_fcns, exists, mode = "function"))) {
        stop(paste("One or more required functions from common_functions.R are not found:",
                   paste(common_fcns[!sapply(common_fcns, exists, mode = "function")], collapse=", "),
                   ". Please ensure common_functions.R is sourced."), call. = FALSE)
    }
    
    # Ensure dplyr is available for the pipe operator and other functions
    if (!requireNamespace("dplyr", quietly = TRUE)) {
        stop("Package 'dplyr' is needed for this function to work. Please install it.", call. = FALSE)
    }


    # Preprocess data
    src_processed <- src %>%
        dplyr::filter(PERIOD <= 52) %>%
        dplyr::arrange(SEX, AGE_GROUP, AREA, CAUSE, YEAR, PERIOD) %>%
        dplyr::mutate(
            NO_DEATHS = as.numeric(NO_DEATHS)
        )
    
    if ("POPULATION" %in% names(src_processed)) {
        src_processed$POPULATION <- as.numeric(src_processed$POPULATION)
    } else {
        message("Warning: POPULATION column not found in the input data for fcn_lm.",
                " The model will be fitted without population adjustment for all patterns,",
                " which might affect accuracy if population structure changes significantly over time.")
    }

    nys <- length(unique(src_processed$YEAR))
    max_period <- max(src_processed$PERIOD, na.rm = TRUE)
    wm_ident <- ifelse(max_period == 12, "Month", "Week")
    l_period <- ifelse(max_period == 12, 12, 52)

    # Define DOM and MOY if not globally available (required by calculate_dates typically)
    if (!exists("DOM")) DOM <- c(31,28,31,30,31,30,31,31,30,31,30,31) # Standard days in month
    if (!exists("MOY")) MOY <- 1:12 # Standard months of year

    src_processed <- calculate_dates(src_processed, max_period, nys, DOM, MOY)
    out_lm_template <- initialize_output(src_processed, wm_ident, l_period)

    patterns <- src_processed %>%
        dplyr::select(SEX, AGE_GROUP, AREA, CAUSE) %>%
        dplyr::distinct() %>%
        dplyr::mutate(patterns_id = paste(SEX, AGE_GROUP, AREA, CAUSE)) %>%
        dplyr::pull(patterns_id)
    n_pat <- length(patterns)

    message(paste("Found", n_pat, "patterns to process."))
    results_list <- list()

    for (j in 1:n_pat) {
        current_pattern_string <- patterns[j]
        message(paste("\nProcessing pattern", j, "/", n_pat, ":", current_pattern_string))

        pattern_parts <- strsplit(current_pattern_string, " ")[[1]]
        
        patt_src <- src_processed[
            src_processed$SEX == pattern_parts[1] &
            src_processed$AGE_GROUP == pattern_parts[2] &
            src_processed$AREA == pattern_parts[3] &
            src_processed$CAUSE == pattern_parts[4],
        ]

        if (nrow(patt_src) < 10) {
            message("Skipping pattern: Insufficient data (less than 10 rows).")
            next
        }

        hist_src <- patt_src[patt_src$event_index == "0", ]

        if (nrow(hist_src) < 5) { # Min data for fitting
            message("Skipping pattern: Insufficient historical data (less than 5 rows for fitting).")
            next
        }
        if (length(unique(hist_src$YEAR)) < 2) {
             message("Skipping pattern: Less than 2 unique YEARs in historical data. Trend cannot be estimated reliably.")
             next
        }
        # For factor(PERIOD), at least one period is needed. If l_period > 1, multiple unique periods are good.
        if (l_period > 1 && length(unique(hist_src$PERIOD)) < 2) {
             message("Skipping pattern: Less than 2 unique PERIODs in historical data for seasonality estimation when l_period > 1.")
             next
        }
         # Check for sufficient non-missing NO_DEATHS in hist_src
        if (sum(!is.na(hist_src$NO_DEATHS)) < 5) {
            message("Skipping pattern: Insufficient non-NA NO_DEATHS in historical data (less than 5).")
            next
        }
        # Check if all NO_DEATHS are zero in hist_src
        if (all(hist_src$NO_DEATHS == 0, na.rm = TRUE)) {
            message("Skipping pattern: All historical NO_DEATHS are zero. Model cannot be reliably fitted.")
            next
        }


        model_results_for_pattern <- fit_and_predict_lm_pop(patt_src, hist_src, l_period)

        if (is.null(model_results_for_pattern) || nrow(model_results_for_pattern) == 0) {
            message("Model fitting or prediction failed for this pattern.")
            next
        }
        
        current_pattern_template_subset <- out_lm_template[
            out_lm_template$SEX == pattern_parts[1] &
            out_lm_template$AGE_GROUP == pattern_parts[2] &
            out_lm_template$AREA == pattern_parts[3] &
            out_lm_template$CAUSE == pattern_parts[4],
        ]

        if(nrow(current_pattern_template_subset) == 0) {
            message(paste("No rows in output template for pattern:", current_pattern_string))
            next
        }
        
        updated_pattern_rows <- update_output_lm_pop(
            current_pattern_template_subset,
            model_results_for_pattern,
            l_period
        )

        if (nrow(updated_pattern_rows) > 0) {
            results_list[[current_pattern_string]] <- updated_pattern_rows
            message(paste("Successfully processed pattern:", current_pattern_string, "(", nrow(updated_pattern_rows), "rows updated)"))
        } else {
            message(paste("No rows updated for pattern:", current_pattern_string))
        }
    }

    if (length(results_list) > 0) {
        out_lm_final <- do.call(rbind, results_list)
        rownames(out_lm_final) <- NULL
        message(paste("\nSuccessfully combined results from", length(results_list), "patterns."))
    } else {
        warning("No patterns were successfully processed! Returning an empty data frame.", call. = FALSE)
        return(out_lm_template[0,]) # Return empty df with correct columns
    }

    out_lm_final <- process_model_results(out_lm_final, "Linear Model (GLM Quasipoisson)")

    end_time <- Sys.time()
    total_time <- difftime(end_time, start_time, units = "mins")
    message(paste("Linear Model (GLM Quasipoisson) calculation completed. Total time:", round(total_time, 2), "minutes."))
    flush.console()

    return(out_lm_final)
}

# Example Usage (assuming 'my_data' is your input dataframe and common_functions.R is sourced):
# Ensure dplyr is loaded: library(dplyr)
# Ensure common_functions.R is sourced: source("modules/common_functions.R")
#
# # Prepare your data 'my_data' with columns:
# # SEX, AGE_GROUP, AREA, CAUSE, YEAR, PERIOD, NO_DEATHS, event_index
# # And optionally: POPULATION
#
# # Example: Create dummy POPULATION if not present for testing
# # if (!"POPULATION" %in% names(my_data)) {
# #   my_data$POPULATION <- runif(nrow(my_data), 10000, 100000) # Example population
# # }
#
# # results_lm <- fcn_lm(my_data)
# # print(head(results_lm))

