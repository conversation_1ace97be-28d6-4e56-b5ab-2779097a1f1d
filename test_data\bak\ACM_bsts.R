# 加载必要的包
library(bsts)
library(dplyr)
library(lubridate)
library(ISOweek)

# 加载公共函数（假设与 NB 模型相同）
source("modules/common_functions.R")

# BSTS 模型拟合和预测函数
fit_and_predict_bsts <- function(patt_src, hist_src, l_period) {
    t.start <- Sys.time()

    # 获取年份数量
    nys <- length(unique(patt_src$YEAR))

    # 根据周期类型设置参数（月/周）
    if (l_period > 51) {
        # 周数据
        num.cycle <- 52
        len.cycle <- 7
        hist_src$logdays <- log(hist_src$DAYS)
        src_pandemic <- patt_src %>%
            mutate(logdays = log(DAYS))
    } else {
        # 月数据
        num.cycle <- 12
        len.cycle <- 30
        hist_src$logdays <- log(hist_src$DAYS)
        src_pandemic <- patt_src %>%
            mutate(logdays = log(DAYS))
    }

    # 构造时间序列（支持月和周数据）
    if (l_period > 51) {
        # 周数据：使用 ISOweek 包计算日期
        hist_src <- hist_src %>%
            arrange(YEAR, PERIOD) %>%
            mutate(
                iso_week = sprintf("%d-W%02d-4", YEAR, PERIOD),
                date = ISOweek2date(iso_week)
            )

        src_pandemic <- src_pandemic %>%
            arrange(YEAR, PERIOD) %>%
            mutate(
                iso_week = sprintf("%d-W%02d-4", YEAR, PERIOD),
                date = ISOweek2date(iso_week)
            )
    } else {
        # 月数据：将日期设置为每月的 15 号
        hist_src <- hist_src %>%
            arrange(YEAR, PERIOD) %>%
            mutate(date = as.Date(paste(YEAR, PERIOD, "15", sep = "-")))

        src_pandemic <- src_pandemic %>%
            arrange(YEAR, PERIOD) %>%
            mutate(date = as.Date(paste(YEAR, PERIOD, "15", sep = "-")))
    }

    # 动态确定未来预测的周期数（horizon）
    last_hist_date <- max(hist_src$date, na.rm = TRUE)
    last_pandemic_date <- max(src_pandemic$date, na.rm = TRUE)

    if (l_period > 51) {
        horizon <- as.numeric(difftime(last_pandemic_date, last_hist_date, units = "weeks"))
    } else {
        horizon <- interval(last_hist_date, last_pandemic_date) %/% months(1)
    }

    horizon <- max(0, floor(horizon))

    # 拟合 BSTS 模型
    ss <- AddLocalLinearTrend(list(), hist_src$NO_DEATHS)
    ss <- AddSeasonal(ss, hist_src$NO_DEATHS, nseasons = num.cycle)

    fit <- tryCatch(
        {
            bsts(
                formula = NO_DEATHS ~ offset(logdays),
                state.specification = ss,
                family = "poisson",
                data = hist_src,
                niter = 1000,
                seed = 1
            )
        },
        error = function(e) {
            warning("BSTS 模型拟合失败: ", e$message)
            return(NULL)
        }
    )

    if (is.null(fit)) {
        return(data.frame())
    }

    # 模型内预测（已知时间段，覆盖 src_pandemic 中 hist_src 的部分）
    estim <- predict(fit, newdata = src_pandemic, horizon = 0)

    # 模型外推测（未来时间段，覆盖 src_pandemic 中超出 hist_src 的部分）
    pred_future <- predict(fit, horizon = horizon)

    # 整合预测结果
    # estim.median 已经包含了 src_pandemic 中 hist_src 部分的预测结果
    # 需要将 pred_future 的结果追加到 estim 中，覆盖 src_pandemic 的剩余部分
    estim.median <- estim$median
    estim.lower <- estim$interval[1, ]
    estim.upper <- estim$interval[2, ]

    # 找到 src_pandemic 中超出 hist_src 的部分
    hist_indices <- which(src_pandemic$date <= last_hist_date)
    future_indices <- which(src_pandemic$date > last_hist_date)

    # 如果有超出 hist_src 的部分，将 pred_future 的结果填入
    if (length(future_indices) > 0) {
        if (length(future_indices) <= length(pred_future$median)) {
            estim.median[future_indices] <- pred_future$median[1:length(future_indices)]
            estim.lower[future_indices] <- pred_future$interval[1, 1:length(future_indices)]
            estim.upper[future_indices] <- pred_future$interval[2, 1:length(future_indices)]
        } else {
            # 如果 future_indices 比 pred_future 长，填充 NA
            estim.median[future_indices] <- c(pred_future$median, rep(NA, length(future_indices) - length(pred_future$median)))
            estim.lower[future_indices] <- c(pred_future$interval[1, ], rep(NA, length(future_indices) - length(pred_future$median)))
            estim.upper[future_indices] <- c(pred_future$interval[2, ], rep(NA, length(future_indices) - length(pred_future$median)))
        }
    }

    # 确保非负值
    estim.median[estim.median < 0] <- 0
    estim.lower[estim.lower < 0] <- 0
    estim.upper[estim.upper < 0] <- 0

    message("模式处理时间: ", round(difftime(Sys.time(), t.start, units = "secs"), 1), " 秒")

    # 输出格式与 fit_and_predict_nb() 一致
    return(list(
        src_pandemic = src_pandemic,
        estim.median = estim.median,
        estim.lower = estim.lower,
        estim.upper = estim.upper
    ))
}

# 更新输出函数（统一处理所有预测结果）
update_output_bsts <- function(out_data, model_results, pattern, year_predict) {
    src_pandemic <- model_results$src_pandemic
    estim.median <- model_results$estim.median
    estim.lower <- model_results$estim.lower
    estim.upper <- model_results$estim.upper

    if (is.null(src_pandemic) || is.null(estim.median) || is.null(estim.lower) || is.null(estim.upper)) {
        message("警告: 模式 ", pattern, " 的模型结果无效")
        return(out_data)
    }

    pattern_parts <- strsplit(pattern, ";")[[1]]
    result_df <- out_data[0, ]

    l_period <- max(out_data$PERIOD, na.rm = TRUE)
    nyear_predict <- length(year_predict)
    for (iyear_predict in 1:nyear_predict) {
        y <- year_predict[iyear_predict]
        for (k in 1:l_period) {
            a <- src_pandemic$YEAR == y & src_pandemic$PERIOD == k
            current_records <- out_data[
                out_data$SEX == pattern_parts[1] &
                    out_data$AGE_GROUP == pattern_parts[2] &
                    out_data$AREA == pattern_parts[3] &
                    out_data$CAUSE == pattern_parts[4] &
                    out_data$YEAR == y &
                    out_data$PERIOD == k,
            ]

            if (nrow(current_records) > 0 && sum(a) > 0) {
                current_records$ESTIMATE <- estim.median[a]
                current_records$LOWER_LIMIT <- estim.lower[a]
                current_records$UPPER_LIMIT <- estim.upper[a]
                result_df <- rbind(result_df, current_records)
            }
        }
    }

    return(result_df)
}

# BSTS 模型主函数
fcn_bsts <- function(src) {
    message("\n[fcn_bsts] 开始 BSTS 模型计算...")
    flush.console()
    start_time <- Sys.time()

    # 预处理数据
    src <- src %>%
        filter(PERIOD <= 52) %>%
        arrange(SEX, AGE_GROUP, AREA, CAUSE, YEAR, PERIOD) %>%
        mutate(NO_DEATHS = as.numeric(NO_DEATHS))

    # 获取基本参数
    nys <- length(unique(src$YEAR))
    max_period <- max(src$PERIOD, na.rm = TRUE)
    wm_ident <- ifelse(max_period == 12, "Month", "Week")
    l_period <- ifelse(max_period == 12, 12, 52)

    # 计算日期
    src <- calculate_dates(src, max_period, nys, DOM, MOY)

    # 初始化输出数据框
    out_data <- initialize_output(src, wm_ident, l_period)

    # 获取唯一模式
    patterns <- src %>%
        select(SEX, AGE_GROUP, AREA, CAUSE) %>%
        distinct() %>%
        mutate(patterns = paste(SEX, AGE_GROUP, AREA, CAUSE, sep = ";")) %>%
        pull(patterns)
    n_pat <- length(patterns)

    # 初始化结果列表
    results <- list()

    # 处理每个模式
    for (j in 1:n_pat) {
        pattern <- patterns[j]
        message("处理模式 ", j, "/", n_pat, ": ", pattern)

        # 提取模式数据
        pattern_parts <- strsplit(pattern, ";")[[1]]
        patt_src <- src[
            src$SEX == pattern_parts[1] &
                src$AGE_GROUP == pattern_parts[2] &
                src$AREA == pattern_parts[3] &
                src$CAUSE == pattern_parts[4],
        ]

        # 如果数据不足，则跳过
        if (nrow(patt_src) < 10) {
            message("由于数据不足，跳过模式")
            next
        }

        # 准备历史数据
        hist_src <- patt_src[patt_src$event_index == "0", ]

        # 如果历史数据不足，则跳过
        if (nrow(hist_src) < 10) {
            message("由于历史数据不足，跳过模式")
            next
        }

        # 模型拟合和预测
        model_results <- fit_and_predict_bsts(patt_src, hist_src, l_period)

        # 如果模型失败，则跳过
        if (length(model_results) == 0 || is.data.frame(model_results)) {
            message("此模式的模型失败")
            next
        }

        # 更新输出（统一处理所有预测结果）
        year_predict <- sort(unique(out_data$YEAR))
        result <- update_output_bsts(out_data, model_results, pattern, year_predict)

        # 存储结果
        if (nrow(result) > 0) {
            results[[j]] <- result
            message("成功处理模式: ", pattern, " (", nrow(result), " 行)")
        }
    }

    # 合并结果
    out_data <- NULL
    if (length(results) > 0) {
        out_data <- do.call(rbind, results[!sapply(results, is.null)])
        message("成功合并 ", length(results[!sapply(results, is.null)]), " 个模式的结果")
    } else {
        warning("没有成功处理的模式!")
        return(NULL)
    }

    # 处理结果
    if (!is.null(out_data)) {
        out_data <- process_model_results(out_data, "BSTS model")
    }

    end_time <- Sys.time()
    total_time <- difftime(end_time, start_time, units = "mins")
    message("BSTS 模型计算完成，总时间: ", round(total_time, 2), " 分钟")
    flush.console()

    return(out_data) # 直接返回整合的预测结果
}

# # 计算超额死亡
# calculate_excess_deaths <- function(recorded_deaths, expected_deaths) {
#     excess_deaths <- ifelse(is.na(recorded_deaths), NA, recorded_deaths - expected_deaths$ESTIMATE)
#     excess_deaths_lower <- ifelse(is.na(recorded_deaths), NA, recorded_deaths - expected_deaths$UPPER_LIMIT)
#     excess_deaths_upper <- ifelse(is.na(recorded_deaths), NA, recorded_deaths - expected_deaths$LOWER_LIMIT)
#     return(data.frame(
#         Excess_Deaths = excess_deaths,
#         Excess_Deaths_Lower = excess_deaths_lower,
#         Excess_Deaths_Upper = excess_deaths_upper
#     ))
# }

# # 主函数：计算超额死亡
# calculate_acm_excess <- function(src) {
#     # 运行 BSTS 模型
#     bsts_results <- fcn_bsts(src)

#     # 提取实际死亡人数
#     recorded_deaths <- src %>%
#         arrange(SEX, AGE_GROUP, AREA, CAUSE, YEAR, PERIOD) %>%
#         pull(NO_DEATHS)

#     # 计算超额死亡
#     if (!is.null(bsts_results)) {
#         excess_deaths <- calculate_excess_deaths(recorded_deaths, bsts_results)
#         bsts_results <- cbind(bsts_results, excess_deaths)
#     }

#     return(bsts_results)
# }