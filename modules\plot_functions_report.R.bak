# Plot Functions
# Contains functions for generating plots for the report module

# Load necessary libraries
library(ggplot2)
library(dplyr)
library(scales)  # For formatting axis labels
library(RColorBrewer)  # For color palettes
library(base64enc)  # For base64 encoding of images

# Create plots HTML
create_plots_html <- function(total_predictions, selected_models) {
  plots_html <- ""

  if (nrow(total_predictions) > 0) {
    # Filter data for selected models
    plot_data <- total_predictions %>%
      filter(Model %in% selected_models) %>%
      mutate(
        TimePoint = as.Date(paste(YEAR, PERIOD, 15, sep = "-")),
        EXCESS_DEATHS = NO_DEATHS - EXP_DEATHS
      )

    # If no data after filtering, return a message
    if (nrow(plot_data) == 0) {
      return("<p>No data available for the selected models.</p>")
    }

    # Define model colors
    model_colors <- c(
      "Historical Average" = "#00CED1",
      "Negative Binomial Regression" = "#FF0000",
      "Quasi-Poisson Model" = "#32CD32",
      "Zero Inflated Poisson Model" = "#9932CC",
      "BSTS Model" = "#FFA500",
      "State Space Model" = "#1E90FF",
      "ARIMA/SARIMA Model" = "#8B4513",
      "Historical Average and Trend Model" = "#FFD700"  # Added new model with gold color
    )

    # Try to generate static plots
    tryCatch({
      # Create temporary file paths
      acm_plot_path <- tempfile(fileext = ".png")
      ed_plot_path <- tempfile(fileext = ".png")
      ps_plot_path <- tempfile(fileext = ".png")

      # Create All Cause Mortality plot
      acm_plot <- ggplot(plot_data, aes(x = TimePoint, y = NO_DEATHS)) +
        geom_line(color = "black", size = 1) +
        labs(
          title = "All Cause Mortality: Recorded vs. Expected",
          x = "Time",
          y = "Number of Deaths"
        ) +
        theme_minimal() +
        theme(
          plot.title = element_text(size = 14, face = "bold"),
          axis.text.x = element_text(angle = 45, hjust = 1)
        )

      # Add expected deaths lines for each model
      for (model_name in unique(plot_data$Model)) {
        model_data <- plot_data[plot_data$Model == model_name, ]
        line_color <- if (model_name %in% names(model_colors)) {
          model_colors[model_name]
        } else {
          "#999999"
        }

        acm_plot <- acm_plot +
          geom_line(
            data = model_data,
            aes(x = TimePoint, y = EXP_DEATHS),
            color = line_color,
            linetype = "dashed",
            size = 0.8
          )
      }

      # Add event markers
      if ("event_index" %in% names(plot_data) && "event_name" %in% names(plot_data)) {
        event_data <- plot_data %>%
          filter(!is.na(event_index), event_index != "0", !is.na(event_name)) %>%
          group_by(event_index, event_name) %>%
          summarise(
            start_time = min(TimePoint, na.rm = TRUE),
            end_time = max(TimePoint, na.rm = TRUE),
            .groups = 'drop'
          )

        if (nrow(event_data) > 0) {
          y_min <- min(plot_data$NO_DEATHS, plot_data$EXP_DEATHS, na.rm = TRUE)
          y_max <- max(plot_data$NO_DEATHS, plot_data$EXP_DEATHS, na.rm = TRUE)
          event_colors_bg <- RColorBrewer::brewer.pal(min(nrow(event_data), 9), "Set3")
          names(event_colors_bg) <- unique(event_data$event_name)

          for (i in 1:nrow(event_data)) {
            event <- event_data[i, ]
            color <- event_colors_bg[event$event_name]

            acm_plot <- acm_plot +
              annotate(
                "rect",
                xmin = event$start_time,
                xmax = event$end_time,
                ymin = y_min,
                ymax = y_max,
                fill = color,
                alpha = 0.08
              ) +
              annotate(
                "text",
                x = event$start_time + (event$end_time - event$start_time) / 2,
                y = y_max * 0.95,
                label = event$event_name,
                color = "darkred",
                size = 3
              )
          }
        }
      }

      # Save All Cause Mortality plot
      ggsave(acm_plot_path, acm_plot, width = 10, height = 6, dpi = 100)

      # Create Excess Deaths plot
      ed_plot <- ggplot(plot_data, aes(x = TimePoint, y = EXCESS_DEATHS, fill = Model)) +
        geom_bar(stat = "identity", position = "dodge") +
        scale_fill_manual(values = model_colors) +
        labs(
          title = "Excess Deaths",
          x = "Time",
          y = "Excess Deaths"
        ) +
        theme_minimal() +
        theme(
          plot.title = element_text(size = 14, face = "bold"),
          axis.text.x = element_text(angle = 45, hjust = 1),
          legend.position = "bottom"
        )

      # Add event markers to Excess Deaths plot
      if ("event_index" %in% names(plot_data) && "event_name" %in% names(plot_data)) {
        event_data <- plot_data %>%
          filter(!is.na(event_index), event_index != "0", !is.na(event_name)) %>%
          group_by(event_index, event_name) %>%
          summarise(
            start_time = min(TimePoint, na.rm = TRUE),
            end_time = max(TimePoint, na.rm = TRUE),
            .groups = 'drop'
          )

        if (nrow(event_data) > 0) {
          y_min <- min(plot_data$EXCESS_DEATHS, na.rm = TRUE)
          y_max <- max(plot_data$EXCESS_DEATHS, na.rm = TRUE)
          event_colors_bg <- RColorBrewer::brewer.pal(min(nrow(event_data), 9), "Set3")
          names(event_colors_bg) <- unique(event_data$event_name)

          for (i in 1:nrow(event_data)) {
            event <- event_data[i, ]
            color <- event_colors_bg[event$event_name]

            ed_plot <- ed_plot +
              annotate(
                "rect",
                xmin = event$start_time,
                xmax = event$end_time,
                ymin = y_min,
                ymax = y_max,
                fill = color,
                alpha = 0.08
              ) +
              annotate(
                "text",
                x = event$start_time + (event$end_time - event$start_time) / 2,
                y = y_max * 0.95,
                label = event$event_name,
                color = "darkred",
                size = 3
              )
          }
        }
      }

      # Save Excess Deaths plot
      ggsave(ed_plot_path, ed_plot, width = 10, height = 6, dpi = 100)

      # Create P-Score plot
      ps_plot <- ggplot(plot_data, aes(x = TimePoint, y = P_SCORE, fill = Model)) +
        geom_bar(stat = "identity", position = "dodge") +
        scale_fill_manual(values = model_colors) +
        labs(
          title = "P-Score (%)",
          x = "Time",
          y = "P-Score (%)"
        ) +
        theme_minimal() +
        theme(
          plot.title = element_text(size = 14, face = "bold"),
          axis.text.x = element_text(angle = 45, hjust = 1),
          legend.position = "bottom"
        )

      # Add event markers to P-Score plot
      if ("event_index" %in% names(plot_data) && "event_name" %in% names(plot_data)) {
        event_data <- plot_data %>%
          filter(!is.na(event_index), event_index != "0", !is.na(event_name)) %>%
          group_by(event_index, event_name) %>%
          summarise(
            start_time = min(TimePoint, na.rm = TRUE),
            end_time = max(TimePoint, na.rm = TRUE),
            .groups = 'drop'
          )

        if (nrow(event_data) > 0) {
          y_min <- min(plot_data$P_SCORE, na.rm = TRUE)
          y_max <- max(plot_data$P_SCORE, na.rm = TRUE)
          event_colors_bg <- RColorBrewer::brewer.pal(min(nrow(event_data), 9), "Set3")
          names(event_colors_bg) <- unique(event_data$event_name)

          for (i in 1:nrow(event_data)) {
            event <- event_data[i, ]
            color <- event_colors_bg[event$event_name]

            ps_plot <- ps_plot +
              annotate(
                "rect",
                xmin = event$start_time,
                xmax = event$end_time,
                ymin = y_min,
                ymax = y_max,
                fill = color,
                alpha = 0.08
              ) +
              annotate(
                "text",
                x = event$start_time + (event$end_time - event$start_time) / 2,
                y = y_max * 0.95,
                label = event$event_name,
                color = "darkred",
                size = 3
              )
          }
        }
      }

      # Save P-Score plot
      ggsave(ps_plot_path, ps_plot, width = 10, height = 6, dpi = 100)

      # Convert plots to base64 and embed in HTML
      acm_plot_base64 <- base64enc::base64encode(acm_plot_path)
      ed_plot_base64 <- base64enc::base64encode(ed_plot_path)
      ps_plot_base64 <- base64enc::base64encode(ps_plot_path)

      plots_html <- paste0(
        "<h3>All Cause Mortality</h3>",
        "<div style='text-align: center;'>",
        "<img src='data:image/png;base64,", acm_plot_base64, "' style='max-width: 100%; height: auto;' />",
        "</div>",
        "<h3>Excess Deaths</h3>",
        "<div style='text-align: center;'>",
        "<img src='data:image/png;base64,", ed_plot_base64, "' style='max-width: 100%; height: auto;' />",
        "</div>",
        "<h3>P-Score</h3>",
        "<div style='text-align: center;'>",
        "<img src='data:image/png;base64,", ps_plot_base64, "' style='max-width: 100%; height: auto;' />",
        "</div>"
      )
    }, error = function(e) {
      message("Error generating plots: ", e$message)
      plots_html <- paste0(
        "<h3>All Cause Mortality</h3>",
        "<p>Unable to generate plots. Error: ", e$message, "</p>",
        "<div style='text-align: center; padding: 20px; background-color: #f8f9fa; border-radius: 5px;'>",
        "<p><i class='fa fa-chart-line' style='font-size: 48px; color: #3498db;'></i></p>",
        "<p>All Cause Mortality Plot</p>",
        "</div>",
        "<h3>Excess Deaths</h3>",
        "<div style='text-align: center; padding: 20px; background-color: #f8f9fa; border-radius: 5px;'>",
        "<p><i class='fa fa-chart-bar' style='font-size: 48px; color: #e74c3c;'></i></p>",
        "<p>Excess Deaths Plot</p>",
        "</div>",
        "<h3>P-Score</h3>",
        "<div style='text-align: center; padding: 20px; background-color: #f8f9fa; border-radius: 5px;'>",
        "<p><i class='fa fa-chart-pie' style='font-size: 48px; color: #2ecc71;'></i></p>",
        "<p>P-Score Plot</p>",
        "</div>"
      )
    })
  } else {
    plots_html <- "<p>No data available for plotting.</p>"
  }

  return(plots_html)
}