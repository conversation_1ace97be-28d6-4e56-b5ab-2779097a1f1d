library(shiny)
library(DT)
library(openxlsx) # For reading Excel files
library(lubridate) # For date processing
library(dplyr) # For data operations
library(shinyjs) # For JavaScript-based UI enhancements
library(zoo) # 添加zoo包，用于na.locf函数

# 加载DT扩展
# 确保在全局.R或app.R中添加了以下代码
# options(DT.options = list(
#   language = list(url = '//cdn.datatables.net/plug-ins/1.10.11/i18n/Chinese.json')
# ))

source("modules/utils.R")

# 数据处理模块的UI部分
data_process_module_ui <- function(id) {
  ns <- NS(id)
  fluidPage(
    useShinyjs(), # 初始化shinyjs
    tags$head(
      tags$link(rel = "stylesheet", type = "text/css", href = "style.css"), # 可选
      tags$style(HTML("
        /* 表格下载按钮样式 */
        .dt-buttons {
          margin-bottom: 10px;
        }
        .download-btn {
          margin-bottom: 10px;
        }
      "))
    ),
    fluidRow(
      column(
        7,
        tabsetPanel(
          id = ns("datatabs"),
          tabPanel(
            "Upload Data",
            br(),
            wellPanel(
              p("This tool estimates weekly or monthly excess deaths in the Western Pacific Region during the COVID-19 pandemic."),
              p(
                "The", strong("5-years historical average"), "and", strong("expected deaths forecasted by negative-binomial regression"),
                "are calculated from 2015-2019 data."
              ),
              p(
                class = "helper", icon("question-circle"),
                span("What format does the Excel file need?", style = "font-size:0.85em;"),
                br(), "Use a *.xls or *.xlsx file in the WHO standardized template."
              ),
              selectizeInput(ns("template_country"),
                label = "Download a template:",
                choices = c(
                  "Choose a template" = "",
                  "Australia (empty template)", "Philippines (empty template)", "French Polynesia (empty template)",
                  "Generic Monthly template", "Generic Weekly template",
                  "Australia (filled up to August 2020)", "Japan (filled up to August 2020)",
                  "Republic of Korea (filled up to August 2020)", "New Zealand (filled up to August 2020)",
                  "Philippines (filled up to August 2020)"
                )
              ),
              downloadButton(ns("download_templates"), "Download template"),
              br(), br(),
              selectInput(ns("filetype"),
                label = "Open a data set",
                choices = c(
                  "Excel spreadsheet (*.xls or *.xlsx)" = 1,
                  "Built-in example data" = 2
                )
              ),
              conditionalPanel(
                condition = paste0("input['", ns("filetype"), "'] == 1"),
                fileInput(ns("rawdatafile"), label = NULL, accept = c(".xls", ".xlsx")),
                uiOutput(ns("selectsheet")),
                uiOutput(ns("selecteventsheet")),
                actionButton(ns("loadEvents"), "Merge Mortality/Events Data"),
                # 上传数据的进度消息区域
                div(id = ns("progress_upload"), style = "color: blue; margin-top: 10px;")
              ),
              conditionalPanel(
                condition = paste0("input['", ns("filetype"), "'] == 2"),
                selectizeInput(ns("samplecountry"),
                  label = "Choose a country",
                  choices = c("Choose a country" = "",
                              "Australia", "Japan", "Republic of Korea",
                              "New Zealand", "Philippines")
                ),
                uiOutput(ns("selectbuiltinsheet")),
                uiOutput(ns("selectbuiltineventsheet")),
                actionButton(ns("loadBuiltinEvents"), "Merge Built-in Data"),
                # 内置数据的进度消息区域
                div(id = ns("progress_builtin"), style = "color: blue; margin-top: 10px;")
              )
            )
          ),
          tabPanel(
            "View Data",
            br(),
            p("Below is the all-cause mortality data. Expected columns: 'AREA', 'AGE_GROUP', 'SEX', 'YEAR', 'PERIOD', 'NO_DEATHS'."),
            wellPanel(
              h4("Merged Data Table"),
              DTOutput(ns("ACM_table"), width = "100%"),
              # 添加下载按钮
              div(class = "download-btn",
                downloadButton(ns("downloadMergedData"), "Download Full Data")
              ),
            ),
            wellPanel(
              h4("Raw Mortality Data"),
              DTOutput(ns("raw_mortality_table"), width = "100%"),
              # 添加下载按钮
              div(class = "download-btn",
                downloadButton(ns("downloadRawData"), "Download Full Data")
              )
            ),
            wellPanel(
              h4("Events Data"),
              DTOutput(ns("events_table"), width = "100%"),
              # 添加下载按钮
              div(class = "download-btn",
                downloadButton(ns("downloadEventsData"), "Download Full Data")
              )
            )
          )
        )
      ),
      column(
        4,
        tabsetPanel(
          tabPanel(
            "Data Summary", br(),
            verbatimTextOutput(ns("ACMsum"))
          )
        )
      )
    )
  )
}

# 数据处理模块的服务器部分
data_process_module_server <- function(id, rv) {
  moduleServer(id, function(input, output, session) {
    # 使用命名空间
    ns <- session$ns

    # 初始化本地响应式值
    data <- reactiveValues(
      acm_rawdata = NULL,
      acm_data = NULL,
      event_data = NULL,
      merged_data = NULL,
      data_description = NULL
    )

    # 将数据暴露给全局响应式值
    observe({
      rv$acm_rawdata <- data$acm_rawdata
      rv$acm_data <- data$acm_data
      rv$event_data <- data$event_data
      rv$merged_data <- data$merged_data
      rv$data_description <- data$data_description
    })

    # UI渲染
    output$selectsheet <- renderUI({
      req(input$rawdatafile)
      sheets <- readxl::excel_sheets(input$rawdatafile$datapath)
      selected_sheet <- ifelse("National level" %in% sheets, "National level", sheets[1])
      selectInput(ns("sheetname"), "Select mortality data sheet", choices = sheets, selected = selected_sheet)
    })

    output$selecteventsheet <- renderUI({
      req(input$rawdatafile, input$sheetname)
      sheets <- readxl::excel_sheets(input$rawdatafile$datapath)
      event_sheets <- setdiff(sheets, input$sheetname)
      selected_event_sheets <- ifelse("events" %in% sheets, "events", event_sheets)
      selectInput(ns("eventsheetname"), "Select events data sheet", choices = selected_event_sheets)
    })

    output$selectbuiltinsheet <- renderUI({
      req(input$samplecountry)
      file_path <- file.path("XLSX", paste0(gsub(" ", "_", input$samplecountry), "_built_in_data.xlsx"))
      if (file.exists(file_path)) {
        sheets <- readxl::excel_sheets(file_path)
        selected_sheet <- ifelse("National level" %in% sheets, "National level", sheets[1])
        selectInput(ns("builtinsheetname"), "Select mortality data sheet", choices = sheets, selected = selected_sheet)
      }
    })

    output$selectbuiltineventsheet <- renderUI({
      req(input$samplecountry, input$builtinsheetname)
      file_path <- file.path("XLSX", paste0(gsub(" ", "_", input$samplecountry), "_built_in_data.xlsx"))
      if (file.exists(file_path)) {
        sheets <- readxl::excel_sheets(file_path)
        event_sheets <- setdiff(sheets, input$builtinsheetname)
        selected_event_sheet <- ifelse("events" %in% event_sheets, "events", event_sheets)
        selectInput(ns("builtineventsheetname"), "Select events data sheet", choices = event_sheets, selected = selected_event_sheet)
      }
    })

    # 数据加载和合并（带进度显示）
    observeEvent(input$loadEvents, {
      req(input$rawdatafile, input$sheetname, input$eventsheetname)

      # 显示进度消息
      shinyjs::html(ns("progress_upload"), "Processing data, please wait...")
      shinyjs::show(ns("progress_upload"))

      # 创建临时文件
      temp_file <- tempfile(pattern = "upload_", fileext = ".xlsx")
      file.copy(input$rawdatafile$datapath, temp_file, overwrite = TRUE)

      # 使用withProgress显示进度条
      withProgress(message = "Merging uploaded data...", value = 0, {
        tryCatch({
          # 读取死亡率数据
          incProgress(0.2, detail = "Reading mortality data...")
          raw_data <- openxlsx::read.xlsx(temp_file, sheet = input$sheetname, colNames = FALSE)
          data$acm_rawdata <- raw_data

          # 处理死亡率数据
          incProgress(0.4, detail = "Processing mortality data...")
          data$acm_data <- process_mortality_data2(temp_file, input$sheetname)

          # 加载事件数据
          incProgress(0.6, detail = "Loading events data...")
          data$event_data <- loadEventsData(temp_file, input$eventsheetname)

          # 合并数据
          incProgress(0.8, detail = "Merging data...")
          if (!is.null(data$acm_data) && !is.null(data$event_data)) {
            data$merged_data <- mergeEventsData(data$acm_data, data$event_data)
            data$data_description <- paste("Uploaded data from", input$rawdatafile$name)

            # 重新加载数据并生成新的rv$merged_data后，将指定的rv元素设置为NULL
            rv$total_prediction <- NULL
            rv$plot_time_data <- NULL
            rv$summary_by_event <- NULL
            rv$overall_summary <- NULL

            # 更新进度消息（成功）
            shinyjs::html(ns("progress_upload"), "Data merged successfully!")
          } else {
            shinyjs::html(ns("progress_upload"), "Error: Data could not be merged.")
          }
        },
        finally = {
          # 清理临时文件
          if (file.exists(temp_file)) {
            unlink(temp_file)
          }
        })

        # 完成进度
        incProgress(0.2, detail = "Complete!")
      })

      # 短暂延迟后隐藏进度消息
      shinyjs::delay(2000, shinyjs::hide(ns("progress_upload")))
    })

    # 修改内置数据处理函数，添加进度条
    observeEvent(input$loadBuiltinEvents, {
      req(input$samplecountry, input$builtinsheetname, input$builtineventsheetname)

      # 显示进度消息
      shinyjs::html(ns("progress_builtin"), "Loading built-in data, please wait...")
      shinyjs::show(ns("progress_builtin"))

      # 修改国家名称处理方式，参考data_tab.R的处理方法
      country_name <- c("Australia", "Japan", "Republic_of_Korea", "New_Zealand", "Philippines")[
        match(input$samplecountry, c(
          "Australia", "Japan", "Republic of Korea", "New Zealand", "Philippines"
        ))
      ]

      file_path <- file.path("XLSX", paste0(country_name, "_built_in_data.xlsx"))

      # 使用withProgress显示进度条
      withProgress(message = "Loading built-in data...", value = 0, {
        # 检查文件是否存在
        if (file.exists(file_path)) {
          # 读取原始数据
          incProgress(0.2, detail = "Reading mortality data...")
          raw_data <- openxlsx::read.xlsx(file_path, sheet = input$builtinsheetname, colNames = FALSE)
          data$acm_rawdata <- raw_data

          # 处理死亡率数据
          incProgress(0.4, detail = "Processing mortality data...")
          data$acm_data <- process_mortality_data2(file_path, input$builtinsheetname)

          # 加载事件数据
          incProgress(0.6, detail = "Loading events data...")
          data$event_data <- loadEventsData(file_path, input$builtineventsheetname)

          # 合并数据
          incProgress(0.8, detail = "Merging data...")
          if (!is.null(data$acm_data) && !is.null(data$event_data)) {
            data$merged_data <- mergeEventsData(data$acm_data, data$event_data)
            data$data_description <- paste("Built-in", input$samplecountry, "data")

            # 重新加载数据并生成新的rv$merged_data后，将指定的rv元素设置为NULL
            rv$total_prediction <- NULL
            rv$plot_time_data <- NULL
            rv$summary_by_event <- NULL
            rv$overall_summary <- NULL

            # 更新进度消息（成功）
            shinyjs::html(ns("progress_builtin"), "Built-in data loaded successfully!")
          } else {
            shinyjs::html(ns("progress_builtin"), "Error: Data could not be loaded.")
          }
        } else {
          shinyjs::html(ns("progress_builtin"), "Error: File not found.")
        }

        # 完成进度
        incProgress(0.2, detail = "Complete!")
      })

      # 短暂延迟后隐藏进度消息
      shinyjs::delay(2000, shinyjs::hide(ns("progress_builtin")))
    })

    # 表格输出
    output$ACM_table <- renderDT({
      req(data$merged_data)
      datatable(data$merged_data,
                options = list(
                  pageLength = 10,
                  scrollX = TRUE,
                  autoWidth = FALSE,  # 修改为FALSE以防止自动调整宽度
                  scrollCollapse = TRUE,
                  dom = 'Blfrtip',
                  buttons = list(
                    list(extend = 'copy', text = 'Copy')
                  ),
                  columnDefs = list(  # 添加列定义以确保对齐
                    list(className = 'dt-center', targets = '_all')
                  )
                ),
                rownames = FALSE,
                extensions = 'Buttons')
    })

    output$raw_mortality_table <- renderDT({
      if (is.null(data$acm_rawdata)) {
        datatable(data.frame(Message = "No mortality data loaded yet"), options = list(dom = "t"), rownames = FALSE)
      } else {
        datatable(data$acm_rawdata,
                  options = list(
                    pageLength = 10,
                    scrollX = TRUE,
                    autoWidth = FALSE,  # 修改为FALSE
                    scrollCollapse = TRUE,
                    dom = 'Blfrtip',
                    buttons = list(
                      list(extend = 'copy', text = 'Copy')
                    ),
                    columnDefs = list(  # 添加列定义
                      list(className = 'dt-center', targets = '_all')
                    )
                  ),
                  rownames = FALSE,
                  extensions = 'Buttons')
      }
    })

    output$events_table <- renderDT({
      if (is.null(data$event_data)) {
        datatable(data.frame(Message = "No events data loaded yet"), options = list(dom = "t"), rownames = FALSE)
      } else {
        datatable(data$event_data,
                  options = list(
                    pageLength = 10,
                    scrollX = TRUE,
                    autoWidth = FALSE,  # 修改为FALSE
                    scrollCollapse = TRUE,
                    dom = 'Blfrtip',
                    buttons = list(
                      list(extend = 'copy', text = 'Copy')
                    ),
                    columnDefs = list(  # 添加列定义
                      list(className = 'dt-center', targets = '_all')
                    )
                  ),
                  rownames = FALSE,
                  extensions = 'Buttons')
      }
    })

    # 添加下载处理器 - 修改为使用Excel格式
    output$downloadMergedData <- downloadHandler(
      filename = function() {
        paste("Merged_Data_", format(Sys.Date(), "%Y-%m-%d"), ".xlsx", sep = "")
      },
      content = function(file) {
        req(data$merged_data)
        openxlsx::write.xlsx(data$merged_data, file, rowNames = FALSE)
      }
    )

    output$downloadRawData <- downloadHandler(
      filename = function() {
        paste("Raw_Mortality_Data_", format(Sys.Date(), "%Y-%m-%d"), ".xlsx", sep = "")
      },
      content = function(file) {
        req(data$acm_rawdata)
        openxlsx::write.xlsx(data$acm_rawdata, file, rowNames = FALSE)
      }
    )

    output$downloadEventsData <- downloadHandler(
      filename = function() {
        paste("Events_Data_", format(Sys.Date(), "%Y-%m-%d"), ".xlsx", sep = "")
      },
      content = function(file) {
        req(data$event_data)
        openxlsx::write.xlsx(data$event_data, file, rowNames = FALSE)
      }
    )

    # 数据摘要输出
    output$ACMsum <- renderPrint({
      if (is.null(data$merged_data)) {
        cat("No data loaded yet.\n")
      } else {
        cat("Data Summary:\n")
        cat("-------------\n")
        cat("Data source:", data$data_description, "\n\n")

        # 计算并显示基本统计信息
        cat("Number of records:", nrow(data$merged_data), "\n")
        cat("Years covered:", paste(sort(unique(data$merged_data$YEAR)), collapse = ", "), "\n")

        # 显示死亡人数统计
        deaths_summary <- summary(data$merged_data$NO_DEATHS)
        cat("\nDeaths statistics:\n")
        print(deaths_summary)

        # 显示按性别分组的死亡人数
        if ("SEX" %in% names(data$merged_data)) {
          cat("\nDeaths by sex:\n")
          sex_summary <- data$merged_data %>%
            group_by(SEX) %>%
            summarise(
              Count = n(),
              `Total Deaths` = sum(NO_DEATHS, na.rm = TRUE),
              `Mean Deaths` = mean(NO_DEATHS, na.rm = TRUE),
              `Min Deaths` = min(NO_DEATHS, na.rm = TRUE),
              `Max Deaths` = max(NO_DEATHS, na.rm = TRUE)
            )
          print(sex_summary)
        }

        # 显示按年龄组分组的死亡人数
        if ("AGE_GROUP" %in% names(data$merged_data)) {
          cat("\nDeaths by age group:\n")
          age_summary <- data$merged_data %>%
            group_by(AGE_GROUP) %>%
            summarise(
              Count = n(),
              `Total Deaths` = sum(NO_DEATHS, na.rm = TRUE),
              `Mean Deaths` = mean(NO_DEATHS, na.rm = TRUE)
            )
          print(age_summary)
        }
      }
    })

    # 下载处理程序
    output$download_templates <- downloadHandler(
      filename = function() {
        paste0(c(
          "Australia (empty template).xlsx", "Philippines (empty template).xlsx", "French Polynesia (empty template).xlsx",
          "Data Entry Template - monthly.xlsx", "Data Entry Template - weekly.xlsx",
          "Australia_built_in_data.xlsx", "Japan_built_in_data.xlsx",
          "Republic_of_Korea_built_in_data.xlsx", "New_Zealand_built_in_data.xlsx",
          "Philippines_built_in_data.xlsx"
        )[
          match(input$template_country, c(
            "Australia (empty template)", "Philippines (empty template)", "French Polynesia (empty template)",
            "Generic Monthly template", "Generic Weekly template",
            "Australia (filled up to August 2020)", "Japan (filled up to August 2020)",
            "Republic of Korea (filled up to August 2020)", "New Zealand (filled up to August 2020)",
            "Philippines (filled up to August 2020)"
          ))
        ])
      },
      content = function(file) {
        file.copy(
          from = paste0(
            "./XLSX/",
            c(
              "Australia (empty template).xlsx", "Philippines (empty template).xlsx", "French Polynesia (empty template).xlsx",
              "Data Entry Template - monthly.xlsx", "Data Entry Template - weekly.xlsx",
              "Australia_built_in_data.xlsx", "Japan_built_in_data.xlsx",
              "Republic_of_Korea_built_in_data.xlsx", "New_Zealand_built_in_data.xlsx",
              "Philippines_built_in_data.xlsx"
            )[
              match(input$template_country, c(
                "Australia (empty template)", "Philippines (empty template)", "French Polynesia (empty template)",
                "Generic Monthly template", "Generic Weekly template",
                "Australia (filled up to August 2020)", "Japan (filled up to August 2020)",
                "Republic of Korea (filled up to August 2020)", "New Zealand (filled up to August 2020)",
                "Philippines (filled up to August 2020)"
              ))
            ]
          ),
          to = file
        )
      }
    )

  }) # End moduleServer
} # Add this closing bracket for the data_process_module_server function