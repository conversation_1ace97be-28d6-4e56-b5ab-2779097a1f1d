# 加载公共函数
# Ensure this path is correct relative to your working directory when sourcing model_run_module.R
# If model_run_module.R is in the main project directory, and common_functions.R is in modules:
# source("modules/common_functions.R") # This should be sourced by the main app or model_run_module.R

# 历史平均及趋势模型拟合和预测函数
fit_and_predict_his_tr <- function(patt_src, hist_src, l_period) {
    t.start <- Sys.time()
    DOM <- c(31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31) # Days Of Month

    is_leap <- function(year) {
        return((year %% 4 == 0 & year %% 100 != 0) | (year %% 400 == 0))
    }

    # PERIOD is kept as its original type (e.g., numeric) in hist_src and patt_src initially

    # --- Robust aDATE calculation component based on year ---
    calculate_year_cumulative_days <- function(years_vector) {
        if (length(years_vector) == 0 || all(is.na(years_vector))) {
            return(rep(NA_real_, length(years_vector)))
        }
        min_data_year <- min(years_vector, na.rm = TRUE)
        max_data_year <- max(years_vector, na.rm = TRUE)

        if (is.infinite(min_data_year) || is.infinite(max_data_year)) {
            return(rep(NA_real_, length(years_vector)))
        }

        full_year_range <- min_data_year:max_data_year
        days_in_year_values <- ifelse(is_leap(full_year_range), 366, 365)

        cum_days_from_min_year_start <- c(0, cumsum(days_in_year_values[-length(days_in_year_values)]))
        if (length(full_year_range) == 1) {
            names(cum_days_from_min_year_start) <- as.character(full_year_range)
        } else {
            names(cum_days_from_min_year_start) <- as.character(full_year_range)
        }

        year_char_vector <- as.character(years_vector)
        return_values <- cum_days_from_min_year_start[year_char_vector]
        return(return_values)
    }

    year_cumulative_days_patt <- calculate_year_cumulative_days(patt_src$YEAR)

    # Ensure PERIOD is numeric for these calculations if it's not already
    # These operations expect PERIOD to be indexable or numerically interpretable
    patt_src_period_numeric_for_calc <- as.numeric(as.character(patt_src$PERIOD))
    hist_src_period_numeric_for_calc <- as.numeric(as.character(hist_src$PERIOD))


    if (l_period > 51) { # Weekly data
        day_component_patt <- cumsum(c(0, rep(7, 52)))[patt_src_period_numeric_for_calc] + 3.5
        aDATE_patt <- year_cumulative_days_patt + day_component_patt

        hist_src$logdays <- log(7) # logdays for training data
        src_pandemic <- patt_src %>%
            dplyr::mutate(loc_DATE = aDATE_patt, logdays = log(7)) # logdays for prediction data
    } else { # Monthly data
        # logdays for hist_src
        logdays_hist <- sapply(seq_along(hist_src_period_numeric_for_calc), function(i) {
            log(ifelse(hist_src_period_numeric_for_calc[i] == 2 && is_leap(hist_src$YEAR[i]), 29, DOM[hist_src_period_numeric_for_calc[i]]))
        })
        hist_src$logdays <- logdays_hist

        # Day component for monthly data for patt_src
        day_component_patt <- cumsum(c(0, DOM))[patt_src_period_numeric_for_calc] + 15
        aDATE_patt <- year_cumulative_days_patt + day_component_patt

        # logdays for patt_src (to be used in src_pandemic)
        logdays_patt_values <- sapply(seq_along(patt_src_period_numeric_for_calc), function(i) {
            log(ifelse(patt_src_period_numeric_for_calc[i] == 2 && is_leap(patt_src$YEAR[i]), 29, DOM[patt_src_period_numeric_for_calc[i]]))
        })

        src_pandemic <- patt_src %>%
            dplyr::mutate(loc_DATE = aDATE_patt, logdays = logdays_patt_values)
    }

    # Model formula uses factor(PERIOD)
    model_formula_str <- "NO_DEATHS ~ factor(PERIOD) + YEAR + offset(logdays)"
    model_description <- "GLM Quasipoisson"
    message(paste("Fitting model:", model_description))

    fit <- tryCatch(
        {
            stats::glm(stats::as.formula(model_formula_str),
                family = stats::quasipoisson(link = "log"),
                data = hist_src, # hist_src$PERIOD is original type here
                control = stats::glm.control(maxit = 100)
            )
        },
        error = function(e) {
            warning(paste("GLM fitting failed for pattern:", e$message), call. = FALSE)
            return(NULL)
        }
    )

    if (is.null(fit)) {
        return(data.frame())
    }

    # Prepare src_pandemic for prediction: ensure PERIOD is a factor with levels from the model fit
    if (!is.null(fit$xlevels[["factor(PERIOD)"]])) {
        model_period_levels <- fit$xlevels[["factor(PERIOD)"]]
        # Ensure all PERIOD values in src_pandemic are present in model_period_levels
        # If not, prediction might fail for those levels.
        # For safety, filter src_pandemic to include only levels present in the model,
        # or ensure patt_src originally only contains these.
        # Here, we convert to factor, predict will handle missing levels if any (though ideally they match).
        src_pandemic$PERIOD <- factor(src_pandemic$PERIOD, levels = model_period_levels)
    } else {
        # Fallback if xlevels are not found as expected, though unlikely for factor() in formula
        warning("Could not find factor levels for PERIOD in the fitted model. Prediction might be unreliable.")
        src_pandemic$PERIOD <- as.factor(src_pandemic$PERIOD) # Basic factor conversion
    }


    pred_link_scale <- tryCatch(
        {
            stats::predict(fit, newdata = src_pandemic, type = "link", se.fit = TRUE)
        },
        error = function(e) {
            warning(paste("Prediction failed for pattern:", e$message), call. = FALSE)
            return(NULL)
        }
    )

    if (is.null(pred_link_scale)) {
        return(data.frame())
    }

    patt_src_results <- src_pandemic # Start with src_pandemic (PERIOD is currently a factor)
    crit_val <- stats::qnorm(0.975)
    patt_src_results$ESTIMATE_link <- pred_link_scale$fit
    patt_src_results$SE_link <- pred_link_scale$se.fit
    patt_src_results$ESTIMATE <- exp(patt_src_results$ESTIMATE_link)
    patt_src_results$LOWER_LIMIT <- exp(patt_src_results$ESTIMATE_link - crit_val * patt_src_results$SE_link)
    patt_src_results$UPPER_LIMIT <- exp(patt_src_results$ESTIMATE_link + crit_val * patt_src_results$SE_link)

    patt_src_results$ESTIMATE[patt_src_results$ESTIMATE < 0] <- 0
    patt_src_results$LOWER_LIMIT[patt_src_results$LOWER_LIMIT < 0] <- 0
    patt_src_results$UPPER_LIMIT[patt_src_results$UPPER_LIMIT < 0] <- 0

    # Convert PERIOD back to numeric (or original type if it was character representing numbers)
    # This ensures the output PERIOD is consistent with other models.
    patt_src_results$PERIOD <- as.numeric(as.character(patt_src_results$PERIOD))


    message("模式处理时间: ", round(difftime(Sys.time(), t.start, units = "secs"), 1), " 秒")

    return(list(
        src_pandemic = patt_src_results, # patt_src_results$PERIOD is now numeric
        estim.median = patt_src_results$ESTIMATE,
        estim.lower = patt_src_results$LOWER_LIMIT,
        estim.upper = patt_src_results$UPPER_LIMIT
    ))
}


# 更新输出函数
update_output_his_tr <- function(out_data, model_results, pattern, year_predict) {
    src_pandemic_from_model <- model_results$src_pandemic
    # src_pandemic_from_model$PERIOD is now numeric

    if (is.null(src_pandemic_from_model) || nrow(src_pandemic_from_model) == 0) {
        message("警告: 模式 ", pattern, " 的模型结果无效或为空")
        return(out_data[0, ])
    }

    pattern_parts <- strsplit(pattern, ";")[[1]]
    result_df <- out_data[0, ]

    # l_period from the model output (which should be numeric PERIOD)
    l_period <- max(src_pandemic_from_model$PERIOD, na.rm = TRUE)
    if (is.infinite(l_period) || is.na(l_period)) {
        l_period <- max(out_data$PERIOD, na.rm = TRUE) # Fallback
    }


    for (iyear_predict in 1:length(year_predict)) {
        y <- year_predict[iyear_predict]
        for (k_loop_idx in 1:l_period) {
            # Both PERIOD columns are expected to be numeric or character-numeric here
            matched_prediction_rows <- src_pandemic_from_model$YEAR == y &
                src_pandemic_from_model$PERIOD == k_loop_idx # Direct comparison

            current_records_template <- out_data[
                out_data$SEX == pattern_parts[1] &
                    out_data$AGE_GROUP == pattern_parts[2] &
                    out_data$AREA == pattern_parts[3] &
                    out_data$CAUSE == pattern_parts[4] &
                    out_data$YEAR == y &
                    out_data$PERIOD == k_loop_idx, # Direct comparison
            ]

            if (nrow(current_records_template) > 0 && sum(matched_prediction_rows, na.rm = TRUE) > 0) {
                idx_to_use <- which(matched_prediction_rows)[1]

                current_records_template$ESTIMATE <- src_pandemic_from_model$ESTIMATE[idx_to_use]
                current_records_template$LOWER_LIMIT <- src_pandemic_from_model$LOWER_LIMIT[idx_to_use]
                current_records_template$UPPER_LIMIT <- src_pandemic_from_model$UPPER_LIMIT[idx_to_use]

                if ("SERIES" %in% names(src_pandemic_from_model) && "SERIES" %in% names(current_records_template)) {
                    current_records_template$SERIES <- src_pandemic_from_model$SERIES[idx_to_use]
                }
                if ("event_index" %in% names(src_pandemic_from_model) && "event_index" %in% names(current_records_template)) {
                    current_records_template$event_index <- src_pandemic_from_model$event_index[idx_to_use]
                }
                if ("event_name" %in% names(src_pandemic_from_model) && "event_name" %in% names(current_records_template)) {
                    current_records_template$event_name <- src_pandemic_from_model$event_name[idx_to_use]
                }
                result_df <- rbind(result_df, current_records_template)
            }
        }
    }
    return(result_df)
}

# 历史平均+趋势回归模型主函数
fcn_his_tr <- function(src) {
    message("\n[fcn_his_tr] 开始历史平均及趋势模型计算...")
    flush.console()
    start_time <- Sys.time()

    src <- src %>%
        dplyr::filter(PERIOD <= 52) %>%
        dplyr::arrange(SEX, AGE_GROUP, AREA, CAUSE, YEAR, PERIOD) %>%
        dplyr::mutate(NO_DEATHS = as.numeric(NO_DEATHS))

    if (!exists("DOM")) DOM <- c(31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31)
    if (!exists("MOY")) MOY <- month.abb

    if (!exists("calculate_dates") || !exists("initialize_output") || !exists("process_model_results")) {
        stop("One or more helper functions (calculate_dates, initialize_output, process_model_results) not found. Ensure common_functions.R is sourced.")
    }

    nys_data <- length(unique(src$YEAR))
    max_period_data <- max(src$PERIOD, na.rm = TRUE) # PERIOD is original type
    wm_ident <- ifelse(max_period_data == 12, "Month", "Week")
    l_period_data <- ifelse(max_period_data == 12, 12, 52)

    src <- calculate_dates(src, max_period_data, nys_data, DOM, MOY)
    out_data_template <- initialize_output(src, wm_ident, l_period_data) # PERIOD in template is original type

    patterns <- src %>%
        dplyr::select(SEX, AGE_GROUP, AREA, CAUSE) %>%
        dplyr::distinct() %>%
        dplyr::mutate(patterns = paste(SEX, AGE_GROUP, AREA, CAUSE, sep = ";")) %>%
        dplyr::pull(patterns)

    n_pat <- length(patterns)
    results_list <- list()

    for (j in 1:n_pat) {
        pattern <- patterns[j]
        message("处理模式 ", j, "/", n_pat, ": ", pattern)

        pattern_parts <- strsplit(pattern, ";")[[1]]
        patt_src_loop <- src[
            src$SEX == pattern_parts[1] &
                src$AGE_GROUP == pattern_parts[2] &
                src$AREA == pattern_parts[3] &
                src$CAUSE == pattern_parts[4],
        ] # PERIOD is original type

        if (nrow(patt_src_loop) < 10) {
            message("由于数据不足，跳过模式 ( overall < 10 rows )")
            next
        }

        hist_src_loop <- patt_src_loop[patt_src_loop$event_index == "0", ] # PERIOD is original type
        if (nrow(hist_src_loop) < 10) {
            message("由于历史数据不足，跳过模式 ( historical < 10 rows )")
            next
        }

        model_results <- fit_and_predict_his_tr(patt_src_loop, hist_src_loop, l_period_data)
        # model_results$src_pandemic$PERIOD is now numeric

        if (length(model_results) == 0 || !is.list(model_results) || is.null(model_results$src_pandemic) || nrow(model_results$src_pandemic) == 0) {
            message("此模式的模型失败或未返回有效结果")
            next
        }

        year_predict_loop <- sort(unique(out_data_template$YEAR))
        single_pattern_result_df <- update_output_his_tr(out_data_template, model_results, pattern, year_predict_loop)
        # single_pattern_result_df$PERIOD is original type (from out_data_template)

        if (nrow(single_pattern_result_df) > 0) {
            results_list[[j]] <- single_pattern_result_df
            message("成功处理模式: ", pattern, " (", nrow(single_pattern_result_df), " 行)")
        } else {
            message("模式处理后无结果: ", pattern)
        }
    }

    final_out_data <- NULL
    if (length(results_list) > 0) {
        valid_results <- Filter(function(x) !is.null(x) && nrow(x) > 0, results_list)
        if (length(valid_results) > 0) {
            final_out_data <- do.call(rbind, valid_results) # final_out_data$PERIOD is original type
            message("成功合并 ", length(valid_results), " 个模式的结果")
        } else {
            message("没有成功处理并产生有效结果的模式。")
        }
    } else {
        warning("没有成功处理的模式!")
    }

    if (!is.null(final_out_data) && nrow(final_out_data) > 0) {
        final_out_data <- process_model_results(final_out_data, "Historical average and trend model")
        # process_model_results receives PERIOD in its original type
    } else {
        message("最终模型输出为空，未调用 process_model_results。")
        return(dplyr::tibble())
    }

    end_time <- Sys.time()
    total_time <- difftime(end_time, start_time, units = "mins")
    message("历史平均及趋势模型计算完成，总时间: ", round(total_time, 2), " 分钟")
    flush.console()

    return(final_out_data)
}
