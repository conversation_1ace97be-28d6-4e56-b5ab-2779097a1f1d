# New function: calculate overall summary statistics by pattern
calculate_overall_summary <- function(out_spline) {
    # Debug output
    message("[DEBUG] calculate_overall_summary called with out_spline dimensions: ", nrow(out_spline), " x ", ncol(out_spline))
    message("[DEBUG] out_spline columns: ", paste(names(out_spline), collapse = ", "))
    flush.console()
    
    # Check if out_spline is valid
    if (nrow(out_spline) == 0) {
        message("[DEBUG] Warning: out_spline is empty")
        flush.console()
        return(data.frame())
    }
    
    # Check if required columns exist
    required_cols <- c("NO_DEATHS", "EXCESS_DEATHS", "EXP_DEATHS", "UPPER_LIMIT", "LOWER_LIMIT", "event_index")
    missing_cols <- setdiff(required_cols, names(out_spline))
    
    if (length(missing_cols) > 0) {
        message("[DEBUG] Warning: Missing columns in out_spline: ", paste(missing_cols, collapse = ", "))
        flush.console()
        # Create a minimal summary with available data
        if ("NO_DEATHS" %in% names(out_spline)) {
            overall_summary <- out_spline %>%
                mutate(NO_DEATHS = as.numeric(NO_DEATHS)) %>%
                group_by(COUNTRY, ISO3, WM_IDENTIFIER, SEX, AGE_GROUP, AREA, CAUSE) %>%
                reframe(
                    TOTAL_DEATHS = sum(NO_DEATHS, na.rm = TRUE),
                    TOTAL_EXPECTED = NA,
                    TOTAL_EXCESS = NA,
                    TOTAL_SE = NA,
                    EXCESS_LOWER = NA,
                    EXCESS_UPPER = NA,
                    P_SCORE = NA,
                    event_name = "All Events",
                    event_index = "999"
                )
            return(overall_summary)
        } else {
            # Cannot create summary without NO_DEATHS
            message("[DEBUG] Cannot create summary: NO_DEATHS column missing")
            flush.console()
            return(data.frame())
        }
    }
    
    # Calculate overall summary statistics for each pattern
    tryCatch({
        overall_summary <- out_spline %>%
            mutate(NO_DEATHS = as.numeric(NO_DEATHS)) %>%
            mutate(EXP_DEATHS = as.numeric(EXP_DEATHS)) %>%
            mutate(EXCESS_DEATHS = as.numeric(EXCESS_DEATHS)) %>%
            mutate(UPPER_LIMIT = as.numeric(UPPER_LIMIT)) %>%
            mutate(LOWER_LIMIT = as.numeric(LOWER_LIMIT)) %>%
            filter(event_index != "0" & !is.na(NO_DEATHS) & !is.na(event_index)) %>%
            group_by(COUNTRY, ISO3, WM_IDENTIFIER, SEX, AGE_GROUP, AREA, CAUSE) %>%
            reframe(
                TOTAL_DEATHS = sum(NO_DEATHS, na.rm = TRUE),
                TOTAL_EXPECTED = sum(EXP_DEATHS, na.rm = TRUE),
                TOTAL_EXCESS = sum(NO_DEATHS - EXP_DEATHS, na.rm = TRUE),
                # Reverse engineer standard error from confidence intervals and calculate total variance
                TOTAL_SE = sqrt(sum(((UPPER_LIMIT - LOWER_LIMIT) / (2*1.96))^2, na.rm = TRUE))
            ) %>%
            mutate(
                EXCESS_LOWER = TOTAL_EXCESS - 1.96 * TOTAL_SE,
                EXCESS_UPPER = TOTAL_EXCESS + 1.96 * TOTAL_SE,
                P_SCORE = 100 * TOTAL_EXCESS / TOTAL_EXPECTED,
                event_name = "All Events",
                event_index = "999"
            )
        
        message("[DEBUG] calculate_overall_summary completed with result dimensions: ", nrow(overall_summary), " x ", ncol(overall_summary))
        flush.console()
        return(overall_summary)
    }, error = function(e) {
        message("[DEBUG] Error in calculate_overall_summary: ", e$message)
        flush.console()
        return(data.frame())
    })
}
