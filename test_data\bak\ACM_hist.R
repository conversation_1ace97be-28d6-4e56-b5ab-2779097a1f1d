# Load required libraries
required_packages <- c("dplyr", "reshape2", "readxl", "stringr", "mgcv")
lapply(required_packages, require, character.only = TRUE)

# Define constants
DOM <- c(31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31)
MOY <- c("Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec")
YRS2020 <- 2015

# Helper function: date calculation
calculate_dates <- function(src, max_period, nys, DOM, MOY) {
    if (max_period == 12) {
        day <- cumsum(c(0, DOM))[src$PERIOD] + 15
        src$DATE <- cumsum(c(0, rep(365, nys)))[src$YEAR - 2014] + day
    } else {
        day <- as.numeric(substr(src$DATE_TO_SPECIFY_WEEK, 5, 6))
        if (all(!is.na(day))) {
            Date <- match(substr(src$DATE_TO_SPECIFY_WEEK, 1, 3), MOY)
            day <- cumsum(c(0, DOM))[Date] + day - 3.5
            loc_YEAR <- src$YEAR
            bad_day <- (day - 0) < 0 & !is.na(day)
            loc_YEAR[bad_day] <- loc_YEAR[bad_day] + 1
            src$DATE <- cumsum(c(0, rep(365, nys)))[loc_YEAR - 2014] + day
        } else {
            day <- cumsum(c(0, rep(7, 52)))[src$PERIOD] + 3.5
            src$DATE <- cumsum(c(0, rep(365, nys)))[src$YEAR - 2014] + day
        }
    }
    return(src)
}


# Helper function: Calculate historical average
calculate_historical_average_hist <- function(hist_src, src_pandemic, l_period, YRS2020) {
    ave_deaths <- as.numeric(tapply(hist_src$NO_DEATHS, hist_src$PERIOD, mean, na.rm = TRUE))
    var_deaths <- as.numeric(tapply(hist_src$NO_DEATHS, hist_src$PERIOD, var, na.rm = TRUE))
    num_deaths <- as.numeric(tapply(hist_src$NO_DEATHS, hist_src$PERIOD, length))
    nyear_predict <- length(unique(src_pandemic$YEAR[src_pandemic$YEAR >= YRS2020]))
    ave_deaths <- rep(ave_deaths, nyear_predict + 5)
    var_deaths <- rep(var_deaths, nyear_predict + 5)
    num_deaths <- rep(num_deaths, nyear_predict + 5)
    ave_deaths_lower <- ave_deaths - qnorm(0.975) * sqrt(var_deaths * (1 + 1 / num_deaths))
    ave_deaths_upper <- ave_deaths + qnorm(0.975) * sqrt(var_deaths * (1 + 1 / num_deaths))

    return(list(
        ave_deaths = ave_deaths,
        ave_deaths_lower = ave_deaths_lower,
        ave_deaths_upper = ave_deaths_upper
    ))
}

# Helper function: Fit and predict (only historical average)
fit_and_predict_hist <- function(patt_src, hist_src, l_period, YRS2020, nys, DOM) {
    t.start <- Sys.time()

    # Create src_pandemic
    if (l_period > 51) {
        day <- cumsum(c(0, rep(7, 52)))[patt_src$PERIOD] + 3.5
        aDATE <- cumsum(c(0, rep(365, nys)))[patt_src$YEAR - 2014] + day
        src_pandemic <- patt_src %>%
            mutate(loc_DATE = aDATE, logdays = log(7))
    } else {
        day <- cumsum(c(0, DOM))[patt_src$PERIOD] + 15
        aDATE <- cumsum(c(0, rep(365, nys)))[patt_src$YEAR - 2014] + day
        days <- DOM[patt_src$PERIOD]
        days[14] <- 29 # Feb 2016
        if (length(days) > 61) days[62] <- 29 # Feb 2020
        src_pandemic <- patt_src %>%
            mutate(loc_DATE = aDATE, logdays = log(days))
    }

    # Calculate historical average
    hist_avg <- calculate_historical_average_hist(hist_src, src_pandemic, l_period, YRS2020)

    message("Pattern processing time: ", round(difftime(Sys.time(), t.start, units = "secs"), 1), " sec")

    return(list(
        src_pandemic = src_pandemic,
        estim.median = hist_avg$ave_deaths[1:nrow(src_pandemic)],
        estim.lower = hist_avg$ave_deaths_lower[1:nrow(src_pandemic)],
        estim.upper = hist_avg$ave_deaths_upper[1:nrow(src_pandemic)]
    ))
}

# Helper function: Update output
update_output_hist <- function(out_hist, model_results, j, pattern, l_period,
                               patt_src, hist_src, year_predict) {
    src_pandemic <- model_results$src_pandemic
    estim.median <- model_results$estim.median
    estim.lower <- model_results$estim.lower
    estim.upper <- model_results$estim.upper

    nyear_predict <- length(year_predict)

    # Create result data frame
    result_df <- out_hist[0, ]

    for (iyear_predict in seq_along(year_predict)) {
        y <- year_predict[iyear_predict]
        for (k in 0:(l_period - 1)) {
            a <- src_pandemic$YEAR == y & src_pandemic$PERIOD == (k + 1)
            y_temp <- y
            while (!any(a) && y_temp >= 2017) {
                y_temp <- y_temp - 1
                a <- src_pandemic$YEAR == y_temp & src_pandemic$PERIOD == (k + 1)
            }
            if (!any(a)) {
                a <- src_pandemic$YEAR == y & src_pandemic$PERIOD == k
            }

            # Get records for the current pattern
            current_records <- out_hist[
                out_hist$YEAR == y &
                    out_hist$PERIOD == (k + 1) &
                    paste(
                        out_hist$SEX, out_hist$AGE_GROUP,
                        out_hist$AREA, out_hist$CAUSE
                    ) == pattern,
            ]

            if (nrow(current_records) > 0) {
                current_records$ESTIMATE <- estim.median[a]
                current_records$LOWER_LIMIT <- estim.lower[a]
                current_records$UPPER_LIMIT <- estim.upper[a]
                result_df <- rbind(result_df, current_records)
            }
        }
    }

    return(result_df)
}

# Helper function: Final processing
finalize_output_hist <- function(out_hist, wm_ident) {
    out_hist <- out_hist %>%
        mutate(EXCESS_DEATHS = NO_DEATHS - ESTIMATE) %>%
        melt(id.vars = c(
            "COUNTRY", "ISO3", "WM_IDENTIFIER", "YEAR", "PERIOD",
            "SEX", "AGE_GROUP", "AREA", "CAUSE", "DATE_TO_SPECIFY_WEEK",
            "SE_IDENTIFIER", "LOWER_LIMIT", "UPPER_LIMIT", "EXCESS_DEATHS",
            "event_name", "event_index"
        )) %>%
        rename(SERIES = variable, NO_DEATHS = value) %>%
        mutate(SERIES = case_when(
            SERIES == "NO_DEATHS" ~ "Current deaths",
            SERIES == "ESTIMATE" ~ "Historical average"
        )) %>%
        filter(!is.na(SERIES), YEAR != "") %>%
        group_by(
            COUNTRY, ISO3, WM_IDENTIFIER, YEAR, PERIOD,
            SEX, AGE_GROUP, AREA, CAUSE, DATE_TO_SPECIFY_WEEK,
            SE_IDENTIFIER, LOWER_LIMIT, UPPER_LIMIT, EXCESS_DEATHS,
            event_name, event_index
        ) %>%
        reframe(
            EXP_DEATHS = NO_DEATHS[SERIES == "Historical average"],
            NO_DEATHS = NO_DEATHS[SERIES == "Current deaths"],
            SERIES = "Historical average"
        ) %>%
        arrange(AREA, SEX, AGE_GROUP, YEAR, PERIOD)

    return(out_hist)
}

# Helper function: Serial processing
process_serial_hist <- function(n_pat, patterns, src, l_period, YRS2020, nys, DOM, out_hist, wm_ident) {
    message("Using serial processing...")
    start_time <- Sys.time()

    results <- list()

    for (j in seq_len(n_pat)) {
        pattern <- patterns[j]
        message("Processing pattern: ", pattern)

        patt_src <- src[paste(src$SEX, src$AGE_GROUP, src$AREA, src$CAUSE) == pattern, ]
        hist_src <- patt_src[patt_src$event_index == "0", ]

        if (sum(hist_src$NO_DEATHS, na.rm = TRUE) <= 200) {
            message("Skipping insufficient data pattern: ", pattern)
            next
        }

        model_results <- fit_and_predict_hist(patt_src, hist_src, l_period, YRS2020, nys, DOM)
        if (is.null(model_results)) {
            message("Model fitting failed, skipping pattern: ", pattern)
            next
        }

        result <- update_output_hist(out_hist, model_results, j, pattern, l_period,
            patt_src, hist_src,
            year_predict = sort(unique(out_hist$YEAR))
        )

        if (!is.null(result) && nrow(result) > 0) {
            results[[j]] <- result
            message("Successfully processed pattern: ", pattern, " (", nrow(result), " rows)")
        }
    }

    if (length(results) > 0) {
        out_hist <- do.call(rbind, results[!sapply(results, is.null)])
        message("Successfully combined results from ", length(results[!sapply(results, is.null)]), " patterns")
    } else {
        warning("No successfully processed patterns!")
        return(NULL)
    }

    end_time <- Sys.time()
    total_time <- difftime(end_time, start_time, units = "mins")
    message("Serial processing completed, total time: ", round(total_time, 2), " minutes")

    out_hist <- finalize_output_hist(out_hist, wm_ident)
    return(out_hist)
}

# Main function
fcn_hist <- function(src) {
    # Preprocess data
    src <- src %>%
        filter(PERIOD <= 52) %>%
        arrange(SEX, AGE_GROUP, AREA, CAUSE, YEAR, PERIOD) %>%
        mutate(NO_DEATHS = as.numeric(NO_DEATHS))

    nys <- length(unique(src$YEAR))
    max_period <- max(src$PERIOD, na.rm = TRUE)
    wm_ident <- ifelse(max_period == 12, "Month", "Week")
    l_period <- ifelse(max_period == 12, 12, 52)

    # Calculate dates (kept for standalone execution)
    src <- calculate_dates(src, max_period, nys, DOM, MOY)

    # Initialize output data frame (kept for standalone execution)
    out_hist <- initialize_output(src, YRS2020, wm_ident, l_period)

    # Get unique patterns
    patterns <- src %>%
        select(SEX, AGE_GROUP, AREA, CAUSE) %>%
        distinct() %>%
        mutate(patterns = paste(SEX, AGE_GROUP, AREA, CAUSE)) %>%
        pull(patterns)
    n_pat <- length(patterns)

    # Serial processing
    out_hist <- process_serial_hist(n_pat, patterns, src, l_period, YRS2020, nys, DOM, out_hist, wm_ident)

    # Calculate summary statistics
    summary_by_event <- calculate_summary_by_event(out_hist)
    overall_summary <- calculate_overall_summary(out_hist)
    all_summaries <- bind_rows(summary_by_event, overall_summary)

    # Add attributes
    attr(out_hist, "summary_by_event") <- summary_by_event
    attr(out_hist, "overall_summary") <- overall_summary
    attr(out_hist, "all_summaries") <- all_summaries

    return(out_hist)
}

# # Functions identical to reference code (kept for standalone execution, can be removed if using reference code)
# calculate_dates <- function(src, max_period, nys, DOM, MOY) {
#     if (max_period == 12) {
#         day <- cumsum(c(0, DOM))[src$PERIOD] + 15
#         src$DATE <- cumsum(c(0, rep(365, nys)))[src$YEAR - 2014] + day
#     } else {
#         day <- as.numeric(substr(src$DATE_TO_SPECIFY_WEEK, 5, 6))
#         if (all(!is.na(day))) {
#             Date <- match(substr(src$DATE_TO_SPECIFY_WEEK, 1, 3), MOY)
#             day <- cumsum(c(0, DOM))[Date] + day - 3.5
#             loc_YEAR <- src$YEAR
#             bad_day <- (day - 0) < 0 & !is.na(day)
#             loc_YEAR[bad_day] <- loc_YEAR[bad_day] + 1
#             src$DATE <- cumsum(c(0, rep(365, nys)))[loc_YEAR - 2014] + day
#         } else {
#             day <- cumsum(c(0, rep(7, 52)))[src$PERIOD] + 3.5
#             src$DATE <- cumsum(c(0, rep(365, nys)))[src$YEAR - 2014] + day
#         }
#     }
#     return(src)
# }

# initialize_output <- function(src, YRS2020, wm_ident, l_period) {
#     out_hist <- src %>%
#         filter(YEAR >= YRS2020) %>%
#         mutate(
#             ESTIMATE = NO_DEATHS,
#             LOWER_LIMIT = NO_DEATHS,
#             UPPER_LIMIT = NO_DEATHS,
#             EXCESS_DEATHS = NO_DEATHS,
#             WM_IDENTIFIER = wm_ident
#         )
#     return(out_hist)
# }

# calculate_summary_by_event <- function(out_hist) {
#     summary_by_event <- out_hist %>%
#         filter(event_index != 0 & !is.na(NO_DEATHS)) %>%
#         group_by(COUNTRY, ISO3, WM_IDENTIFIER, SEX, AGE_GROUP, AREA, CAUSE, event_index, event_name) %>%
#         reframe(
#             TOTAL_DEATHS = sum(NO_DEATHS, na.rm = TRUE),
#             TOTAL_EXPECTED = sum(EXP_DEATHS, na.rm = TRUE),
#             TOTAL_EXCESS = sum(NO_DEATHS - EXP_DEATHS, na.rm = TRUE),
#             TOTAL_SE = sqrt(sum(((UPPER_LIMIT - LOWER_LIMIT) / (1.96 * 2))^2, na.rm = TRUE))
#         ) %>%
#         mutate(
#             EXCESS_LOWER = TOTAL_EXCESS - 1.96 * TOTAL_SE,
#             EXCESS_UPPER = TOTAL_EXCESS + 1.96 * TOTAL_SE,
#             P_SCORE = 100 * TOTAL_EXCESS / TOTAL_EXPECTED
#         )

#     return(summary_by_event)
# }

# calculate_overall_summary <- function(out_hist) {
#     overall_summary <- out_hist %>%
#         filter(event_index != 0 & !is.na(NO_DEATHS)) %>%
#         group_by(COUNTRY, ISO3, WM_IDENTIFIER, SEX, AGE_GROUP, AREA, CAUSE) %>%
#         reframe(
#             TOTAL_DEATHS = sum(NO_DEATHS, na.rm = TRUE),
#             TOTAL_EXPECTED = sum(EXP_DEATHS, na.rm = TRUE),
#             TOTAL_EXCESS = sum(NO_DEATHS - EXP_DEATHS, na.rm = TRUE),
#             TOTAL_SE = sqrt(sum(((UPPER_LIMIT - LOWER_LIMIT) / (2 * 1.96))^2, na.rm = TRUE))
#         ) %>%
#         mutate(
#             EXCESS_LOWER = TOTAL_EXCESS - 1.96 * TOTAL_SE,
#             EXCESS_UPPER = TOTAL_EXCESS + 1.96 * TOTAL_SE,
#             P_SCORE = 100 * TOTAL_EXCESS / TOTAL_EXPECTED,
#             event_name = "All Events",
#             event_index = 999
#         )

#     return(overall_summary)
# }

# # Use parallel processing (default)
# res2 <- fcn_hist(result)
