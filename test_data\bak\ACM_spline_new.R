# 加载公共函数
source("modules/common_functions.R")

# Helper function: model fitting and prediction for Negative Binomial
fit_and_predict <- function(patt_src, hist_src, l_period, nys, DOM) {
    t.start <- Sys.time()

    # Set parameters based on period type (month/week)
    if (l_period > 51) {
        # Weekly data
        day <- cumsum(c(0, rep(7, 52)))[patt_src$PERIOD] + 3.5
        aDATE <- cumsum(c(0, rep(365, nys)))[patt_src$YEAR - 2014] + day
        num.cycle <- 52
        len.cycle <- 7
        hist_src$logdays <- log(7)
        src_pandemic <- patt_src %>%
            mutate(loc_DATE = aDATE, logdays = log(7))

        # Fit model
        fit <- tryCatch({
            mgcv::gam(NO_DEATHS ~ offset(logdays) + YEAR +
                     s(PERIOD, bs = "cc", fx = TRUE, k = 9),
                     knots = list(PERIOD = c(0, num.cycle)),
                     method = "REML", family = nb(), data = hist_src)
        }, error = function(e) {
            warning("Negative binomial model fitting failed for pattern: ",
                   paste(unique(patt_src$SEX, patt_src$AGE_GROUP,
                                patt_src$AREA, patt_src$CAUSE), collapse = " "))
            return(NULL)
        })
        if (is.null(fit)) {
            return(data.frame())
        }
    } else {
        # Monthly data
        day <- cumsum(c(0, DOM))[patt_src$PERIOD] + 15
        aDATE <- cumsum(c(0, rep(365, nys)))[patt_src$YEAR - 2014] + day
        num.cycle <- 12
        len.cycle <- 30
        days <- DOM[hist_src$PERIOD]
        days[14] <- 29  # Adjust for leap year
        hist_src$logdays <- log(days)

        days <- DOM[patt_src$PERIOD]
        days[14] <- 29  # Feb 2016
        if (length(days) > 61) days[62] <- 29  # Feb 2020
        src_pandemic <- patt_src %>%
            mutate(loc_DATE = aDATE, logdays = log(days))

        # Fit model
        fit <- tryCatch({
            mgcv::gam(NO_DEATHS ~ offset(logdays) + YEAR +
                     s(PERIOD, bs = "cc", fx = TRUE, k = 5),
                     knots = list(PERIOD = c(0, num.cycle)),
                     method = "REML", family = nb(), data = hist_src)
        }, error = function(e) {
            warning("Negative binomial model fitting failed for pattern: ",
                   paste(unique(patt_src$SEX, patt_src$AGE_GROUP,
                                patt_src$AREA, patt_src$CAUSE), collapse = " "))
            return(NULL)
        })
        if (is.null(fit)) return(data.frame())
    }

    # Model prediction
    estim <- mgcv::predict.gam(fit, newdata = src_pandemic, se.fit = TRUE)
    theta <- fit$family$getTheta(TRUE)

    # Simulate from posterior distribution
    set.seed(1)
    a <- matrix(rnorm(n = 1000 * length(estim$fit),
                     mean = estim$fit, sd = estim$se.fit),
               ncol = 1000)
    estim.median <- apply(a, 1, function(x) {
        mean(qnbinom(p = 0.5, mu = exp(x), size = theta))
    })
    estim.lower <- apply(a, 1, function(x) {
        mean(qnbinom(p = 0.025, mu = exp(x), size = theta))
    })
    estim.upper <- apply(a, 1, function(x) {
        mean(qnbinom(p = 0.975, mu = exp(x), size = theta))
    })

    # Ensure non-negative values
    estim.median[estim.median < 0] <- 0
    estim.lower[estim.lower < 0] <- 0
    estim.upper[estim.upper < 0] <- 0

    # Standardization
    estim.median.std <- len.cycle * estim.median / exp(src_pandemic$logdays)
    estim.lower.std <- len.cycle * estim.lower / exp(src_pandemic$logdays)
    estim.upper.std <- len.cycle * estim.upper / exp(src_pandemic$logdays)

    message("Pattern processing time: ", round(difftime(Sys.time(), t.start, units = "secs"), 1), " sec")

    return(list(
        src_pandemic = src_pandemic,
        estim.median = estim.median,
        estim.lower = estim.lower,
        estim.upper = estim.upper
    ))
}

# Helper function: model fitting and prediction for Poisson
fit_and_predict_poisson <- function(patt_src, hist_src, l_period, nys, DOM) {
    message("Using General Poisson Model...")
    flush.console()

    t.start <- Sys.time()

    # Set parameters based on period type (month/week)
    if (l_period > 51) {
        # Weekly data
        day <- cumsum(c(0, rep(7, 52)))[patt_src$PERIOD] + 3.5
        aDATE <- cumsum(c(0, rep(365, nys)))[patt_src$YEAR - 2014] + day
        num.cycle <- 52
        len.cycle <- 7
        hist_src$logdays <- log(7)
        src_pandemic <- patt_src %>%
            mutate(loc_DATE = aDATE, logdays = log(7))

        # Fit model with Poisson family
        fit <- tryCatch({
            mgcv::gam(NO_DEATHS ~ offset(logdays) + YEAR +
                     s(PERIOD, bs = "cc", fx = TRUE, k = 9),
                     knots = list(PERIOD = c(0, num.cycle)),
                     method = "REML", family = poisson(), data = hist_src)
        }, error = function(e) {
            warning("Poisson model fitting failed for pattern: ",
                   paste(unique(patt_src$SEX, patt_src$AGE_GROUP,
                                patt_src$AREA, patt_src$CAUSE), collapse = " "))
            return(NULL)
        })
        if (is.null(fit)) {
            return(data.frame())
        }
    } else {
        # Monthly data
        day <- cumsum(c(0, DOM))[patt_src$PERIOD] + 15
        aDATE <- cumsum(c(0, rep(365, nys)))[patt_src$YEAR - 2014] + day
        num.cycle <- 12
        len.cycle <- 30
        days <- DOM[hist_src$PERIOD]
        days[14] <- 29  # Adjust for leap year
        hist_src$logdays <- log(days)

        days <- DOM[patt_src$PERIOD]
        days[14] <- 29  # Feb 2016
        if (length(days) > 61) days[62] <- 29  # Feb 2020
        src_pandemic <- patt_src %>%
            mutate(loc_DATE = aDATE, logdays = log(days))

        # Fit model with Poisson family
        fit <- tryCatch({
            mgcv::gam(NO_DEATHS ~ offset(logdays) + YEAR +
                     s(PERIOD, bs = "cc", fx = TRUE, k = 5),
                     knots = list(PERIOD = c(0, num.cycle)),
                     method = "REML", family = poisson(), data = hist_src)
        }, error = function(e) {
            warning("Poisson model fitting failed for pattern: ",
                   paste(unique(patt_src$SEX, patt_src$AGE_GROUP,
                                patt_src$AREA, patt_src$CAUSE), collapse = " "))
            return(NULL)
        })
        if (is.null(fit)) return(data.frame())
    }

    # Model prediction
    estim <- mgcv::predict.gam(fit, newdata = src_pandemic, se.fit = TRUE)

    # For Poisson, we use qpois instead of qnbinom
    set.seed(1)
    a <- matrix(rnorm(n = 1000 * length(estim$fit),
                     mean = estim$fit, sd = estim$se.fit),
               ncol = 1000)
    estim.median <- apply(a, 1, function(x) {
        mean(qpois(lambda = exp(x), p = 0.5))
    })
    estim.lower <- apply(a, 1, function(x) {
        mean(qpois(lambda = exp(x), p = 0.025))
    })
    estim.upper <- apply(a, 1, function(x) {
        mean(qpois(lambda = exp(x), p = 0.975))
    })

    # Ensure non-negative values
    estim.median[estim.median < 0] <- 0
    estim.lower[estim.lower < 0] <- 0
    estim.upper[estim.upper < 0] <- 0

    # Standardization
    estim.median.std <- len.cycle * estim.median / exp(src_pandemic$logdays)
    estim.lower.std <- len.cycle * estim.lower / exp(src_pandemic$logdays)
    estim.upper.std <- len.cycle * estim.upper / exp(src_pandemic$logdays)

    message("Poisson pattern processing time: ", round(difftime(Sys.time(), t.start, units = "secs"), 1), " sec")
    flush.console()

    return(list(
        src_pandemic = src_pandemic,
        estim.median = estim.median,
        estim.lower = estim.lower,
        estim.upper = estim.upper
    ))
}

# Helper function: model fitting and prediction for Zero-Inflated Poisson
fit_and_predict_zip <- function(patt_src, hist_src, l_period, nys, DOM) {
    message("Using Zero-Inflated Poisson Model...")
    flush.console()

    t.start <- Sys.time()

    # Set parameters based on period type (month/week)
    if (l_period > 51) {
        # Weekly data
        day <- cumsum(c(0, rep(7, 52)))[patt_src$PERIOD] + 3.5
        aDATE <- cumsum(c(0, rep(365, nys)))[patt_src$YEAR - 2014] + day
        num.cycle <- 52
        len.cycle <- 7
        hist_src$logdays <- log(7)
        src_pandemic <- patt_src %>%
            mutate(loc_DATE = aDATE, logdays = log(7))

        # Fit model with Zero-Inflated Poisson family
        fit <- tryCatch({
            mgcv::gam(NO_DEATHS ~ offset(logdays) + YEAR +
                     s(PERIOD, bs = "cc", fx = TRUE, k = 9),
                     knots = list(PERIOD = c(0, num.cycle)),
                     method = "REML", family = mgcv::ziP(), data = hist_src)
        }, error = function(e) {
            warning("Zero-Inflated Poisson model fitting failed for pattern: ",
                   paste(unique(patt_src$SEX, patt_src$AGE_GROUP,
                                patt_src$AREA, patt_src$CAUSE), collapse = " "))
            return(NULL)
        })
        if (is.null(fit)) {
            return(data.frame())
        }
    } else {
        # Monthly data
        day <- cumsum(c(0, DOM))[patt_src$PERIOD] + 15
        aDATE <- cumsum(c(0, rep(365, nys)))[patt_src$YEAR - 2014] + day
        num.cycle <- 12
        len.cycle <- 30
        days <- DOM[hist_src$PERIOD]
        days[14] <- 29  # Adjust for leap year
        hist_src$logdays <- log(days)

        days <- DOM[patt_src$PERIOD]
        days[14] <- 29  # Feb 2016
        if (length(days) > 61) days[62] <- 29  # Feb 2020
        src_pandemic <- patt_src %>%
            mutate(loc_DATE = aDATE, logdays = log(days))

        # Fit model with Zero-Inflated Poisson family
        fit <- tryCatch({
            mgcv::gam(NO_DEATHS ~ offset(logdays) + YEAR +
                     s(PERIOD, bs = "cc", fx = TRUE, k = 5),
                     knots = list(PERIOD = c(0, num.cycle)),
                     method = "REML", family = mgcv::ziP(), data = hist_src)
        }, error = function(e) {
            warning("Zero-Inflated Poisson model fitting failed for pattern: ",
                   paste(unique(patt_src$SEX, patt_src$AGE_GROUP,
                                patt_src$AREA, patt_src$CAUSE), collapse = " "))
            return(NULL)
        })
        if (is.null(fit)) return(data.frame())
    }

    # Model prediction
    estim <- mgcv::predict.gam(fit, newdata = src_pandemic, se.fit = TRUE)

    # Get the zero-inflation probability
    theta <- fit$family$getTheta()

    # For Zero-Inflated Poisson, we use custom prediction
    set.seed(1)
    a <- matrix(rnorm(n = 1000 * length(estim$fit),
                     mean = estim$fit, sd = estim$se.fit),
               ncol = 1000)

    # Custom prediction function for ZIP
    predict_zip <- function(lambda, theta, p) {
        # theta is the zero-inflation probability
        # lambda is the Poisson mean
        # p is the quantile probability

        # Calculate quantile for ZIP distribution
        if (p <= theta) {
            return(0)  # Return 0 if quantile is in the zero-inflated part
        } else {
            # Adjust p for the Poisson part
            adjusted_p <- (p - theta) / (1 - theta)
            return(qpois(p = adjusted_p, lambda = lambda))
        }
    }

    estim.median <- apply(a, 1, function(x) {
        mean(sapply(exp(x), function(lambda) predict_zip(lambda, theta, 0.5)))
    })
    estim.lower <- apply(a, 1, function(x) {
        mean(sapply(exp(x), function(lambda) predict_zip(lambda, theta, 0.025)))
    })
    estim.upper <- apply(a, 1, function(x) {
        mean(sapply(exp(x), function(lambda) predict_zip(lambda, theta, 0.975)))
    })

    # Ensure non-negative values
    estim.median[estim.median < 0] <- 0
    estim.lower[estim.lower < 0] <- 0
    estim.upper[estim.upper < 0] <- 0

    # Standardization
    estim.median.std <- len.cycle * estim.median / exp(src_pandemic$logdays)
    estim.lower.std <- len.cycle * estim.lower / exp(src_pandemic$logdays)
    estim.upper.std <- len.cycle * estim.upper / exp(src_pandemic$logdays)

    message("Zero-Inflated Poisson pattern processing time: ", round(difftime(Sys.time(), t.start, units = "secs"), 1), " sec")
    flush.console()

    return(list(
        src_pandemic = src_pandemic,
        estim.median = estim.median,
        estim.lower = estim.lower,
        estim.upper = estim.upper
    ))
}

# Helper function: update output
update_output <- function(out_spline, model_results, j, pattern, l_period,
                         patt_src, hist_src, year_predict) {
    # Debug output
    message("[DEBUG] Updating output for pattern: ", pattern)
    message("[DEBUG] Model results class: ", class(model_results))
    message("[DEBUG] Model results contains: ", paste(names(model_results), collapse = ", "))
    flush.console()

    # Extract model results
    src_pandemic <- model_results$src_pandemic
    estim.median <- model_results$estim.median
    estim.lower <- model_results$estim.lower
    estim.upper <- model_results$estim.upper

    # Check if model results are valid
    if (is.null(src_pandemic) || is.null(estim.median) || is.null(estim.lower) || is.null(estim.upper)) {
        message("[DEBUG] Warning: Invalid model results for pattern: ", pattern)
        return(out_spline)
    }

    # Extract pattern data
    pattern_parts <- strsplit(pattern, " ")[[1]]

    # Initialize result data frame
    result_df <- out_spline[0, ]

    # Process each year and period
    nyear_predict <- length(year_predict)
    for (iyear_predict in 1:nyear_predict) {
        y <- year_predict[iyear_predict]
        for (k in 1:l_period) {
            # Find matching records in src_pandemic
            a <- src_pandemic$YEAR == y & src_pandemic$PERIOD == (k + 1)
            y_temp <- y

            # Try different combinations if no match found
            if (sum(a) == 0) {
                y_temp <- y_temp - 1
                a <- src_pandemic$YEAR == y_temp & src_pandemic$PERIOD == (k + 1)
            }
            if (sum(a) == 0) {
                a <- src_pandemic$YEAR == y & src_pandemic$PERIOD == k
            }

            # Find matching records in out_spline
            current_records <- out_spline[
                out_spline$SEX == pattern_parts[1] &
                out_spline$AGE_GROUP == pattern_parts[2] &
                out_spline$AREA == pattern_parts[3] &
                out_spline$CAUSE == pattern_parts[4] &
                out_spline$YEAR == y &
                out_spline$PERIOD == k,
            ]

            # Update records if found and have matching src_pandemic records
            if (nrow(current_records) > 0 && sum(a) > 0) {
                current_records$ESTIMATE <- estim.median[a]
                current_records$LOWER_LIMIT <- estim.lower[a]
                current_records$UPPER_LIMIT <- estim.upper[a]
                result_df <- rbind(result_df, current_records)
            }
        }
    }

    return(result_df)
}

# Helper function: serial processing with model selection
process_serial_with_model <- function(n_pat, patterns, src, l_period, YRS2020, nys, DOM, out_spline, wm_ident, model_type) {
    message("Using serial processing with model type: ", model_type)
    flush.console()
    start_time <- Sys.time()

    # Process each pattern
    for (j in 1:n_pat) {
        pattern <- patterns[j]
        message("Processing pattern ", j, "/", n_pat, ": ", pattern)
        flush.console()

        # Extract pattern data
        pattern_parts <- strsplit(pattern, " ")[[1]]
        patt_src <- src %>%
            filter(SEX == pattern_parts[1],
                   AGE_GROUP == pattern_parts[2],
                   AREA == pattern_parts[3],
                   CAUSE == pattern_parts[4])

        # Skip if insufficient data
        if (nrow(patt_src) < 10) {
            message("Skipping pattern due to insufficient data")
            flush.console()
            next
        }

        # Prepare historical data
        hist_src <- patt_src %>%
            filter(event_index == "0")

        # Skip if insufficient historical data
        if (nrow(hist_src) < 10) {
            message("Skipping pattern due to insufficient historical data")
            flush.console()
            next
        }

        # Call appropriate model function based on model_type
        message("[DEBUG] Model type in process_serial_with_model: ", model_type)
        flush.console()

        if (model_type == "poisson") {
            message("[DEBUG] Calling fit_and_predict_poisson for pattern: ", pattern)
            flush.console()
            model_results <- fit_and_predict_poisson(patt_src, hist_src, l_period, nys, DOM)
            message("[DEBUG] fit_and_predict_poisson returned: ", paste(names(model_results), collapse = ", "))
            flush.console()
        } else if (model_type == "zip") {
            message("[DEBUG] Calling fit_and_predict_zip for pattern: ", pattern)
            flush.console()
            model_results <- fit_and_predict_zip(patt_src, hist_src, l_period, nys, DOM)
            message("[DEBUG] fit_and_predict_zip returned: ", paste(names(model_results), collapse = ", "))
            flush.console()
        } else {
            # Default to negative binomial
            message("[DEBUG] Using negative binomial model for type: ", model_type)
            flush.console()
            model_results <- fit_and_predict(patt_src, hist_src, l_period, nys, DOM)
            message("[DEBUG] fit_and_predict returned: ", paste(names(model_results), collapse = ", "))
            flush.console()
        }

        # Skip if model failed
        if (length(model_results) == 0 || is.data.frame(model_results)) {
            message("Model failed for this pattern")
            flush.console()
            next
        }

        # Update output with model results
        year_predict <- sort(unique(out_spline$YEAR))
        out_spline <- update_output(out_spline, model_results, j, pattern, l_period,
                                   patt_src, hist_src, year_predict)
    }

    message("Total processing time: ", round(difftime(Sys.time(), start_time, units = "mins"), 1), " minutes")
    flush.console()

    return(out_spline)
}

# Helper function: serial processing for Negative Binomial
process_serial <- function(n_pat, patterns, src, l_period, YRS2020, nys, DOM, out_spline, wm_ident) {
    message("Using serial processing...")
    flush.console()
    start_time <- Sys.time()

    # Initialize results list
    results <- list()

    # Process each pattern
    for (j in 1:n_pat) {
        pattern <- patterns[j]
        message("Processing pattern ", j, "/", n_pat, ": ", pattern)

        # Extract pattern data
        pattern_parts <- strsplit(pattern, " ")[[1]]
        patt_src <- src[
            src$SEX == pattern_parts[1] &
            src$AGE_GROUP == pattern_parts[2] &
            src$AREA == pattern_parts[3] &
            src$CAUSE == pattern_parts[4],
        ]

        # Skip if insufficient data
        if (nrow(patt_src) < 10) {
            message("Skipping pattern due to insufficient data")
            next
        }

        # Prepare historical data
        hist_src <- patt_src[patt_src$event_index == "0", ]

        # Skip if insufficient historical data
        if (nrow(hist_src) < 10) {
            message("Skipping pattern due to insufficient historical data")
            next
        }

        # Model fitting and prediction
        model_results <- fit_and_predict(patt_src, hist_src, l_period, YRS2020, nys, DOM)

        # Skip if model failed
        if (length(model_results) == 0 || is.data.frame(model_results)) {
            message("Model failed for this pattern")
            next
        }

        # Update output with model results
        year_predict <- sort(unique(out_spline$YEAR))
        result <- update_output(out_spline, model_results, j, pattern, l_period,
                               patt_src, hist_src, year_predict)

        # Store result
        if (nrow(result) > 0) {
            results[[j]] <- result
            message("Successfully processed pattern: ", pattern, " (", nrow(result), " rows)")
        }
    }

    # Combine results
    if (length(results) > 0) {
        out_spline <- do.call(rbind, results[!sapply(results, is.null)])
        message("Successfully combined results from ", length(results[!sapply(results, is.null)]), " patterns")
    } else {
        warning("No successfully processed patterns!")
        return(NULL)
    }

    end_time <- Sys.time()
    total_time <- difftime(end_time, start_time, units = "mins")
    message("Serial processing completed, total time: ", round(total_time, 2), " minutes")

    # 使用公共函数处理结果
    out_spline <- process_model_results(out_spline, "Negative Binomial")

    message("\n[fcn.spline] Serial processing completed")
    flush.console()
    return(out_spline)
}

# 简化主函数，移除并行处理相关代码
fcn.spline <- function(src, model_type = "Negative Binomial") {
    message("\n[fcn.spline] Starting calculation with model type: ", model_type, "...")
    flush.console()

    # Preprocess data
    src <- src %>%
        filter(PERIOD <= 52) %>%
        arrange(SEX, AGE_GROUP, AREA, CAUSE, YEAR, PERIOD) %>%
        mutate(NO_DEATHS = as.numeric(NO_DEATHS))

    nys <- length(unique(src$YEAR))
    max_period <- max(src$PERIOD, na.rm = TRUE)
    wm_ident <- ifelse(max_period == 12, "Month", "Week")
    l_period <- ifelse(max_period == 12, 12, 52)

    # Calculate dates
    src <- calculate_dates(src, max_period, nys, DOM, MOY)

    # Initialize output data frame
    out_spline <- initialize_output(src, YRS2020, wm_ident, l_period)

    # Get unique patterns
    patterns <- src %>%
        select(SEX, AGE_GROUP, AREA, CAUSE) %>%
        distinct() %>%
        mutate(patterns = paste(SEX, AGE_GROUP, AREA, CAUSE)) %>%
        pull(patterns)
    n_pat <- length(patterns)

    # Choose processing method based on model type
    message("[DEBUG] Processing with model_type: ", model_type)
    flush.console()

    if (model_type == "poisson") {
        # Use Poisson model
        message("[DEBUG] Using Poisson model processing")
        flush.console()
        out_spline <- process_serial_with_model(n_pat, patterns, src, l_period, YRS2020, nys, DOM, out_spline, wm_ident, model_type)
    } else if (model_type == "zip") {
        # Use Zero-Inflated Poisson model
        message("[DEBUG] Using Zero-Inflated Poisson model processing")
        flush.console()
        out_spline <- process_serial_with_model(n_pat, patterns, src, l_period, YRS2020, nys, DOM, out_spline, wm_ident, model_type)
    } else {
        # Default: Use Negative Binomial model
        message("[DEBUG] Using Negative Binomial model processing")
        flush.console()
        out_spline <- process_serial(n_pat, patterns, src, l_period, YRS2020, nys, DOM, out_spline, wm_ident)
    }

    return(out_spline)
}
