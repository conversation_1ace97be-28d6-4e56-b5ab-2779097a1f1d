# P-Score 图表函数
# 负责绘制 P-Score 图表

# 加载必要的库
library(ggplot2)
library(dplyr)

# 加载其他绘图函数
source("modules/plot_functions/plot_utils.R")
source("modules/plot_functions/plot_event_annotations.R")

# 绘制 P-Score 图表
plot_p_score <- function(data, title = "P-Score", subtitle = NULL) {
  # 检查数据是否有效
  if (is.null(data) || nrow(data) == 0) {
    return(ggplot() + 
             theme_minimal() + 
             labs(title = "No data available"))
  }
  
  # 提取事件数据
  event_data <- extract_event_data(data)
  
  # 计算y轴范围
  y_min <- min(0, min(data$P_SCORE, na.rm = TRUE))
  y_max <- max(data$P_SCORE, na.rm = TRUE) * 1.1
  
  # 创建基础图层
  p <- ggplot(data, aes(x = TimePoint, y = P_SCORE)) +
    # 添加主图层
    geom_col(aes(fill = Model), position = "dodge", width = 30, alpha = 0.7) +
    {
      if ("P_LOWER" %in% names(data) && "P_UPPER" %in% names(data)) {
        geom_errorbar(aes(ymin = P_LOWER, ymax = P_UPPER),
                     position = position_dodge(30),
                     width = 5, color = "gray40")
      }
    }
  
  # 添加事件标注
  p <- add_event_annotations(p, event_data, y_min, y_max)
  
  # 添加颜色映射
  p <- p +
    scale_fill_manual(
      name = "Model",
      values = get_model_colors()
    )
  
  # 添加通用的图表元素
  p <- add_common_plot_elements(p, 
                               x_lab = "Date", 
                               y_lab = "P-Score (%)", 
                               title = title, 
                               subtitle = subtitle)
  
  # 格式化坐标轴
  p <- format_date_axis(p)
  p <- format_numeric_axis(p, y_min, y_max)
  
  return(p)
}
