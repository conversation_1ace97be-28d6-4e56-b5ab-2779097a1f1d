# 超额死亡率图表函数
# 负责绘制超额死亡率图表

# 加载必要的库
library(ggplot2)
library(dplyr)

# 加载其他绘图函数
source("modules/plot_functions/plot_utils.R")
source("modules/plot_functions/plot_event_annotations.R")

# 绘制超额死亡率图表
plot_excess_deaths <- function(data, title = "Excess Deaths", subtitle = NULL) {
  # 检查数据是否有效
  if (is.null(data) || nrow(data) == 0) {
    return(ggplot() +
      theme_minimal() +
      labs(title = "No data available"))
  }

  # 提取事件数据
  event_data <- extract_event_data(data)

  # 计算y轴范围
  y_min <- min(0, min(data$EXCESS_DEATHS, na.rm = TRUE))
  y_max <- max(data$EXCESS_DEATHS, na.rm = TRUE) * 1.1

  # 创建基础图层
  p <- ggplot(data, aes(x = TimePoint, y = EXCESS_DEATHS))

  # 添加事件标注
  p <- p + add_event_annotations(p, event_data, y_min, y_max)

  # 添加主图层
  p <- p +
    geom_line(aes(color = Model), linewidth = 1) + {
      if ("EXCESS_LOWER" %in% names(data) && "EXCESS_UPPER" %in% names(data)) {
        geom_ribbon(aes(ymin = EXCESS_LOWER, ymax = EXCESS_UPPER, fill = Model), alpha = 0.2, color = NA)
      }
    }


  # 添加颜色映射
  p <- p +
    scale_color_manual(
      name = "Model",
      values = get_model_colors()
    ) +
    scale_fill_manual(
      name = "95% CI",
      values = get_model_colors()
    )

  # 添加通用的图表元素
  p <- add_common_plot_elements(p,
    x_lab = "Date",
    y_lab = "Excess Deaths",
    title = title,
    subtitle = subtitle
  )

  # 格式化坐标轴
  p <- format_date_axis(p)
  p <- format_numeric_axis(p, y_min, y_max)

  return(p)
}
