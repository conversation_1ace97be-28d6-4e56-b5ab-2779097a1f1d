# Load necessary R packages
library(shiny)
library(dplyr)
library(DT)
library(mgcv)
library(openxlsx)
library(reshape2)
library(readxl)
library(stringr)
library(shinyjs) # 添加shinyjs库用于进度提示功能


# # Assume dataset rv_data is loaded into the environment
# rv_data <- readRDS(file.path(data_dir, "merged_data.rds"))
# rv_data$CAUSE <- "Total"
# rv_data$AREA <- "Total" # rv_data$AREA
# rv_data$COUNTRY <- "AAA"
# rv_data$ISO3 <- "ISO"
# rv_data$SE_IDENTIFIER <- "END"


# # Get unique values for filter variables
# unique_sex <- unique(rv_data$SEX)
# unique_age_group <- unique(rv_data$AGE_GROUP)
# unique_area <- unique(rv_data$AREA)
# unique_cause <- unique(rv_data$CAUSE)


# # 1. Extract unique combinations of YEAR and PERIOD
# unique_combinations <- rv_data %>%
#   filter(PERIOD <= 52) %>%
#   select(YEAR, PERIOD) %>%
#   distinct()

# # Save current locale setting
# original_locale <- Sys.getlocale("LC_TIME")
# # Change locale setting to English
# Sys.setlocale("LC_TIME", "en_US.UTF-8")

# # 2. Batch calculate DATE_TO_SPECIFY_WEEK
# unique_combinations$DATE_TO_SPECIFY_WEEK <- apply(unique_combinations, 1, function(row) {
#   target_date <- get_week_start_date(as.numeric(row["YEAR"]), as.numeric(row["PERIOD"]))
#   format(target_date, "%b-%d")
# })

# # Restore original locale setting
# Sys.setlocale("LC_TIME", original_locale)


# # 3. Merge results back to original dataframe
# rv_data <- rv_data %>%
#   filter(PERIOD <= 52) %>%
#   left_join(unique_combinations, by = c("YEAR", "PERIOD"))

# Define UI interface
# 修改UI函数，使用默认值并允许动态更新
model_module_ui <- function(id) {
  ns <- NS(id)
  fluidPage(
    useShinyjs(),
    # Add necessary CSS
    tags$head(
      tags$style(HTML("
            .shiny-output-error { visibility: hidden; }
            .shiny-output-error:before { visibility: hidden; }

            /* 进度条样式 */
            .progress-container {
                margin-top: 10px;
                margin-bottom: 10px;
            }
            .progress {
                height: 20px;
            }
            .progress-bar {
                transition: width 0.3s ease;
            }
            #progress-message {
                margin-top: 5px;
                font-weight: bold;
            }

            /* 表格下载按钮样式 */
            .dt-buttons {
                margin-bottom: 10px;
            }

            /* 数据未就绪提示样式 */
            .data-not-ready {
                text-align: center;
                padding: 50px;
                color: #666;
                background: #f9f9f9;
                border-radius: 5px;
                margin: 20px;
            }
        "))
    ),

    # 添加数据检查条件面板
    conditionalPanel(
      condition = "!output.has_data",
      ns = ns,
      div(
        class = "data-not-ready",
        style = "text-align: center; margin-top: 50px;",
        h3("Data is not ready. Please process data first...", style = "color: #666;"),
        br(),
        actionButton(ns("goto_data_tab"), "Go to Data tab",
          style = "margin-top: 20px; background-color: #337ab7; color: white;"
        )
      )
    ),

    # 原有的UI内容放在条件面板中
    conditionalPanel(
      condition = "output.has_data",
      ns = ns,
      fluidRow(
        # Left side for table content
        column(
          9,
          tabsetPanel(
            tabPanel(
              "Total Prediction Results",
              br(),
              wellPanel(
                # 添加下载按钮
                DT::dataTableOutput(ns("total_predictions")),
                br(), br(),
                downloadButton(ns("downloadTotalPredictions"), "Download Full Data")
              )
            ),
            tabPanel(
              "Summary by Event",
              br(),
              wellPanel(
                # 添加下载按钮
                DT::dataTableOutput(ns("summary_by_event")),
                br(), br(),
                downloadButton(ns("downloadSummaryByEvent"), "Download Full Data")
              )
            ),
            tabPanel(
              "Overall Summary",
              br(),
              wellPanel(
                # 添加下载按钮
                DT::dataTableOutput(ns("overall_summary")),
                br(), br(),
                downloadButton(ns("downloadOverallSummary"), "Download Full Data")
              )
            )
          )
        ),
        # Right side for input controls
        column(
          3,
          tabsetPanel(
            tabPanel(
              "Data & Model",
              # Filter controls
              h4("Data Filtering"),
              uiOutput(ns("sex_selector")),
              uiOutput(ns("age_group_selector")),
              # Model method selection
              h4("Model Selection"),
              checkboxGroupInput(ns("model_method"), "Select Model Methods:",
                choices = c("Historical Average", "Negative Binomial Regression"),
                selected = c("Historical Average", "Negative Binomial Regression")
              ),
              # Run button
              br(),
              actionButton(ns("run"), "Run Model", class = "btn-primary btn-block"),

              # 在Run Model按钮下面添加进度条
              br(),
              hidden(
                div(
                  id = ns("progress-area"), class = "progress-container",
                  div(
                    class = "progress",
                    div(
                      id = ns("progress-bar"), class = "progress-bar progress-bar-striped active",
                      role = "progressbar", style = "width: 0%", `aria-valuenow` = "0",
                      `aria-valuemin` = "0", `aria-valuemax` = "100"
                    )
                  ),
                  div(
                    id = ns("progress-message"), class = "text-center",
                    textOutput(ns("progress_text"))
                  )
                )
              )
            )
          )
        )
      )
    )
  )
}

# Define server logic
model_module_server <- function(id, rv) {
  moduleServer(
    id,
    function(input, output, session) {
      # 添加数据状态检查
      output$has_data <- reactive({
        !is.null(rv$merged_data)
      })
      outputOptions(output, "has_data", suspendWhenHidden = FALSE)

      # 加载必要的源文件
      source("modules/utils.R")
      source("modules/ACM_hist.R")
      source("modules/ACM_spline.R")

      # 创建存储处理后数据的响应式值
      processed_data <- reactiveValues(
        total_predictions = NULL,
        summary_by_event = NULL,
        overall_summary = NULL
      )

      # 创建响应式表达式获取数据
      rv_data <- reactive({
        req(rv$merged_data_ext)
        return(rv$merged_data_ext)
      })

      # 获取唯一值用于过滤
      unique_sex <- reactive({
        req(rv_data())
        unique(rv_data()$SEX)
      })

      unique_age_group <- reactive({
        req(rv_data())
        unique(rv_data()$AGE_GROUP)
      })

      # 动态生成UI选择器
      output$sex_selector <- renderUI({
        req(unique_sex())
        choices <- unique_sex()
        selected <- if ("Total" %in% choices) "Total" else choices
        checkboxGroupInput(session$ns("sex"), "Select Sex:",
          choices = choices,
          selected = selected
        )
      })

      output$age_group_selector <- renderUI({
        req(unique_age_group())
        choices <- unique_age_group()
        selected <- if ("Total" %in% choices) "Total" else choices
        checkboxGroupInput(session$ns("age_group"), "Select Age Group:",
          choices = choices,
          selected = selected
        )
      })

      # 过滤数据
      filtered_data <- reactive({
        req(rv_data(), input$sex, input$age_group)
        rv_data() %>%
          filter(
            SEX %in% input$sex,
            AGE_GROUP %in% input$age_group
          )
      })

      # 创建进度值和文本的响应式值
      progress_value <- reactiveVal(0)
      progress_message <- reactiveVal("")

      # 输出进度文本
      output$progress_text <- renderText({
        progress_message()
      })

      # 更新进度条的函数
      updateProgress <- function(value, message) {
        progress_value(value)
        progress_message(message)
        shinyjs::runjs(sprintf(
          "$('#%s').css('width', '%s%%').attr('aria-valuenow', %s);",
          session$ns("progress-bar"), value, value
        ))
      }

      # 模型结果计算
      model_results <- eventReactive(input$run, {
        # 保存数据用于调试
        tryCatch(
          {
            if (!is.null(rv$merged_data_ext) && is.data.frame(rv$merged_data_ext)) {
              write.csv(rv$merged_data_ext, "merged_data_ext.csv", row.names = FALSE)
            }
          },
          error = function(e) {
            message("Error saving data: ", e$message)
          }
        )

        # 显示进度区域
        shinyjs::show(id = "progress-area")
        # 禁用运行按钮
        shinyjs::disable("run")

        # 初始化进度
        updateProgress(10, "Preparing data...")
        invalidateLater(100)
        Sys.sleep(0.5)

        # 获取过滤后的数据
        req(filtered_data())
        data <- filtered_data()

        # 保存过滤后的数据用于调试
        tryCatch(
          {
            write.csv(data, "filtered_data_debug.csv", row.names = FALSE)
          },
          error = function(e) {
            message("Error saving filtered data: ", e$message)
          }
        )

        results <- list()

        # 如果选择了Historical Average
        if ("Historical Average" %in% input$model_method) {
          updateProgress(30, "Running Historical Average model...")
          invalidateLater(100)
          Sys.sleep(0.2)
          results$hist <- fcn_hist(data)
          updateProgress(60, "Historical Average model completed")
          invalidateLater(100)
          Sys.sleep(0.2)
        }

        # 如果选择了Negative Binomial Regression
        if ("Negative Binomial Regression" %in% input$model_method) {
          updateProgress(70, "Running Negative Binomial Regression model...")
          invalidateLater(100)
          Sys.sleep(0.2)
          results$spline <- fcn.spline(data, parallel = FALSE)
          updateProgress(90, "Negative Binomial Regression model completed")
          invalidateLater(100)
          Sys.sleep(0.2)
        }

        # 完成所有计算
        updateProgress(95, "Processing results...")
        invalidateLater(100)
        Sys.sleep(0.2)

        # 处理 total_predictions 数据
        combined_results <- NULL
        if (!is.null(results$hist) && !is.null(results$spline)) {
          hist_df <- results$hist %>% mutate(Model = "Historical Average")
          spline_df <- results$spline %>% mutate(Model = "Negative Binomial Regression")
          combined_results <- bind_rows(hist_df, spline_df)
        } else if (!is.null(results$hist)) {
          combined_results <- results$hist %>% mutate(Model = "Historical Average")
        } else if (!is.null(results$spline)) {
          combined_results <- results$spline %>% mutate(Model = "Negative Binomial Regression")
        }

        if (!is.null(combined_results)) {
          # 格式化数值列
          numeric_cols <- c("NO_DEATHS", "EXP_DEATHS", "LOWER_LIMIT", "UPPER_LIMIT", "EXCESS_DEATHS")
          combined_results <- combined_results %>%
            mutate(across(all_of(numeric_cols), ~ round(as.numeric(.)))) %>%
            # 移除指定的变量
            select(-c(COUNTRY, ISO3, AREA, CAUSE, DATE_TO_SPECIFY_WEEK, SE_IDENTIFIER))

          # 保存到响应式值
          processed_data$total_predictions <- combined_results

          # 保存到全局响应式值
          rv$total_prediction <- combined_results
        }

        # 处理 summary_by_event 数据
        combined_summary <- NULL
        if (!is.null(results$hist) && !is.null(results$spline)) {
          hist_summary <- attr(results$hist, "summary_by_event") %>% mutate(Model = "Historical Average")
          spline_summary <- attr(results$spline, "summary_by_event") %>% mutate(Model = "Negative Binomial Regression")
          combined_summary <- bind_rows(hist_summary, spline_summary)
        } else if (!is.null(results$hist)) {
          combined_summary <- attr(results$hist, "summary_by_event") %>% mutate(Model = "Historical Average")
        } else if (!is.null(results$spline)) {
          combined_summary <- attr(results$spline, "summary_by_event") %>% mutate(Model = "Negative Binomial Regression")
        }

        if (!is.null(combined_summary)) {
          # 格式化数值列
          integer_cols <- c("TOTAL_DEATHS", "TOTAL_EXPECTED", "TOTAL_EXCESS", "TOTAL_SE", "EXCESS_LOWER", "EXCESS_UPPER")
          decimal_cols <- c("P_SCORE")

          combined_summary <- combined_summary %>%
            mutate(
              across(all_of(integer_cols), ~ round(as.numeric(.))),
              across(all_of(decimal_cols), ~ round(as.numeric(.), 2))
            ) %>%
            # 移除指定的变量
            select(-c(COUNTRY, ISO3, AREA, CAUSE))

          # 保存到响应式值
          processed_data$summary_by_event <- combined_summary

          # 保存到全局响应式值
          rv$summary_by_event <- combined_summary
        }

        # 处理 overall_summary 数据
        combined_overall <- NULL
        if (!is.null(results$hist) && !is.null(results$spline)) {
          hist_overall <- attr(results$hist, "overall_summary") %>% mutate(Model = "Historical Average")
          spline_overall <- attr(results$spline, "overall_summary") %>% mutate(Model = "Negative Binomial Regression")
          combined_overall <- bind_rows(hist_overall, spline_overall)
        } else if (!is.null(results$hist)) {
          combined_overall <- attr(results$hist, "overall_summary") %>% mutate(Model = "Historical Average")
        } else if (!is.null(results$spline)) {
          combined_overall <- attr(results$spline, "overall_summary") %>% mutate(Model = "Negative Binomial Regression")
        }

        if (!is.null(combined_overall)) {
          # 格式化数值列
          integer_cols <- c("TOTAL_DEATHS", "TOTAL_EXPECTED", "TOTAL_EXCESS", "TOTAL_SE", "EXCESS_LOWER", "EXCESS_UPPER")
          decimal_cols <- c("P_SCORE")

          combined_overall <- combined_overall %>%
            mutate(
              across(all_of(integer_cols), ~ round(as.numeric(.))),
              across(all_of(decimal_cols), ~ round(as.numeric(.), 2))
            ) %>%
            # 移除指定的变量
            select(-c(COUNTRY, ISO3, AREA, CAUSE))

          # 保存到响应式值
          processed_data$overall_summary <- combined_overall

          # 保存到全局响应式值
          rv$overall_summary <- combined_overall
        }

        updateProgress(100, "All calculations completed!")

        # 延迟一段时间后隐藏进度条并重新启用按钮
        shinyjs::delay(1500, {
          shinyjs::hide("progress-area")
          shinyjs::enable("run")
        })

        return(results)
      })

      # 表格输出
      output$total_predictions <- DT::renderDataTable({
        model_results() # 触发计算

        if (!is.null(processed_data$total_predictions)) {
          DT::datatable(processed_data$total_predictions,
            options = list(
              pageLength = 10,
              autoWidth = TRUE,
              width = "100%",
              scrollX = TRUE,
              scrollCollapse = TRUE,
              dom = "Blfrtip",
              buttons = list(
                list(extend = "copy", text = "Copy")
              ),
              # Move columnDefs inside the options list
              columnDefs = list(
                list(
                  targets = ncol(processed_data$total_predictions) - 1,
                  width = "200px"
                ),
                list(
                  targets = ncol(processed_data$total_predictions) - 0,
                  width = "200px"
                )
              )
            ),
            extensions = c("Buttons")
          )
        }
      })

      output$summary_by_event <- DT::renderDataTable({
        model_results() # 触发计算

        if (!is.null(processed_data$summary_by_event)) {
          DT::datatable(processed_data$summary_by_event,
            options = list(
              pageLength = 10,
              autoWidth = TRUE,
              width = "100%",
              scrollX = TRUE,
              scrollCollapse = TRUE,
              dom = "Blfrtip",
              buttons = list(
                list(extend = "copy", text = "Copy")
              ),
              # Move columnDefs inside the options list
              columnDefs = list(
                list(
                  targets = ncol(processed_data$summary_by_event) - 1,
                  width = "150px"
                ),
                list(
                  targets = ncol(processed_data$summary_by_event) - 0,
                  width = "200px"
                )
              )
            ),
            extensions = c("Buttons")
          )
        }
      })

      output$overall_summary <- DT::renderDataTable({
        model_results() # 触发计算

        if (!is.null(processed_data$overall_summary)) {
          DT::datatable(processed_data$overall_summary,
            options = list(
              pageLength = 10,
              autoWidth = TRUE,
              width = "100%",
              scrollX = TRUE,
              scrollCollapse = TRUE,
              dom = "Blfrtip",
              buttons = list(
                list(extend = "copy", text = "Copy")
              ),
              # Move columnDefs inside the options list
              columnDefs = list(
                list(
                  targets = ncol(processed_data$overall_summary) - 1,
                  width = "150px"
                ),
                list(
                  targets = ncol(processed_data$overall_summary) - 0,
                  width = "200px"
                )
              )
            ),
            extensions = c("Buttons")
          )
        }
      })

      # 添加下载处理器 - 修改为使用Excel格式
      output$downloadTotalPredictions <- downloadHandler(
        filename = function() {
          paste("Total_Predictions_", format(Sys.Date(), "%Y-%m-%d"), ".xlsx", sep = "")
        },
        content = function(file) {
          req(processed_data$total_predictions)
          openxlsx::write.xlsx(processed_data$total_predictions, file, rowNames = FALSE)
        }
      )

      output$downloadSummaryByEvent <- downloadHandler(
        filename = function() {
          paste("Summary_By_Event_", format(Sys.Date(), "%Y-%m-%d"), ".xlsx", sep = "")
        },
        content = function(file) {
          req(processed_data$summary_by_event)
          openxlsx::write.xlsx(processed_data$summary_by_event, file, rowNames = FALSE)
        }
      )

      output$downloadOverallSummary <- downloadHandler(
        filename = function() {
          paste("Overall_Summary_", format(Sys.Date(), "%Y-%m-%d"), ".xlsx", sep = "")
        },
        content = function(file) {
          req(processed_data$overall_summary)
          openxlsx::write.xlsx(processed_data$overall_summary, file, rowNames = FALSE)
        }
      )

      # 在 moduleServer 函数内部添加导航处理器
      observeEvent(input$goto_data_tab, {
        session$sendCustomMessage("navigateTab", "Data")
      })

      # Return the reactive values for use in other modules if needed
      return(list(
        processed_data = processed_data,
        model_results = model_results
      ))
    }
  )
}
