# 模型函数文件
# 包含四个独立的模型函数，每个函数负责一个模型的处理

# 加载公共函数
source("modules/common_functions.R")

# 1. Historical Average 模型函数
run_historical_average <- function(data) {
  message("\n[INFO] Running Historical Average model...")

  # 运行模型
  result <- tryCatch({
    source("modules/ACM_hist_new.R", local = TRUE)
    fcn_hist(data)
  }, error = function(e) {
    message("Error in Historical Average model: ", e$message)
    NULL
  })

  # 检查结果
  if (!is.null(result)) {
    message("[INFO] Historical Average model completed successfully")
  } else {
    message("[INFO] Historical Average model failed")
  }

  return(result)
}

# 2. Negative Binomial Regression 模型函数
run_negative_binomial <- function(data, save_intermediate = TRUE) {
  message("\n[INFO] Running Negative Binomial Regression model...")

  # 运行模型
  result <- tryCatch({
    message("[DEBUG] Calling fcn_nb")
    flush.console()
    source("modules/ACM_nb.R", local = TRUE)
    model_result <- fcn_nb(data)

    # 检查结果
    if (!is.null(model_result)) {
      message("[DEBUG] Negative Binomial Regression completed successfully")
      message("[DEBUG] Result dimensions: ", nrow(model_result), " x ", ncol(model_result))
      message("[DEBUG] Result columns: ", paste(names(model_result), collapse = ", "))
      message("[DEBUG] Has summary_by_event: ", !is.null(attr(model_result, "summary_by_event")))
      message("[DEBUG] Has overall_summary: ", !is.null(attr(model_result, "overall_summary")))

      # 保存中间数据用于测试
      if (save_intermediate) {
        message("[DEBUG] Saving intermediate data for testing...")
        tryCatch({
          # 保存原始数据
          saveRDS(data, "nb_input_data.rds")
          # 保存模型结果
          saveRDS(model_result, "nb_model_result.rds")
          # 保存模型属性
          if (!is.null(attr(model_result, "summary_by_event"))) {
            saveRDS(attr(model_result, "summary_by_event"), "nb_summary_by_event.rds")
          }
          if (!is.null(attr(model_result, "overall_summary"))) {
            saveRDS(attr(model_result, "overall_summary"), "nb_overall_summary.rds")
          }
          message("[DEBUG] Intermediate data saved successfully")
        }, error = function(e) {
          message("[DEBUG] Error saving intermediate data: ", e$message)
        })
      }
    } else {
      message("[DEBUG] Negative Binomial Regression returned NULL")
    }
    flush.console()

    model_result
  }, error = function(e) {
    message("Error in Negative Binomial Regression model: ", e$message)
    NULL
  })

  return(result)
}

# 3. General Poisson Model 函数
run_general_poisson <- function(data) {
  message("\n[INFO] Running General Poisson Model...")

  # 检查数据是否有效
  message("[DEBUG] Data dimensions: ", nrow(data), " x ", ncol(data))
  message("[DEBUG] Data columns: ", paste(names(data), collapse = ", "))
  flush.console()

  # 运行模型
  result <- tryCatch({
    message("[DEBUG] Calling fcn_poisson")
    flush.console()
    source("modules/ACM_poisson.R", local = TRUE)
    model_result <- fcn_poisson(data)

    # 检查结果
    if (!is.null(model_result)) {
      message("[DEBUG] General Poisson Model completed successfully")
      message("[DEBUG] Result dimensions: ", nrow(model_result), " x ", ncol(model_result))
      message("[DEBUG] Result columns: ", paste(names(model_result), collapse = ", "))
      message("[DEBUG] Has summary_by_event: ", !is.null(attr(model_result, "summary_by_event")))
      message("[DEBUG] Has overall_summary: ", !is.null(attr(model_result, "overall_summary")))
    } else {
      message("[DEBUG] General Poisson Model returned NULL")
    }
    flush.console()

    model_result
  }, error = function(e) {
    message("Error in General Poisson Model: ", e$message)
    NULL
  })

  return(result)
}

# 4. Zero Inflated Poisson Model 函数
run_zero_inflated_poisson <- function(data) {
  message("\n[INFO] Running Zero Inflated Poisson Model...")

  # 检查数据是否有效
  message("[DEBUG] Data dimensions: ", nrow(data), " x ", ncol(data))
  message("[DEBUG] Data columns: ", paste(names(data), collapse = ", "))
  flush.console()

  # 运行模型
  result <- tryCatch({
    message("[DEBUG] Calling fcn_zip")
    flush.console()
    source("modules/ACM_zip.R", local = TRUE)
    model_result <- fcn_zip(data)

    # 检查结果
    if (!is.null(model_result)) {
      message("[DEBUG] Zero Inflated Poisson Model completed successfully")
      message("[DEBUG] Result dimensions: ", nrow(model_result), " x ", ncol(model_result))
      message("[DEBUG] Result columns: ", paste(names(model_result), collapse = ", "))
      message("[DEBUG] Has summary_by_event: ", !is.null(attr(model_result, "summary_by_event")))
      message("[DEBUG] Has overall_summary: ", !is.null(attr(model_result, "overall_summary")))
    } else {
      message("[DEBUG] Zero Inflated Poisson Model returned NULL")
    }
    flush.console()

    model_result
  }, error = function(e) {
    message("Error in Zero Inflated Poisson Model: ", e$message)
    NULL
  })

  return(result)
}

# 5. 处理模型结果函数
process_model_results <- function(results, model_methods) {
  # 打印调试信息
  message("\n[DEBUG] Results summary:")
  message("Historical Average: ", !is.null(results$hist))
  message("Negative Binomial: ", !is.null(results$spline))
  message("General Poisson: ", !is.null(results$poisson))
  message("Zero Inflated Poisson: ", !is.null(results$zip))

  # 检查 Poisson 模型结果
  if (!is.null(results$poisson)) {
    message("Poisson result class: ", class(results$poisson))
    message("Poisson result dimensions: ", nrow(results$poisson), " x ", ncol(results$poisson))
    message("Poisson has summary_by_event: ", !is.null(attr(results$poisson, "summary_by_event")))
    message("Poisson has overall_summary: ", !is.null(attr(results$poisson, "overall_summary")))
  }

  # 检查 ZIP 模型结果
  if (!is.null(results$zip)) {
    message("ZIP result class: ", class(results$zip))
    message("ZIP result dimensions: ", nrow(results$zip), " x ", ncol(results$zip))
    message("ZIP has summary_by_event: ", !is.null(attr(results$zip, "summary_by_event")))
    message("ZIP has overall_summary: ", !is.null(attr(results$zip, "overall_summary")))
  }

  # 处理 total_predictions 数据
  combined_results <- NULL

  # 处理每个选定的模型
  model_map <- c(
    "Historical Average" = "hist",
    "Negative Binomial Regression" = "nb",
    "General Poisson Model" = "poisson",
    "Zero Inflated Poisson Model" = "zip"
  )

  for (method in model_methods) {
    result_key <- switch(method,
      "Historical Average" = "hist",
      "Negative Binomial Regression" = "spline",
      "General Poisson Model" = "poisson",
      "Zero Inflated Poisson Model" = "zip"
    )

    if (!is.null(result_key) && !is.null(results[[result_key]])) {
      current_results <- mutate(results[[result_key]], Model = method)
      combined_results <- bind_rows(combined_results, current_results)
    }
  }

  if (!is.null(combined_results)) {
    # 格式化数值列
    numeric_cols <- c("NO_DEATHS", "EXP_DEATHS", "LOWER_LIMIT", "UPPER_LIMIT", "EXCESS_DEATHS")
    combined_results <- combined_results %>%
      mutate(across(all_of(numeric_cols), ~ round(as.numeric(.)))) %>%
      # 移除指定的变量
      select(-c(COUNTRY, ISO3, AREA, CAUSE, DATE_TO_SPECIFY_WEEK, SE_IDENTIFIER))
  }

  # 处理 summary_by_event
  combined_summary <- bind_rows(
    if (!is.null(results$hist)) mutate(attr(results$hist, "summary_by_event"), Model = "Historical Average") else NULL,
    if (!is.null(results$spline)) mutate(attr(results$spline, "summary_by_event"), Model = "Negative Binomial Regression") else NULL,
    if (!is.null(results$poisson)) mutate(attr(results$poisson, "summary_by_event"), Model = "General Poisson Model") else NULL,
    if (!is.null(results$zip)) mutate(attr(results$zip, "summary_by_event"), Model = "Zero Inflated Poisson Model") else NULL
  )

  # 处理 overall_summary
  combined_overall <- bind_rows(
    if (!is.null(results$hist)) mutate(attr(results$hist, "overall_summary"), Model = "Historical Average") else NULL,
    if (!is.null(results$spline)) mutate(attr(results$spline, "overall_summary"), Model = "Negative Binomial Regression") else NULL,
    if (!is.null(results$poisson)) mutate(attr(results$poisson, "overall_summary"), Model = "General Poisson Model") else NULL,
    if (!is.null(results$zip)) mutate(attr(results$zip, "overall_summary"), Model = "Zero Inflated Poisson Model") else NULL
  )

  return(list(
    total_prediction = combined_results,
    summary_by_event = combined_summary,
    overall_summary = combined_overall
  ))
}
